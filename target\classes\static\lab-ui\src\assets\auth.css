* {
  padding: 0;
  margin: 0;
  box-sizing: border-box;
}

body {
  background: linear-gradient(#f8f9fa, #e9ecef);
  margin: 0;
  overflow: auto;
}

.auth-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100vh;
  width: 100vw;
  background: linear-gradient(#f8f9fa, #e9ecef);
  overflow: auto;
  padding-top: 0;
  position: relative;
}

.cyberpunk-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: url('https://images.unsplash.com/photo-1483478550801-ceba5fe50e8e?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80');
  background-size: cover;
  background-position: center;
  filter: brightness(1.3) saturate(0.8) hue-rotate(5deg);
  z-index: 1;
  animation: backgroundScale 20s infinite alternate ease-in-out;
}

.cyberpunk-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.8) 0%, rgba(245, 245, 250, 0.7) 100%);
  z-index: 2;
}

@keyframes backgroundScale {
  0% { transform: scale(1); }
  100% { transform: scale(1.1); }
}

.lab-title {
  font-size: 38px;
  font-weight: bold;
  color: #05d9e8;
  text-align: center;
  margin-bottom: 30px;
  letter-spacing: 4px;
  text-shadow: 0 0 10px rgba(5, 217, 232, 0.5), 0 0 20px rgba(5, 217, 232, 0.3);
  position: relative;
  border: 2px solid rgba(5, 217, 232, 0.3);
  padding: 15px 40px;
  border-radius: 8px;
  background-color: rgba(255, 255, 255, 0.85);
  box-shadow: 0 5px 25px rgba(5, 217, 232, 0.2);
  animation: titlePulse 3s infinite alternate;
  z-index: 10;
}

@keyframes titlePulse {
  0% {
    box-shadow: 0 5px 25px rgba(5, 217, 232, 0.2);
    text-shadow: 0 0 10px rgba(5, 217, 232, 0.5), 0 0 20px rgba(5, 217, 232, 0.3);
  }
  100% {
    box-shadow: 0 5px 30px rgba(5, 217, 232, 0.4);
    text-shadow: 0 0 15px rgba(5, 217, 232, 0.7), 0 0 25px rgba(5, 217, 232, 0.5);
  }
}

.auth-form-container {
  position: relative;
  width: 450px;
  overflow: hidden;
  padding: 0;
  display: flex;
  justify-content: center;
  margin: 0 auto;
  z-index: 10;
  border-radius: 8px;
}

@keyframes glowing {
  0% { background-position: 0 0; }
  50% { background-position: 400% 0; }
  100% { background-position: 0 0; }
}

.auth-form {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  max-width: 430px;
  height: 450px;
  padding: 40px;
  background-color: rgba(255, 255, 255, 0.95);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.08);
  border-radius: 8px;
  z-index: 1;
  backdrop-filter: blur(5px);
  border: none;
}

/* 特别为注册页面调整表单高度 */
.register-page .auth-form {
  height: 480px;
}

.auth-title {
  color: #05d9e8;
  margin-bottom: 30px;
  font-size: 28px;
  text-shadow: 0 0 10px rgba(5, 217, 232, 0.3);
  font-weight: 600;
  letter-spacing: 1px;
  position: relative;
  padding-bottom: 10px;
}

.auth-title::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 50px;
  height: 2px;
  background: linear-gradient(90deg, transparent, #05d9e8, transparent);
  animation: titleLineGlow 2s infinite alternate;
}

@keyframes titleLineGlow {
  0% { width: 30px; opacity: 0.5; }
  100% { width: 80px; opacity: 1; }
}

.auth-input {
  position: relative;
  width: 85%;
  margin: 10px 0;
}

.auth-input .el-input__inner {
  background-color: rgba(240, 240, 240, 0.8) !important;
  border: none !important;
  border-bottom: 1px solid #05d9e8 !important;
  border-radius: 0 !important;
  color: #333333 !important;
  padding: 10px 10px !important;
  font-size: 16px !important;
  box-shadow: none !important;
}

.auth-input .el-input__inner:focus {
  box-shadow: none !important;
  border-bottom-color: #05d9e8 !important;
  background-color: rgba(232, 248, 250, 0.5) !important;
}

.auth-input .el-input__wrapper {
  background-color: rgba(240, 240, 240, 0.8) !important;
  box-shadow: none !important;
  border: none !important;
  padding: 0 !important;
}

.auth-input .el-input__prefix {
  background-color: rgba(240, 240, 240, 0.8) !important;
  color: #05d9e8 !important;
  margin-right: 5px !important;
}

.auth-input .el-input__suffix {
  background-color: rgba(240, 240, 240, 0.8) !important;
  color: #05d9e8 !important;
}

.auth-form-label {
  position: absolute;
  top: -20px;
  left: 0;
  color: #05d9e8;
  transition: all 0.3s;
  font-size: 14px;
  font-weight: 500;
}

.button-container {
  width: 100%;
  display: flex;
  justify-content: center;
  margin: 35px 0 20px;
}

.auth-button {
  background-color: #05d9e8 !important;
  border: 2px solid #05d9e8 !important;
  color: #ffffff !important;
  font-weight: bold !important;
  padding: 10px 20px !important;
  letter-spacing: 2px !important;
  overflow: hidden !important;
  position: relative !important;
  transition: 0.5s !important;
  width: 180px !important;
  height: 48px !important;
  font-size: 17px !important;
  animation: borderPulse 2s infinite;
  z-index: 2 !important;
  box-shadow: 0 4px 15px rgba(5, 217, 232, 0.4) !important;
  border-radius: 6px !important;
}

@keyframes borderPulse {
  0% { box-shadow: 0 4px 15px rgba(5, 217, 232, 0.4); }
  70% { box-shadow: 0 4px 20px rgba(5, 217, 232, 0.6), 0 0 30px rgba(5, 217, 232, 0.2); }
  100% { box-shadow: 0 4px 15px rgba(5, 217, 232, 0.4); }
}

.auth-button:hover {
  background-color: #04c5d3 !important;
  color: #ffffff !important;
  box-shadow: 0 6px 20px rgba(5, 217, 232, 0.6) !important;
  transform: translateY(-3px) !important;
  letter-spacing: 3px !important;
}

/* 确保loading状态下按钮文字和图标可见 */
.auth-button.is-loading {
  color: #ffffff !important;
  background-color: #05d9e8 !important;
  border: 2px solid #05d9e8 !important;
}

.auth-button.is-loading .el-loading-spinner {
  color: #ffffff !important;
}

.auth-error {
  color: #f56c6c;
  font-size: 14px;
  text-align: left;
  width: 85%;
  margin: 10px 0;
}

.auth-toggle {
  margin-top: 15px;
  color: #666666;
  font-size: 14px;
  width: 100%;
  text-align: center;
}

/* 注册页面中返回登录链接的特殊位置 */
.register-page .auth-toggle {
  position: absolute;
  bottom: 20px;
  left: 25px;
  text-align: left;
  margin-top: 0;
}

.auth-toggle a {
  color: #05d9e8;
  text-decoration: none;
  font-weight: bold;
}

.auth-toggle a:hover {
  text-decoration: underline;
  color: #04c5d3;
}

.active-glow {
  position: relative;
  overflow: hidden;
}

.active-glow::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.7), transparent);
  animation: buttonGlow 2s infinite;
  z-index: 1 !important;
}

.active-glow::after {
  content: "";
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(45deg, #05d9e8, transparent, #05d9e8);
  background-size: 400%;
  z-index: -1;
  animation: borderRotate 8s linear infinite;
  filter: blur(5px);
  opacity: 0;
  transition: opacity 0.3s;
  border-radius: 6px;
}

.active-glow:hover::after {
  opacity: 0.7;
}

@keyframes buttonGlow {
  0% {
    left: -100%;
  }
  50%, 100% {
    left: 100%;
  }
}

@keyframes borderRotate {
  0% {
    background-position: 0% 0%;
  }
  100% {
    background-position: 400% 0%;
  }
}