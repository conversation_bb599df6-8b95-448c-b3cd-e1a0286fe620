<template>
  <div class="post-create-container">
    <!-- 赛博朋克风格背景元素 -->
    <div class="cyberpunk-background">
      <div class="cyberpunk-overlay"></div>
      <div class="cyberpunk-grid"></div>
      <div class="cyberpunk-scanline"></div>
      <div class="cyberpunk-glitch-effect"></div>
    </div>

    <div class="post-create-header">
      <div class="back-button" @click="goBack">
        <i class="el-icon-arrow-left"></i> 返回
      </div>
      <h1>发布内容</h1>

      <!-- 添加全息数据元素 -->
      <div class="holo-data-element">
        <div class="holo-circle"></div>
        <div class="holo-text">DATA UPLOAD</div>
      </div>
    </div>

    <div class="post-create-form">
      <!-- 添加装饰性数据可视化元素 -->
      <div class="data-viz-container">
        <div class="data-viz-bars">
          <div class="data-bar" v-for="(value, index) in [75, 45, 90, 60, 30]" :key="index"
               :style="{ height: value + '%', animationDelay: index * 0.2 + 's' }">
            <div class="data-bar-highlight"></div>
          </div>
        </div>
      </div>

      <el-form :model="postForm" label-position="top">
        <el-form-item label="内容分类">
          <el-radio-group v-model="postForm.category">
            <el-radio-button :label="0">新闻</el-radio-button>
            <el-radio-button :label="1">设备</el-radio-button>
            <el-radio-button :label="2">师生</el-radio-button>
            <el-radio-button :label="3">生活</el-radio-button>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="可见性">
          <el-radio-group v-model="postForm.visibility">
            <el-radio-button :label="0">公开</el-radio-button>
            <el-radio-button :label="1">仅实验室成员可见</el-radio-button>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="内容类型">
          <el-radio-group v-model="postForm.type">
            <el-radio-button :label="0">图片</el-radio-button>
            <el-radio-button :label="1">视频</el-radio-button>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="标题">
          <el-input
            v-model="postForm.title"
            placeholder="请输入标题..."
            maxlength="20"
            show-word-limit
            class="cyberpunk-input"
          ></el-input>
        </el-form-item>

        <el-form-item label="文字内容">
          <el-input
            v-model="postForm.content"
            type="textarea"
            :rows="5"
            placeholder="分享你的研究成果、学术见解或日常感想..."
            class="cyberpunk-input"
          ></el-input>
        </el-form-item>

        <el-form-item label="上传媒体">
          <div class="upload-container">
            <div class="upload-frame">
              <el-upload
                ref="uploadRef"
                :auto-upload="false"
                :limit="postForm.type === 0 ? 9 : 1"
                :multiple="postForm.type === 0"
                :file-list="fileList"
                :on-change="handleFileChange"
                :on-remove="handleFileRemove"
                list-type="picture-card"
                :class="{ 'hide-upload-button': fileList.length >= (postForm.type === 0 ? 9 : 1) }"
              >
                <i class="el-icon-plus"></i>
                <template #tip>
                  <div class="el-upload__tip">
                    {{ postForm.type === 0 ? '最多上传9张图片' : '上传一个视频文件' }}
                  </div>
                </template>
              </el-upload>
            </div>
          </div>
        </el-form-item>

        <el-form-item>
          <div class="form-actions">
            <el-button @click="goBack">取消</el-button>
            <el-button type="primary" @click="submitPost" :loading="submitting">
              <span class="button-text">发布</span>
              <span class="button-glow"></span>
            </el-button>
          </div>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed } from 'vue';
import { useRouter } from 'vue-router';
import { ElMessage, ElMessageBox, ElLoading } from 'element-plus';
import { createPost } from '../api/post';
import { uploadImage, uploadVideo } from '../api/file';

const router = useRouter();
const submitting = ref(false);
const fileList = ref([]);
const uploadRef = ref(null);
const mediaUrls = ref([]);

// 表单数据
const postForm = reactive({
  type: 0, // 0-图片 1-视频
  category: 0, // 0-新闻 1-设备 2-师生 3-生活
  visibility: 0, // 0-公开 1-仅实验室成员可见
  content: '',
  title: '' // 标题可选，可以从内容中提取
});

// 返回上一页
const goBack = () => {
  if (hasUnsavedChanges()) {
    ElMessageBox.confirm('您有未保存的内容，确定要离开吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }).then(() => {
      router.back();
    }).catch(() => {});
  } else {
    router.back();
  }
};

// 检查是否有未保存的更改
const hasUnsavedChanges = () => {
  return postForm.content.trim() !== '' || fileList.value.length > 0;
};

// 处理文件变化
const handleFileChange = (file, fileList) => {
  // 如果是视频类型，只保留最新上传的一个文件
  if (postForm.type === 1 && fileList.length > 1) {
    const latestFile = fileList[fileList.length - 1];
    fileList.value = [latestFile];
  } else {
    fileList.value = fileList;
  }
};

// 处理文件移除
const handleFileRemove = (file, fileList) => {
  fileList.value = fileList;
};

// 上传文件到服务器并获取URL
const uploadFiles = async () => {
  if (fileList.value.length === 0) {
    return [];
  }

  // 根据类型上传图片或视频
  if (postForm.type === 0) {
    // 图片上传
    const uploadPromises = fileList.value.map(async (file) => {
      try {
        const response = await uploadImage(file.raw);
        if (response.code === 200 && response.data && response.data.url) {
          return response.data.url;
        } else {
          throw new Error(`上传图片失败: ${response.message || '未知错误'}`);
        }
      } catch (error) {
        console.error('上传图片失败:', error);
        throw error;
      }
    });

    return await Promise.all(uploadPromises);
  } else {
    // 视频上传
    if (fileList.value.length > 0) {
      const videoFile = fileList.value[0].raw;
      try {
        const response = await uploadVideo(videoFile);
        if (response.code === 200 && response.data && response.data.videoUrl) {
          const urls = [response.data.videoUrl];
          if (response.data.coverUrl) {
            urls.push(response.data.coverUrl);
          }
          return urls;
        } else {
          throw new Error(`上传视频失败: ${response.message || '未知错误'}`);
        }
      } catch (error) {
        console.error('上传视频失败:', error);
        throw error;
      }
    }
  }

  return [];
};

// 提交表单
const submitPost = async () => {
  // 表单验证
  if (!postForm.title.trim()) {
    ElMessage.warning('请输入标题');
    return;
  }

  if (!postForm.content.trim()) {
    ElMessage.warning('请输入文字内容');
    return;
  }

  if (fileList.value.length === 0) {
    ElMessage.warning('请上传至少一个媒体文件');
    return;
  }

  try {
    submitting.value = true;

    // 显示全屏加载
    const loadingInstance = ElLoading.service({
      text: '正在上传文件并发布...',
      background: 'rgba(255, 255, 255, 0.7)'
    });

    // 1. 先上传文件获取URL
    mediaUrls.value = await uploadFiles();

    if (mediaUrls.value.length === 0) {
      throw new Error('文件上传失败');
    }

    // 标题已经在表单验证中确保不为空

    // 3. 创建帖子
    const postData = {
      type: postForm.type,
      title: postForm.title,
      category: postForm.category,
      visibility: postForm.visibility,
      content: postForm.content,
      mediaUrls: mediaUrls.value
    };

    const response = await createPost(postData);

    loadingInstance.close();

    if (response.code === 200) {
      ElMessage.success('发布成功');
      router.push('/posts');
    } else {
      throw new Error(`发布失败: ${response.message || '未知错误'}`);
    }
  } catch (error) {
    console.error('发布失败:', error);
    ElMessage.error(error.message || '发布失败，请稍后再试');
  } finally {
    submitting.value = false;
  }
};

// 添加动画效果的数据
const animationData = reactive({
  uploadProgress: 0,
  isUploading: false
});
</script>

<style scoped>
.post-create-container {
  min-height: 100vh;
  background-color: #f8f9fa;
  color: #333333;
  padding: 20px;
  padding-top: 80px; /* Space for fixed navigation */
  position: relative;
  overflow: hidden;
}

/* 赛博朋克风格背景元素 - 浅色系版本 */
.cyberpunk-background {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
  overflow: hidden;
}

.cyberpunk-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.8) 0%, rgba(245, 245, 250, 0.7) 100%);
  z-index: 1;
}

.cyberpunk-grid {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image:
    linear-gradient(rgba(0, 162, 255, 0.1) 1px, transparent 1px),
    linear-gradient(90deg, rgba(5, 217, 232, 0.1) 1px, transparent 1px);
  background-size: 40px 40px;
  z-index: 2;
}

.cyberpunk-scanline {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    to bottom,
    transparent 50%,
    rgba(0, 162, 255, 0.02) 50%
  );
  background-size: 100% 4px;
  z-index: 3;
  pointer-events: none;
  opacity: 0.1;
}

.cyberpunk-glitch-effect {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: transparent;
  z-index: 4;
  pointer-events: none;
  overflow: hidden;
}

.cyberpunk-glitch-effect::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(5, 217, 232, 0.1), transparent);
  animation: glitchSweep 10s infinite;
}

@keyframes glitchSweep {
  0%, 100% {
    left: -100%;
    opacity: 0;
  }
  10%, 90% {
    left: -100%;
    opacity: 0;
  }
  50% {
    left: 100%;
    opacity: 0.8;
  }
}

.post-create-header {
  display: flex;
  align-items: center;
  margin-bottom: 30px;
  position: relative;
}

.back-button {
  display: flex;
  align-items: center;
  cursor: pointer;
  color: #666666;
  margin-right: 20px;
  transition: color 0.3s;
  font-size: 16px;
}

.back-button:hover {
  color: #05d9e8;
}

.post-create-header h1 {
  font-size: 28px;
  color: #333333;
  margin: 0;
  letter-spacing: 2px;
  font-weight: 600;
}

/* 全息数据元素 - 浅色版 */
.holo-data-element {
  position: absolute;
  right: 20px;
  top: 50%;
  transform: translateY(-50%);
  display: flex;
  align-items: center;
  gap: 10px;
}

.holo-circle {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  border: 2px solid rgba(5, 217, 232, 0.5);
  position: relative;
  animation: holoPulse 3s infinite alternate;
}

.holo-circle::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 15px;
  height: 15px;
  border-radius: 50%;
  background-color: rgba(5, 217, 232, 0.3);
  box-shadow: 0 0 8px rgba(5, 217, 232, 0.5);
}

.holo-text {
  font-size: 14px;
  color: #05d9e8;
  letter-spacing: 1px;
  font-weight: 500;
}

@keyframes holoPulse {
  0% {
    box-shadow: 0 0 5px rgba(5, 217, 232, 0.3);
  }
  100% {
    box-shadow: 0 0 10px rgba(5, 217, 232, 0.5);
  }
}

.post-create-form {
  max-width: 800px;
  margin: 0 auto;
  background-color: rgba(255, 255, 255, 0.98);
  border-radius: 10px;
  padding: 35px;
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.06);
  position: relative;
  z-index: 5;
  border: 1px solid rgba(5, 217, 232, 0.1);
  overflow: hidden;
}

.post-create-form::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 3px;
  background: linear-gradient(90deg, #05d9e8, #00a8ff, #0088ff, #05d9e8);
  background-size: 300% 100%;
  animation: gradientBorder 5s linear infinite;
  z-index: 1;
}

@keyframes gradientBorder {
  0% {
    background-position: 0% 0%;
  }
  100% {
    background-position: 300% 0%;
  }
}

/* 数据可视化元素 - 浅色版 */
.data-viz-container {
  position: absolute;
  right: 20px;
  top: 20px;
  width: 100px;
  height: 80px;
  opacity: 0.5;
  pointer-events: none;
}

.data-viz-bars {
  display: flex;
  align-items: flex-end;
  justify-content: space-between;
  height: 100%;
}

.data-bar {
  width: 12px;
  background-color: rgba(5, 217, 232, 0.2);
  position: relative;
  animation: barPulse 2s infinite alternate;
}

.data-bar-highlight {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 3px;
  background-color: #05d9e8;
  box-shadow: 0 0 5px rgba(5, 217, 232, 0.5);
}

@keyframes barPulse {
  0% {
    height: calc(var(--height) - 10%);
  }
  100% {
    height: var(--height);
  }
}

.upload-container {
  position: relative;
  padding: 10px;
  border-radius: 8px;
  background: linear-gradient(135deg, rgba(0, 162, 255, 0.05) 0%, rgba(5, 217, 232, 0.05) 100%);
}

.upload-frame {
  position: relative;
  border-radius: 6px;
  padding: 10px;
  background-color: rgba(255, 255, 255, 0.8);
  border: 1px dashed rgba(5, 217, 232, 0.3);
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 15px;
  margin-top: 20px;
}

/* 按钮发光效果 - 浅色版 */
:deep(.el-button--primary) {
  position: relative;
  overflow: hidden;
  background-color: #05d9e8;
  border-color: #05d9e8;
  color: #fff;
  transition: all 0.3s;
}

:deep(.el-button--primary:hover) {
  background-color: #04c5d3;
  border-color: #04c5d3;
  box-shadow: 0 0 15px rgba(5, 217, 232, 0.5);
}

.button-text {
  position: relative;
  z-index: 2;
}

.button-glow {
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.5), transparent);
  animation: buttonGlow 3s infinite;
}

@keyframes buttonGlow {
  0% {
    left: -100%;
  }
  50% {
    left: 100%;
  }
  100% {
    left: 100%;
  }
}

/* Hide upload button when limit is reached */
.hide-upload-button :deep(.el-upload--picture-card) {
  display: none;
}

/* Element Plus overrides - 浅色版 */
:deep(.el-form-item__label) {
  color: #333333;
  font-weight: 500;
  letter-spacing: 1px;
  font-size: 16px;
}

:deep(.el-radio-button__inner) {
  background-color: #ffffff;
  border-color: #e0e0e0;
  color: #666666;
  transition: all 0.3s;
}

:deep(.el-radio-button__original-radio:checked + .el-radio-button__inner) {
  background-color: #05d9e8;
  border-color: #05d9e8;
  color: #fff;
  box-shadow: -1px 0 0 0 #05d9e8;
}

/* Cyberpunk input styling - 浅色版 */
:deep(.cyberpunk-input .el-input__inner) {
  background-color: #ffffff !important;
  border-color: #e0e0e0 !important;
  color: #333333 !important;
  transition: all 0.3s;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
  height: 40px;
  line-height: 40px;
}

:deep(.cyberpunk-input .el-input__inner:focus) {
  border-color: #05d9e8 !important;
  box-shadow: 0 0 8px rgba(5, 217, 232, 0.3) !important;
}

:deep(.cyberpunk-input .el-input__count) {
  background: transparent !important;
  color: #666666 !important;
  line-height: 1;
  height: auto;
  padding: 0;
  right: 10px;
  bottom: 5px;
}

:deep(.cyberpunk-input .el-textarea__inner) {
  background-color: #ffffff !important;
  border-color: #e0e0e0 !important;
  color: #333333 !important;
  transition: all 0.3s;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
}

:deep(.cyberpunk-input .el-textarea__inner:focus) {
  border-color: #05d9e8 !important;
  box-shadow: 0 0 8px rgba(5, 217, 232, 0.3) !important;
}

:deep(.el-upload--picture-card) {
  background-color: #ffffff;
  border: 1px dashed #05d9e8;
  color: #05d9e8;
  transition: all 0.3s;
}

:deep(.el-upload--picture-card:hover) {
  border-color: #04c5d3;
  color: #04c5d3;
  box-shadow: 0 0 10px rgba(5, 217, 232, 0.2);
}

:deep(.el-upload-list--picture-card .el-upload-list__item) {
  background-color: #f8f9fa;
  border-color: #e0e0e0;
}

:deep(.el-button) {
  background-color: #ffffff;
  border-color: #e0e0e0;
  color: #666666;
}

:deep(.el-button:hover) {
  background-color: #f5f5f5;
  border-color: #05d9e8;
  color: #05d9e8;
}

:deep(.el-upload__tip) {
  color: #666666;
  font-size: 14px;
}
</style>
