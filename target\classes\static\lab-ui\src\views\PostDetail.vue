<template>
  <div class="post-detail-container">
    <!-- Loading state -->
    <div v-if="loading" class="loading-container">
      <div class="loading-spinner">
        <i class="el-icon-loading"></i>
        <p>加载中...</p>
      </div>
    </div>

    <!-- Error state -->
    <div v-else-if="!post && !loading" class="error-container">
      <div class="error-message">
        <i class="el-icon-warning-outline"></i>
        <h3>加载失败</h3>
        <p>无法加载帖子详情，请稍后重试</p>
        <el-button type="primary" @click="goBack">返回</el-button>
      </div>
    </div>

    <!-- Content when post is loaded -->
    <div v-else-if="post" class="post-detail-content">
      <div class="post-header">
        <div class="back-button" @click="goBack">
          <i class="el-icon-arrow-left"></i> 返回
        </div>
      </div>

      <div class="post-main-content">
        <!-- Left Zone - Media Gallery -->
        <div class="post-media-zone">
          <div class="media-carousel">
            <div class="media-slide" v-if="post.mediaList && post.mediaList.length > 0">
              <img
                :src="post.mediaList[currentMediaIndex].url || ''"
                :alt="`Media ${currentMediaIndex + 1}`"
                @error="handleImageError"
                class="media-image"
              />
            </div>
            <div class="media-slide" v-else-if="post.coverUrl">
              <img
                :src="post.coverUrl"
                :alt="post.content"
                @error="handleImageError"
                class="media-image"
              />
            </div>
            <div class="media-slide" v-else>
              <img
                :src="`https://picsum.photos/id/${(parseInt(post.id) % 10) + 1}/800/400`"
                :alt="post.content"
                @error="handleImageError"
                class="media-image"
              />
            </div>

            <!-- Navigation buttons -->
            <div
              v-if="post.mediaList && post.mediaList.length > 1"
              class="media-nav-button prev-button"
              @click="prevMedia"
            >
              <i class="el-icon-arrow-left"></i>
            </div>
            <div
              v-if="post.mediaList && post.mediaList.length > 1"
              class="media-nav-button next-button"
              @click="nextMedia"
            >
              <i class="el-icon-arrow-right"></i>
            </div>

            <!-- Media indicators -->
            <div v-if="post.mediaList && post.mediaList.length > 1" class="media-indicators">
              <span
                v-for="(_, index) in post.mediaList"
                :key="index"
                :class="['indicator', { active: index === currentMediaIndex }]"
                @click="setCurrentMedia(index)"
              ></span>
            </div>
          </div>
        </div>

        <!-- Right Zone - Content and Comments -->
        <div class="post-content-zone">
          <!-- Author info -->
          <div class="author-info" v-if="post.userBasicVO">
            <img
              :src="post.userBasicVO.image || 'https://randomuser.me/api/portraits/men/1.jpg'"
              :alt="post.userBasicVO?.username || '匿名用户'"
              class="author-avatar"
              @error="handleImageError"
            />
            <div class="author-details">
              <div class="author-name">{{ post.userBasicVO?.username || '匿名用户' }}</div>
              <div class="post-date">{{ formatDate(post.createdAt) }}</div>
            </div>
            <div class="follow-button">
              <el-button type="primary" size="small">关注</el-button>
            </div>
          </div>

          <!-- Post title and content -->
          <div class="post-content">
            <h2 class="post-title">{{ post.title || (post.content ? post.content.substring(0, 30) + '...' : '无标题') }}</h2>
            <div class="post-text">{{ post.content || '无内容' }}</div>
          </div>

          <!-- Post actions -->
          <div class="post-actions">
            <div class="action-button" @click="handleLike">
              <div :class="['heart-icon', { 'liked': isLiked }]"></div>
              <span>{{ post.likeCount || 0 }}</span>
            </div>
            <div class="action-button" @click="handleCollect">
              <div :class="['star-icon', { 'collected': isCollected }]"></div>
              <span>{{ post.collectCount || 0 }}</span>
            </div>
            <div class="action-button" @click="focusCommentInput">
              <div class="comment-icon"></div>
              <span>{{ post.commentCount || 0 }}</span>
            </div>
          </div>

          <!-- Comments section -->
          <div class="comments-section">
            <h3>评论 ({{ post.commentCount || 0 }})</h3>

            <div class="comment-input">
              <img :src="userAvatar" alt="Your avatar" class="comment-avatar" @error="handleImageError" />
              <el-input
                v-model="commentText"
                placeholder="发表评论..."
                :rows="2"
                type="textarea"
                ref="commentInputRef"
              />
              <el-button type="primary" @click="submitComment" :disabled="!commentText.trim()">发送</el-button>
            </div>

            <div v-if="comments.length === 0" class="no-comments">
              <p>暂无评论，快来发表第一条评论吧！</p>
            </div>

            <div v-else class="comments-list">
              <div v-for="comment in comments" :key="comment.id" class="comment-item">
                <img
                  :src="comment.userAvatar"
                  :alt="comment.username"
                  class="comment-avatar"
                  @error="handleImageError"
                />
                <div class="comment-content">
                  <div class="comment-header">
                    <span class="comment-username">{{ comment.username }}</span>
                    <span class="comment-date">{{ formatDate(comment.createdAt) }}</span>
                  </div>
                  <div class="comment-text">{{ comment.content }}</div>
                  <div class="comment-actions">
                    <span @click="replyToComment(comment)">回复</span>
                    <span v-if="comment.hasReplies" @click="toggleReplies(comment)">
                      {{ comment.showReplies ? '收起回复' : `查看回复(${comment.replyCount})` }}
                    </span>
                  </div>

                  <div v-if="comment.showReplies">
                    <div v-if="comment.replies && comment.replies.length" class="comment-replies">
                      <div v-for="reply in comment.replies" :key="reply.id" class="reply-item">
                        <img
                          :src="reply.userAvatar"
                          :alt="reply.username"
                          class="reply-avatar"
                          @error="handleImageError"
                        />
                        <div class="reply-content">
                          <div class="reply-header">
                            <span class="reply-username">{{ reply.username }}</span>
                            <span class="reply-date">{{ formatDate(reply.createdAt) }}</span>
                          </div>
                          <div class="reply-text">
                            <span class="reply-to">@{{ reply.replyTo }}</span> {{ reply.content }}
                          </div>
                          <div class="reply-actions">
                            <span @click="replyToChildComment(reply, comment)">回复</span>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div v-else class="no-replies">
                      <p>暂无回复</p>
                    </div>
                  </div>
                </div>
              </div>

              <div class="load-more-comments" v-if="hasMoreComments">
                <el-button @click="loadMoreComments" :loading="loadingComments">加载更多评论</el-button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { ElMessage, ElLoading } from 'element-plus';
import { getPostDetail, likePost, collectPost, addComment, getChildComments } from '../api/post';
import { getUserInfo } from '../api/user';

const route = useRoute();
const router = useRouter();
const postId = computed(() => route.params.id);

// Post data
const post = ref(null);
const comments = ref([]);
const commentText = ref('');
const commentInputRef = ref(null);
const isLiked = ref(false);
const isCollected = ref(false);
const hasMoreComments = ref(true);
const loadingComments = ref(false);
const userAvatar = ref('https://randomuser.me/api/portraits/men/1.jpg');
const loading = ref(false);
const lastCommentId = ref(0);
const currentUser = ref({
  username: '',
  id: null,
  image: ''
});

// Media carousel state
const currentMediaIndex = ref(0);

// Fetch current user info
const fetchCurrentUser = async () => {
  try {
    // Check if user is logged in
    const token = localStorage.getItem('token');
    if (!token) {
      console.log('User not logged in');
      return;
    }

    const response = await getUserInfo();
    if (response.code === 200 && response.data) {
      currentUser.value = {
        username: response.data.username || '用户',
        id: response.data.id,
        image: response.data.image || 'https://randomuser.me/api/portraits/men/1.jpg'
      };
      userAvatar.value = currentUser.value.image;
      console.log('Current user info:', currentUser.value);
    }
  } catch (error) {
    console.error('Failed to fetch current user info:', error);
  }
};

// Fetch post data
onMounted(async () => {
  try {
    // Fetch current user info first
    await fetchCurrentUser();

    loading.value = true;
    const loadingInstance = ElLoading.service({
      target: '.post-detail-container',
      text: '加载中...'
    });

    console.log('Fetching post detail for ID:', postId.value);

    // Add fallback for missing or invalid postId
    if (!postId.value || isNaN(parseInt(postId.value))) {
      console.error('Invalid post ID:', postId.value);
      ElMessage.error('无效的帖子ID');
      loading.value = false;
      loadingInstance.close();
      return;
    }

    // Try to fetch real data from the API
    try {
      // Call the API
      const response = await getPostDetail(postId.value);
      console.log('Post detail API response:', response);

      if (response && response.code === 200 && response.data) {
        post.value = response.data;
        console.log('Post data received:', post.value);
        console.log('Comments data:', post.value.commentVOList);
        console.log('Comment count:', post.value.commentCount);

        // Format media list if needed
        if (post.value.mediaVOList && post.value.mediaVOList.length > 0) {
          post.value.mediaList = post.value.mediaVOList.map(media => ({
            url: media.mediaUrl
          }));
        } else if (post.value.coverUrl) {
          // If no media list but has cover URL, use that as the only media
          post.value.mediaList = [{ url: post.value.coverUrl }];
        } else {
          // Fallback to placeholder images
          post.value.mediaList = [
            { url: `https://picsum.photos/id/${(parseInt(postId.value) % 10) + 1}/800/400` }
          ];
        }

        // Set comments if available
        if (post.value.commentVOList && post.value.commentVOList.length > 0) {
          console.log('Mapping comments from API response:', post.value.commentVOList);
          comments.value = post.value.commentVOList.map(comment => {
            console.log('Processing comment:', comment);
            return {
              id: comment.id,
              username: comment.userBasicVO?.username || '匿名用户',
              userAvatar: comment.userBasicVO?.image || 'https://randomuser.me/api/portraits/men/1.jpg',
              content: comment.content,
              createdAt: comment.createTime,
              showReplies: false,
              hasReplies: comment.hasChildComment,
              replyCount: comment.childCommentNum || 0,
              replies: []
            };
          });

          console.log('Mapped comments:', comments.value);
          lastCommentId.value = post.value.lastCommentId || 0;
          hasMoreComments.value = comments.value.length < (post.value.commentCount || 0);
        } else {
          console.log('No comments found in API response');
          comments.value = [];
          hasMoreComments.value = false;
        }

        // Ensure comment count is properly set
        if (post.value.commentCount === undefined || post.value.commentCount === null) {
          console.log('Comment count is undefined, setting to 0');
          post.value.commentCount = 0;
        }

        // Close loading state after successful data fetch
        loading.value = false;
        loadingInstance.close();
      } else {
        throw new Error('Failed to get post detail');
      }
    } catch (error) {
      console.error('获取帖子详情失败，使用模拟数据:', error);

      // Fallback to mock data if API fails
      post.value = {
        id: postId.value,
        title: '人工智能研究团队最新研究成果',
        content: '人工智能研究团队最新研究成果：基于深度学习的图像识别系统取得突破性进展。我们的团队经过六个月的努力，成功开发了一种新型的图像识别算法，该算法在标准测试集上的准确率达到了98.7%，超过了当前业界的最高水平。这一成果已被提交到顶级学术会议CVPR 2024，并有望获得接收。',
        coverUrl: `https://picsum.photos/id/${(parseInt(postId.value) % 10) + 1}/800/400`,
        userBasicVO: {
          username: '张教授',
          image: 'https://randomuser.me/api/portraits/men/1.jpg'
        },
        mediaList: [
          { url: `https://picsum.photos/id/${(parseInt(postId.value) % 10) + 1}/800/400` }
        ],
        likeCount: 42,
        collectCount: 18,
        commentCount: 7,
        createdAt: new Date('2023-11-15')
      };

      // Create mock comments
      comments.value = [
        {
          id: 1,
          username: '李研究员',
          userAvatar: 'https://randomuser.me/api/portraits/women/2.jpg',
          content: '恭喜团队取得这一重要突破！期待在会议上看到详细的论文。',
          createdAt: new Date('2023-11-15T10:30:00'),
          showReplies: false,
          hasReplies: true,
          replyCount: 2,
          replies: []
        }
      ];

      hasMoreComments.value = false;
      lastCommentId.value = 1;

      loading.value = false;
      loadingInstance.close();
    }
  } catch (error) {
    console.error('获取帖子详情失败:', error);
    ElMessage.error('获取帖子详情失败，请稍后重试');
    loading.value = false;
    // Use a new loading instance since the original one might not be accessible in this scope
    ElLoading.service().close();
  }
});

// Media navigation functions
const nextMedia = () => {
  if (!post.value || !post.value.mediaList || post.value.mediaList.length <= 1) return;

  if (currentMediaIndex.value < post.value.mediaList.length - 1) {
    currentMediaIndex.value++;
  } else {
    currentMediaIndex.value = 0; // Loop back to the first image
  }
};

const prevMedia = () => {
  if (!post.value || !post.value.mediaList || post.value.mediaList.length <= 1) return;

  if (currentMediaIndex.value > 0) {
    currentMediaIndex.value--;
  } else {
    currentMediaIndex.value = post.value.mediaList.length - 1; // Loop to the last image
  }
};

const setCurrentMedia = (index) => {
  if (!post.value || !post.value.mediaList) return;

  if (index >= 0 && index < post.value.mediaList.length) {
    currentMediaIndex.value = index;
  }
};

// Format date
const formatDate = (date) => {
  if (!date) return '';
  return new Date(date).toLocaleString('zh-CN');
};

// Go back
const goBack = () => {
  router.back();
};

// Handle like
const handleLike = async () => {
  if (!post.value) return;

  try {
    const response = await likePost(post.value.id);

    if (response.code === 200) {
      if (!isLiked.value) {
        post.value.likeCount++;
        isLiked.value = true;
        ElMessage.success('点赞成功');
      } else {
        post.value.likeCount--;
        isLiked.value = false;
        ElMessage.success('已取消点赞');
      }
    } else {
      ElMessage.error(response.message || '操作失败，请稍后重试');
    }
  } catch (error) {
    console.error('点赞操作失败:', error);
    ElMessage.error('操作失败，请稍后重试');
  }
};

// Handle collect
const handleCollect = async () => {
  if (!post.value) return;

  try {
    const response = await collectPost(post.value.id);

    if (response.code === 200) {
      if (!isCollected.value) {
        post.value.collectCount++;
        isCollected.value = true;
        ElMessage.success('收藏成功');
      } else {
        post.value.collectCount--;
        isCollected.value = false;
        ElMessage.success('已取消收藏');
      }
    } else {
      ElMessage.error(response.message || '操作失败，请稍后重试');
    }
  } catch (error) {
    console.error('收藏操作失败:', error);
    ElMessage.error('操作失败，请稍后重试');
  }
};

// Focus comment input
const focusCommentInput = () => {
  if (commentInputRef.value) {
    commentInputRef.value.focus();
  }
};

// Submit comment
const submitComment = async () => {
  if (!commentText.value.trim() || !post.value) return;

  try {
    // Extract parent comment ID if replying to a comment
    let parentId = 0;

    // First check if we have a stored comment ID from replyToComment
    if (commentText.value.commentId) {
      console.log(`Using stored comment ID for reply: ${commentText.value.commentId}`);
      parentId = commentText.value.commentId;
    } else {
      // Fallback to the old method of finding by username
      const replyMatch = commentText.value.match(/^@([^\s]+)/);
      if (replyMatch) {
        const replyToUsername = replyMatch[1];
        console.log(`Looking for comment with username: ${replyToUsername}`);

        // Find the comment we're replying to
        const parentComment = comments.value.find(c => c.username === replyToUsername);
        if (parentComment) {
          parentId = parentComment.id;
          console.log(`Found parent comment with ID: ${parentId}`);
        } else {
          // If not found in top-level comments, search in replies
          console.log('Searching in replies...');
          for (const comment of comments.value) {
            if (comment.replies && comment.replies.length > 0) {
              const replyComment = comment.replies.find(r => r.username === replyToUsername);
              if (replyComment) {
                // When replying to a reply, we should use the parent comment's ID
                parentId = comment.id;
                console.log(`Found reply in comment ID: ${parentId}`);
                break;
              }
            }
          }
        }
      }
    }

    // Prepare comment data
    const commentData = {
      postId: post.value.id,
      content: commentText.value,
      parentId: parentId
    };

    // Call the API to add the comment
    const response = await addComment(commentData);

    if (response.code === 200) {
      // If successful, add the comment to the UI
      const newComment = {
        id: response.data?.id || Date.now(), // Use the returned ID if available
        username: currentUser.value.username || '用户',
        userAvatar: userAvatar.value,
        content: commentText.value,
        createdAt: new Date(),
        showReplies: false,
        hasReplies: false,
        replyCount: 0,
        replies: []
      };

      comments.value.unshift(newComment);
      post.value.commentCount++;

      // Clear the comment text and the stored comment ID
      commentText.value = '';
      commentText.value.commentId = null;

      ElMessage.success('评论发布成功');
    } else {
      //ElMessage.error(response.message || '评论发布失败，请稍后重试');
    }
  } catch (error) {
    console.error('评论发布失败:', error);
    //ElMessage.error('评论发布失败，请稍后重试');
  }
};

// Reply to comment
const replyToComment = (comment) => {
  // Store the comment ID and username for reference when submitting
  commentText.value = `@${comment.username} `;
  // Store the comment ID in a data attribute for later retrieval
  commentText.value.commentId = comment.id;
  console.log(`Setting up reply to comment ID: ${comment.id}, username: ${comment.username}`);
  focusCommentInput();
};

// Reply to child comment (reply)
const replyToChildComment = (reply, parentComment) => {
  // Store the parent comment ID and the reply username
  commentText.value = `@${reply.username} `;
  // When replying to a child comment, we need to use the parent comment's ID
  commentText.value.commentId = parentComment.id;
  console.log(`Setting up reply to child comment. Parent ID: ${parentComment.id}, Reply username: ${reply.username}`);
  focusCommentInput();
};

// Toggle replies
const toggleReplies = async (comment) => {
  // If we're showing replies and there are none loaded yet but the comment has replies
  if (!comment.showReplies && comment.hasReplies && (!comment.replies || comment.replies.length === 0)) {
    try {
      console.log('Fetching child comments for comment ID:', comment.id);

      // Fetch child comments from the API
      const response = await getChildComments(comment.id);
      console.log('Child comments response:', response);

      if (response && response.code === 200 && response.data) {
        console.log('Full response data:', response.data);

        // Map the child comments to our format
        // Check for commentVOList which is the correct property name from ChildCommentVO
        if (response.data.commentVOList && response.data.commentVOList.length > 0) {
          console.log('Child comments received:', response.data.commentVOList);

          comment.replies = response.data.commentVOList.map(reply => ({
            id: reply.id,
            username: reply.userBasicVO?.username || '匿名用户',
            userAvatar: reply.userBasicVO?.image || 'https://randomuser.me/api/portraits/men/1.jpg',
            content: reply.content,
            createdAt: reply.createTime,
            replyTo: reply.replyToUsername || comment.username
          }));

          console.log('Mapped child comments:', comment.replies);
        } else {
          console.log('No child comments found');
          comment.replies = [];
        }
      } else {
        console.error('Failed to fetch child comments:', response);
        ElMessage.error('获取回复失败，请稍后重试');
      }

      // Toggle visibility
      comment.showReplies = true;
    } catch (error) {
      console.error('获取回复失败:', error);
      ElMessage.error('获取回复失败，请稍后重试');
    }
  } else {
    // Simply toggle visibility if replies are already loaded
    comment.showReplies = !comment.showReplies;
    console.log('Toggled replies visibility:', comment.showReplies);
  }
};

// Handle image loading errors
const handleImageError = (e) => {
  e.target.src = 'https://via.placeholder.com/400x300?text=Image+Not+Available';
};

// Load more comments
const loadMoreComments = async () => {
  if (!post.value || !lastCommentId.value) {
    console.log('Cannot load more comments: post or lastCommentId is missing', {
      postId: post.value?.id,
      lastCommentId: lastCommentId.value
    });
    return;
  }

  loadingComments.value = true;
  console.log('Loading more comments with lastCommentId:', lastCommentId.value);

  try {
    // Use the same getPostDetail API but with the lastCommentId parameter
    // We need to modify this to include the lastCommentId in the request
    const response = await getPostDetail(post.value.id, lastCommentId.value);
    console.log('Load more comments response:', response);

    if (response.code === 200 && response.data) {
      // If there are more comments in the response
      if (response.data.commentVOList && response.data.commentVOList.length > 0) {
        console.log('New comments received:', response.data.commentVOList);

        const newComments = response.data.commentVOList.map(comment => {
          console.log('Processing new comment:', comment);
          return {
            id: comment.id,
            username: comment.userBasicVO?.username || '匿名用户',
            userAvatar: comment.userBasicVO?.image || 'https://randomuser.me/api/portraits/men/1.jpg',
            content: comment.content,
            createdAt: comment.createTime,
            showReplies: false,
            hasReplies: comment.hasChildComment,
            replyCount: comment.childCommentNum || 0,
            replies: []
          };
        });

        console.log('Mapped new comments:', newComments);

        // Filter out any duplicates before adding to the list
        const existingIds = comments.value.map(c => c.id);
        const uniqueNewComments = newComments.filter(c => !existingIds.includes(c.id));

        if (uniqueNewComments.length > 0) {
          // Add new comments to the list
          comments.value = [...comments.value, ...uniqueNewComments];
          console.log('Updated comments list:', comments.value);

          // Update lastCommentId for next pagination
          if (response.data.lastCommentId) {
            lastCommentId.value = response.data.lastCommentId;
            console.log('Updated lastCommentId:', lastCommentId.value);
            // Check if we have more comments to load
            hasMoreComments.value = comments.value.length < (post.value.commentCount || 0);
          } else {
            console.log('No lastCommentId in response, no more comments to load');
            hasMoreComments.value = false;
          }
        } else {
          console.log('No new unique comments received');
          hasMoreComments.value = false;
        }
      } else {
        console.log('No comments in response');
        hasMoreComments.value = false;
      }
    } else {
      console.error('Failed to load more comments:', response);
      ElMessage.error('加载更多评论失败');
      hasMoreComments.value = false;
    }
  } catch (error) {
    console.error('加载更多评论失败:', error);
    ElMessage.error('加载更多评论失败，请稍后重试');
    hasMoreComments.value = false;
  } finally {
    loadingComments.value = false;
  }
};
</script>

<style scoped>
.post-detail-container {
  min-height: 100vh;
  width: 100%;
  background-color: #f8f9fa;
  color: #333333;
  padding: 20px;
  padding-top: 80px; /* Space for fixed navigation */
  position: relative;
  margin: 0;
  box-sizing: border-box;
}

.post-detail-content {
  max-width: 1200px;
  margin: 0 auto;
  background-color: #ffffff;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.loading-container, .error-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: calc(100vh - 100px);
  width: 100%;
}

.loading-spinner, .error-message {
  text-align: center;
  padding: 30px;
  background-color: #ffffff;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.loading-spinner i, .error-message i {
  font-size: 48px;
  margin-bottom: 20px;
  color: #05d9e8;
}

.error-message i {
  color: #ff2a6d;
}

.error-message h3 {
  font-size: 24px;
  margin-bottom: 10px;
  color: #333333;
}

.error-message p {
  margin-bottom: 20px;
  color: #666666;
}

.post-header {
  padding: 15px 20px;
  border-bottom: 1px solid #e9ecef;
  display: flex;
  justify-content: flex-start;
  align-items: center;
}

.back-button {
  display: flex;
  align-items: center;
  cursor: pointer;
  color: #666666;
  transition: color 0.3s;
}

.back-button:hover {
  color: #05d9e8;
}

/* Main content layout with two zones */
.post-main-content {
  display: flex;
  flex-direction: row;
  min-height: calc(100vh - 150px);
}

/* Left zone - Media Gallery */
.post-media-zone {
  flex: 1;
  background-color: #f0f2f5;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  border-right: 1px solid rgba(5, 217, 232, 0.3);
  position: relative;
}

.post-media-zone::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image:
    linear-gradient(rgba(5, 217, 232, 0.05) 1px, transparent 1px),
    linear-gradient(90deg, rgba(5, 217, 232, 0.05) 1px, transparent 1px);
  background-size: 30px 30px;
  z-index: 1;
  pointer-events: none;
}

.post-media-zone::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle at center, transparent 0%, #f0f2f5 80%);
  z-index: 2;
  pointer-events: none;
}

.media-carousel {
  width: 100%;
  height: 100%;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 3;
}

.media-slide {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.media-image {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  filter: saturate(1.1) contrast(1.05);
}

.media-carousel:hover .media-image {
  filter: saturate(1.2) contrast(1.1) brightness(1.05);
  box-shadow: 0 8px 20px rgba(5, 217, 232, 0.3);
}

.media-nav-button {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  width: 40px;
  height: 40px;
  background-color: rgba(255, 255, 255, 0.7);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: #333333;
  z-index: 10;
  transition: all 0.3s;
  opacity: 0;
}

.media-carousel:hover .media-nav-button {
  opacity: 1;
}

.media-nav-button:hover {
  background-color: rgba(5, 217, 232, 0.4);
  box-shadow: 0 0 10px rgba(5, 217, 232, 0.5);
  color: #ffffff;
}

.prev-button {
  left: 20px;
}

.next-button {
  right: 20px;
}

.media-indicators {
  position: absolute;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 10px;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.media-carousel:hover .media-indicators {
  opacity: 1;
}

.indicator {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background-color: rgba(0, 0, 0, 0.2);
  cursor: pointer;
  transition: all 0.3s;
}

.indicator:hover {
  transform: scale(1.2);
  background-color: rgba(5, 217, 232, 0.5);
}

.indicator.active {
  background-color: #05d9e8;
  box-shadow: 0 0 8px rgba(5, 217, 232, 0.8);
}

/* Right zone - Content and Comments */
.post-content-zone {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
  max-height: calc(100vh - 150px);
  background-color: #ffffff; /* Light background color */
  color: #333333; /* Dark text for better contrast on light background */
  position: relative;
  border-left: 1px solid rgba(5, 217, 232, 0.3);
}

.post-content-zone::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 150px;
  height: 150px;
  background: radial-gradient(circle at top right, rgba(5, 217, 232, 0.1), transparent 70%);
  pointer-events: none;
  z-index: 1;
}

.post-content-zone > * {
  position: relative;
  z-index: 2;
}

/* Author info */
.author-info {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid rgba(5, 217, 232, 0.2);
  position: relative;
}

.author-info::after {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 0;
  width: 100%;
  height: 1px;
  background: linear-gradient(90deg, rgba(5, 217, 232, 0.5), transparent);
}

.author-avatar {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  margin-right: 15px;
  border: 2px solid rgba(5, 217, 232, 0.3);
  box-shadow: 0 0 10px rgba(5, 217, 232, 0.2);
  transition: all 0.3s;
}

.author-avatar:hover {
  border-color: rgba(5, 217, 232, 0.7);
  box-shadow: 0 0 15px rgba(5, 217, 232, 0.4);
}

.author-details {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.author-name {
  font-weight: bold;
  font-size: 20px;
  color: #05d9e8;
  text-shadow: 0 0 5px rgba(5, 217, 232, 0.3);
}

.post-date {
  font-size: 16px;
  color: #666666;
  margin-top: 5px;
  position: relative;
  display: inline-block;
  padding-left: 15px;
}

.post-date::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 8px;
  height: 8px;
  background-color: rgba(5, 217, 232, 0.3);
  border-radius: 50%;
}

.follow-button {
  margin-left: auto;
}

/* Post content */
.post-content {
  margin-bottom: 20px;
  position: relative;
}

.post-title {
  font-size: 28px;
  font-weight: bold;
  margin-bottom: 15px;
  color: #05d9e8; /* Maintain cyberpunk theme color */
  text-shadow: 0 0 10px rgba(5, 217, 232, 0.5);
  position: relative;
  display: inline-block;
}

.post-title::after {
  content: '';
  position: absolute;
  bottom: -5px;
  left: 0;
  width: 100%;
  height: 1px;
  background: linear-gradient(90deg, #05d9e8, transparent);
}

.post-text {
  font-size: 18px;
  line-height: 1.8;
  margin-bottom: 20px;
  white-space: pre-line;
  color: #333333; /* Dark text for better contrast on light background */
  padding: 15px;
  background-color: rgba(240, 242, 245, 0.5);
  border-left: 2px solid #ff2a6d;
  border-radius: 0 8px 8px 0;
}

/* Post actions */
.post-actions {
  display: flex;
  gap: 20px;
  padding: 15px 0;
  border-top: 1px solid rgba(5, 217, 232, 0.2); /* Maintain cyberpunk theme */
  border-bottom: 1px solid rgba(5, 217, 232, 0.2); /* Maintain cyberpunk theme */
  margin-bottom: 20px;
  position: relative;
}

.post-actions::before {
  content: '';
  position: absolute;
  top: -1px;
  left: 0;
  width: 100%;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(5, 217, 232, 0.5), transparent);
}

.action-button {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  color: #666666; /* Dark gray for better contrast on light background */
  transition: all 0.3s;
  padding: 5px 10px;
  border-radius: 4px;
  position: relative;
  overflow: hidden;
  font-size: 18px;
}

.action-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(5, 217, 232, 0.1), transparent);
  transition: all 0.5s;
}

.action-button:hover {
  color: #05d9e8; /* Maintain cyberpunk theme */
  background-color: rgba(5, 217, 232, 0.05);
}

.action-button:hover::before {
  left: 100%;
}

.heart-icon {
  display: inline-block;
  width: 18px;
  height: 16px;
  position: relative;
  margin-right: 5px;
}

.heart-icon:before,
.heart-icon:after {
  content: "";
  position: absolute;
  top: 0;
  width: 9px;
  height: 15px;
  border-radius: 9px 9px 0 0;
  background: #666666;
  transition: all 0.3s;
}

.heart-icon:before {
  left: 0;
  transform: rotate(-45deg);
  transform-origin: 100% 100%;
}

.heart-icon:after {
  left: 9px;
  transform: rotate(45deg);
  transform-origin: 0 100%;
}

.heart-icon.liked:before,
.heart-icon.liked:after {
  background: #ff2a6d; /* Maintain cyberpunk theme */
  box-shadow: 0 0 5px rgba(255, 42, 109, 0.7);
}

.star-icon {
  display: inline-block;
  width: 18px;
  height: 18px;
  position: relative;
  margin-right: 5px;
}

.star-icon:before {
  content: "★";
  font-size: 18px;
  color: #666666;
  transition: all 0.3s;
}

.star-icon.collected:before {
  color: #05d9e8; /* Maintain cyberpunk theme */
  text-shadow: 0 0 5px rgba(5, 217, 232, 0.7);
}

.comment-icon {
  display: inline-block;
  width: 18px;
  height: 18px;
  position: relative;
  margin-right: 5px;
}

.comment-icon:before {
  content: "";
  position: absolute;
  width: 14px;
  height: 10px;
  border: 2px solid #666666;
  border-radius: 3px;
  top: 0;
  left: 0;
  transition: all 0.3s;
}

.comment-icon:after {
  content: "";
  position: absolute;
  width: 0;
  height: 0;
  border-style: solid;
  border-width: 5px 5px 0 0;
  border-color: #666666 transparent transparent transparent;
  bottom: 0;
  left: 2px;
  transition: all 0.3s;
}

.action-button:hover .comment-icon:before {
  border-color: #05d9e8;
  box-shadow: 0 0 5px rgba(5, 217, 232, 0.5);
}

.action-button:hover .comment-icon:after {
  border-color: #05d9e8 transparent transparent transparent;
}

/* Comments section */
.comments-section {
  margin-top: 30px;
}

.comments-section h3 {
  margin-bottom: 20px;
  color: #333333; /* Dark text for better contrast on light background */
  font-size: 24px;
}

.comment-input {
  display: flex;
  gap: 10px;
  margin-bottom: 30px;
}

.comment-avatar, .reply-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
}

.comment-input .el-input {
  flex: 1;
}

.no-comments {
  text-align: center;
  padding: 30px;
  color: #666666; /* Dark gray for better contrast on light background */
  background-color: #f0f2f5; /* Light gray background */
  border-radius: 8px;
  margin-bottom: 20px;
}

.comments-list {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.comment-item {
  display: flex;
  gap: 10px;
}

.comment-content {
  flex: 1;
  background-color: #f0f2f5; /* Light gray background */
  border-radius: 8px;
  padding: 15px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
}

.comment-header, .reply-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 5px;
}

.comment-username, .reply-username {
  font-weight: bold;
  color: #333333; /* Dark text for better contrast on light background */
  font-size: 18px;
}

.comment-date, .reply-date {
  font-size: 14px;
  color: #666666; /* Medium gray for better contrast on light background */
}

.comment-text, .reply-text {
  margin-bottom: 10px;
  line-height: 1.5;
  color: #333333; /* Dark text for better contrast on light background */
  font-size: 18px;
}

.comment-actions {
  display: flex;
  gap: 15px;
}

.comment-actions span {
  color: #666666; /* Medium gray for better contrast on light background */
  cursor: pointer;
  font-size: 14px;
}

.comment-actions span:hover {
  color: #05d9e8; /* Use theme color for hover */
}

.comment-replies {
  margin-top: 15px;
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.reply-item {
  display: flex;
  gap: 10px;
}

.reply-content {
  flex: 1;
  background-color: #e9ecef; /* Slightly darker gray for replies on light background */
  border-radius: 8px;
  padding: 10px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.reply-to {
  color: #05d9e8;
  margin-right: 5px;
}

.reply-actions {
  display: flex;
  gap: 15px;
  margin-top: 5px;
}

.reply-actions span {
  color: #666666;
  cursor: pointer;
  font-size: 14px;
}

.reply-actions span:hover {
  color: #05d9e8;
}

.no-replies {
  padding: 10px;
  background-color: #e9ecef;
  border-radius: 8px;
  margin-top: 10px;
  text-align: center;
  color: #666666;
}

.load-more-comments {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

/* Element Plus overrides */
:deep(.el-textarea__inner) {
  background-color: #ffffff; /* Light background for textarea */
  border-color: rgba(5, 217, 232, 0.3); /* Cyberpunk border */
  color: #333333; /* Dark text */
  transition: all 0.3s;
}

:deep(.el-textarea__inner:focus) {
  border-color: rgba(5, 217, 232, 0.7);
  box-shadow: 0 0 10px rgba(5, 217, 232, 0.3);
}

:deep(.el-button) {
  background-color: rgba(5, 217, 232, 0.2);
  border-color: #05d9e8;
  color: #05d9e8;
  position: relative;
  overflow: hidden;
  transition: all 0.3s;
}

:deep(.el-button:hover) {
  background-color: rgba(5, 217, 232, 0.4);
  border-color: #05d9e8;
  box-shadow: 0 0 10px rgba(5, 217, 232, 0.5);
}

:deep(.el-button::before) {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(5, 217, 232, 0.2), transparent);
  transition: all 0.5s;
}

:deep(.el-button:hover::before) {
  left: 100%;
}

/* Responsive design */
@media (max-width: 992px) {
  .post-main-content {
    flex-direction: column;
  }

  .post-media-zone {
    height: 400px;
    border-right: none;
    border-bottom: 1px solid #e9ecef;
  }

  .post-content-zone {
    max-height: none;
  }
}
</style>
