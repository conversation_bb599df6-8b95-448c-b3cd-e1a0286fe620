<template>
  <div class="teams-container">
    <div class="teams-header">
      <div class="cyberpunk-grid"></div>
      <div class="cyberpunk-glitch"></div>
      <h1 class="cyberpunk-title" data-text="团队成员">{{ currentLang === 'zh' ? '团队成员' : 'Team Members' }}</h1>
      <div class="title-decoration">
        <div class="decoration-line"></div>
        <div class="decoration-circle"></div>
        <div class="decoration-line"></div>
      </div>
      <p class="cyberpunk-subtitle">{{ currentLang === 'zh' ? '我们的研究团队由一群充满激情和创造力的专家组成' : 'Our research team consists of passionate and creative experts' }}</p>
    </div>

    <div class="team-categories">
      <div
        v-for="category in categories"
        :key="category.id"
        class="category-item"
        :class="{ active: selectedCategory === category.id }"
        @click="selectedCategory = category.id"
      >
        <span class="category-text">{{ currentLang === 'zh' ? category.name : category.nameEn }}</span>
      </div>
    </div>

    <div class="team-members">
      <div v-for="member in filteredMembers" :key="member.id" class="member-card">
        <div class="member-photo">
          <div class="photo-frame">
            <img :src="member.photo" :alt="member.name" />
            <div class="photo-overlay"></div>
            <div class="photo-glitch"></div>
            <div class="corner-decoration top-left"></div>
            <div class="corner-decoration top-right"></div>
            <div class="corner-decoration bottom-left"></div>
            <div class="corner-decoration bottom-right"></div>
          </div>
        </div>
        <div class="member-info">
          <h3 class="member-name">{{ currentLang === 'zh' ? member.name : member.nameEn }}</h3>
          <div class="info-divider"></div>
          <p class="member-title">{{ currentLang === 'zh' ? member.title : member.titleEn }}</p>
          <p class="member-degree">{{ currentLang === 'zh' ? member.degree : member.degreeEn }}</p>
          <p class="member-affiliation">{{ currentLang === 'zh' ? member.affiliation : member.affiliationEn }}</p>
          <div class="card-decoration"></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';

// Language state
const currentLang = ref(localStorage.getItem('language') || 'zh');

// Team categories
const categories = [
  { id: 'all', name: '全部', nameEn: 'All' },
  { id: 'faculty', name: '教师', nameEn: 'Faculty' },
  { id: 'postdoc', name: '博士后', nameEn: 'Postdocs' },
  { id: 'phd', name: '博士生', nameEn: 'PhD Students' },
  { id: 'master', name: '硕士生', nameEn: 'Master Students' },
  { id: 'alumni', name: '校友', nameEn: 'Alumni' }
];

// Selected category
const selectedCategory = ref('all');

// Team members data (mock data)
const members = ref([
  {
    id: 1,
    name: '陈明',
    nameEn: 'Ming Chen',
    title: '教授，博士生导师',
    titleEn: 'Professor, PhD Supervisor',
    degree: '博士',
    degreeEn: 'PhD',
    affiliation: '计算机科学与工程系',
    affiliationEn: 'Department of Computer Science and Engineering',
    photo: 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8MjB8fGFzaWFuJTIwbWFufGVufDB8fDB8fHww&auto=format&fit=crop&w=500&q=60',
    category: 'faculty'
  },
  {
    id: 2,
    name: '李华',
    nameEn: 'Hua Li',
    title: '副教授，博士生导师',
    titleEn: 'Associate Professor, PhD Supervisor',
    degree: '博士',
    degreeEn: 'PhD',
    affiliation: '人工智能研究院',
    affiliationEn: 'Institute of Artificial Intelligence',
    photo: 'https://images.unsplash.com/photo-1544005313-94ddf0286df2?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8NHx8YXNpYW4lMjB3b21hbnxlbnwwfHwwfHx8MA%3D%3D&auto=format&fit=crop&w=500&q=60',
    category: 'faculty'
  },
  {
    id: 3,
    name: '张伟',
    nameEn: 'Wei Zhang',
    title: '助理教授',
    titleEn: 'Assistant Professor',
    degree: '博士',
    degreeEn: 'PhD',
    affiliation: '计算机科学与工程系',
    affiliationEn: 'Department of Computer Science and Engineering',
    photo: 'https://images.unsplash.com/photo-1506794778202-cad84cf45f1d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8Mnx8YXNpYW4lMjBtYW58ZW58MHx8MHx8fDA%3D&auto=format&fit=crop&w=500&q=60',
    category: 'faculty'
  },
  {
    id: 4,
    name: '王芳',
    nameEn: 'Fang Wang',
    title: '博士后研究员',
    titleEn: 'Postdoctoral Researcher',
    degree: '博士',
    degreeEn: 'PhD',
    affiliation: '人工智能研究院',
    affiliationEn: 'Institute of Artificial Intelligence',
    photo: 'https://images.unsplash.com/photo-1580489944761-15a19d654956?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8OHx8YXNpYW4lMjB3b21hbnxlbnwwfHwwfHx8MA%3D%3D&auto=format&fit=crop&w=500&q=60',
    category: 'postdoc'
  },
  {
    id: 5,
    name: '赵强',
    nameEn: 'Qiang Zhao',
    title: '博士后研究员',
    titleEn: 'Postdoctoral Researcher',
    degree: '博士',
    degreeEn: 'PhD',
    affiliation: '计算机科学与工程系',
    affiliationEn: 'Department of Computer Science and Engineering',
    photo: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8MTJ8fGFzaWFuJTIwbWFufGVufDB8fDB8fHww&auto=format&fit=crop&w=500&q=60',
    category: 'postdoc'
  },
  {
    id: 6,
    name: '钱明',
    nameEn: 'Ming Qian',
    title: '博士研究生',
    titleEn: 'PhD Student',
    degree: '硕士',
    degreeEn: 'Master',
    affiliation: '计算机科学与工程系',
    affiliationEn: 'Department of Computer Science and Engineering',
    photo: 'https://images.unsplash.com/photo-1504457047772-27faf1c00561?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8MTZ8fGFzaWFuJTIwbWFufGVufDB8fDB8fHww&auto=format&fit=crop&w=500&q=60',
    category: 'phd'
  },
  {
    id: 7,
    name: '孙丽',
    nameEn: 'Li Sun',
    title: '博士研究生',
    titleEn: 'PhD Student',
    degree: '硕士',
    degreeEn: 'Master',
    affiliation: '人工智能研究院',
    affiliationEn: 'Institute of Artificial Intelligence',
    photo: 'https://images.unsplash.com/photo-1567532939604-b6b5b0db2604?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8MTF8fGFzaWFuJTIwd29tYW58ZW58MHx8MHx8fDA%3D&auto=format&fit=crop&w=500&q=60',
    category: 'phd'
  },
  {
    id: 8,
    name: '周杰',
    nameEn: 'Jie Zhou',
    title: '硕士研究生',
    titleEn: 'Master Student',
    degree: '学士',
    degreeEn: 'Bachelor',
    affiliation: '计算机科学与工程系',
    affiliationEn: 'Department of Computer Science and Engineering',
    photo: 'https://images.unsplash.com/photo-1633332755192-727a05c4013d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8OHx8YXNpYW4lMjBtYW58ZW58MHx8MHx8fDA%3D&auto=format&fit=crop&w=500&q=60',
    category: 'master'
  },
  {
    id: 9,
    name: '吴婷',
    nameEn: 'Ting Wu',
    title: '硕士研究生',
    titleEn: 'Master Student',
    degree: '学士',
    degreeEn: 'Bachelor',
    affiliation: '人工智能研究院',
    affiliationEn: 'Institute of Artificial Intelligence',
    photo: 'https://images.unsplash.com/photo-1534751516642-a1af1ef26a56?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8MTh8fGFzaWFuJTIwd29tYW58ZW58MHx8MHx8fDA%3D&auto=format&fit=crop&w=500&q=60',
    category: 'master'
  },
  {
    id: 10,
    name: '郑阳',
    nameEn: 'Yang Zheng',
    title: '校友，现就职于谷歌',
    titleEn: 'Alumni, now at Google',
    degree: '博士',
    degreeEn: 'PhD',
    affiliation: '2018届毕业',
    affiliationEn: 'Class of 2018',
    photo: 'https://images.unsplash.com/photo-1512484776495-a09d92e87c3b?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8MTl8fGFzaWFuJTIwbWFufGVufDB8fDB8fHww&auto=format&fit=crop&w=500&q=60',
    category: 'alumni'
  },
  {
    id: 11,
    name: '冯雪',
    nameEn: 'Xue Feng',
    title: '校友，现就职于微软',
    titleEn: 'Alumni, now at Microsoft',
    degree: '博士',
    degreeEn: 'PhD',
    affiliation: '2019届毕业',
    affiliationEn: 'Class of 2019',
    photo: 'https://images.unsplash.com/photo-1597586124394-fbd6ef244026?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8MjR8fGFzaWFuJTIwd29tYW58ZW58MHx8MHx8fDA%3D&auto=format&fit=crop&w=500&q=60',
    category: 'alumni'
  },
  {
    id: 12,
    name: '陈浩',
    nameEn: 'Hao Chen',
    title: '校友，现就职于百度',
    titleEn: 'Alumni, now at Baidu',
    degree: '硕士',
    degreeEn: 'Master',
    affiliation: '2020届毕业',
    affiliationEn: 'Class of 2020',
    photo: 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8MjZ8fGFzaWFuJTIwbWFufGVufDB8fDB8fHww&auto=format&fit=crop&w=500&q=60',
    category: 'alumni'
  }
]);

// Filtered members based on selected category
const filteredMembers = computed(() => {
  if (selectedCategory.value === 'all') {
    return members.value;
  }
  return members.value.filter(member => member.category === selectedCategory.value);
});

// Setup
onMounted(() => {
  // Check for language changes
  window.addEventListener('storage', (event) => {
    if (event.key === 'language') {
      currentLang.value = event.newValue;
    }
  });
});
</script>

<style scoped>
.teams-container {
  min-height: 100vh;
  background-color: #f8f9fa;
  color: #333333;
  padding: 80px 20px 40px;
  position: relative;
  overflow-x: hidden;
}

.teams-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image:
    linear-gradient(rgba(5, 217, 232, 0.02) 1px, transparent 1px),
    linear-gradient(90deg, rgba(5, 217, 232, 0.02) 1px, transparent 1px);
  background-size: 50px 50px;
  z-index: 0;
  pointer-events: none;
}

.teams-header {
  text-align: center;
  margin-bottom: 60px;
  position: relative;
  padding: 40px 20px;
  overflow: hidden;
}

.cyberpunk-grid {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image:
    linear-gradient(rgba(5, 217, 232, 0.05) 1px, transparent 1px),
    linear-gradient(90deg, rgba(5, 217, 232, 0.05) 1px, transparent 1px);
  background-size: 20px 20px;
  z-index: 0;
  animation: gridPulse 10s infinite alternate ease-in-out;
}

.cyberpunk-glitch {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: transparent;
  z-index: 1;
  pointer-events: none;
  overflow: hidden;
}

.cyberpunk-glitch::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 42, 109, 0.1), transparent);
  animation: glitchSweep 8s infinite;
}

@keyframes glitchSweep {
  0%, 100% {
    left: -100%;
    opacity: 0;
  }
  10%, 90% {
    left: -100%;
    opacity: 0;
  }
  50% {
    left: 100%;
    opacity: 1;
  }
}

@keyframes gridPulse {
  0% {
    opacity: 0.3;
    background-size: 20px 20px;
  }
  50% {
    opacity: 0.6;
    background-size: 22px 22px;
  }
  100% {
    opacity: 0.3;
    background-size: 20px 20px;
  }
}

.cyberpunk-title {
  font-size: 3rem;
  margin-bottom: 20px;
  color: #333333;
  text-shadow: 1px 1px 3px rgba(138, 43, 226, 0.3);
  position: relative;
  display: inline-block;
  z-index: 2;
  letter-spacing: 2px;
  animation: textGlow 3s infinite alternate;
  font-weight: 600;
}

.cyberpunk-title::after {
  content: attr(data-text);
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
  color: transparent;
  text-shadow: 1px 1px 3px rgba(255, 42, 109, 0.3);
  opacity: 0;
  animation: textGlitch 8s infinite;
}

@keyframes textGlow {
  0% {
    text-shadow: 1px 1px 3px rgba(138, 43, 226, 0.3), 1px 1px 3px rgba(0, 162, 255, 0.2);
  }
  100% {
    text-shadow: 1px 1px 5px rgba(138, 43, 226, 0.5), 1px 1px 5px rgba(0, 162, 255, 0.3), 1px 1px 5px rgba(255, 42, 109, 0.2);
  }
}

@keyframes textGlitch {
  0%, 90%, 100% {
    opacity: 0;
    transform: translateX(0);
  }
  92%, 94%, 96% {
    opacity: 0.4;
    transform: translateX(2px);
  }
  93%, 95%, 97% {
    opacity: 0.4;
    transform: translateX(-2px);
  }
}

.title-decoration {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 15px 0 25px;
}

.decoration-line {
  width: 100px;
  height: 1px;
  background: linear-gradient(90deg, transparent, #05d9e8, transparent);
}

.decoration-circle {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background-color: #05d9e8;
  margin: 0 10px;
  box-shadow: 0 0 8px rgba(5, 217, 232, 0.4);
  animation: pulsate 2s infinite alternate;
}

@keyframes pulsate {
  0% {
    transform: scale(1);
    opacity: 0.7;
  }
  100% {
    transform: scale(1.2);
    opacity: 1;
  }
}

.cyberpunk-subtitle {
  font-size: 1.2rem;
  color: #666666;
  position: relative;
  z-index: 2;
  max-width: 700px;
  margin: 0 auto;
  line-height: 1.5;
  font-weight: 500;
}

.team-categories {
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
  gap: 15px;
  margin-bottom: 60px;
  position: relative;
  z-index: 2;
}

.category-item {
  padding: 10px 25px;
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 30px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.9rem;
  position: relative;
  overflow: hidden;
  border: 1px solid rgba(5, 217, 232, 0.1);
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);
  color: #666666;
}

.category-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 50%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(5, 217, 232, 0.1), transparent);
  transition: left 0.5s ease;
}

.category-item:hover {
  background-color: rgba(248, 249, 250, 0.95);
  transform: translateY(-3px);
  border-color: rgba(5, 217, 232, 0.3);
  box-shadow: 0 6px 15px rgba(0, 0, 0, 0.08), 0 0 5px rgba(5, 217, 232, 0.1);
  color: #333333;
}

.category-item:hover::before {
  left: 100%;
}

.category-item.active {
  background-color: rgba(138, 43, 226, 0.1);
  color: #333333;
  border-color: rgba(138, 43, 226, 0.3);
  box-shadow: 0 0 15px rgba(138, 43, 226, 0.1);
  font-weight: 600;
}

.category-text {
  position: relative;
  z-index: 2;
}

.team-members {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 30px;
  max-width: 1200px;
  margin: 0 auto;
  position: relative;
  z-index: 2;
}

.member-card {
  background-color: rgba(255, 255, 255, 0.95);
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  position: relative;
  border: 1px solid rgba(5, 217, 232, 0.1);
}

.member-card:hover {
  transform: translateY(-5px) scale(1.02);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1), 0 0 15px rgba(5, 217, 232, 0.1);
  border-color: rgba(5, 217, 232, 0.3);
}

.photo-frame {
  position: relative;
  overflow: hidden;
}

.member-photo img {
  width: 100%;
  height: 280px;
  object-fit: cover;
  transition: all 0.5s ease;
  filter: saturate(1.1) contrast(1.05);
}

.member-card:hover .member-photo img {
  transform: scale(1.05);
  filter: saturate(1.2) contrast(1.1);
}

.photo-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(5, 217, 232, 0.1) 0%, rgba(138, 43, 226, 0.1) 100%);
  z-index: 1;
  opacity: 0.3;
  transition: opacity 0.3s ease;
}

.member-card:hover .photo-overlay {
  opacity: 0.5;
}

.photo-glitch {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: transparent;
  z-index: 2;
  pointer-events: none;
  overflow: hidden;
}

.photo-glitch::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 42, 109, 0.2), transparent);
  animation: photoGlitch 5s infinite;
  animation-play-state: paused;
}

.member-card:hover .photo-glitch::before {
  animation-play-state: running;
}

@keyframes photoGlitch {
  0%, 100% {
    left: -100%;
    opacity: 0;
  }
  40%, 60% {
    left: 100%;
    opacity: 0.7;
  }
}

.corner-decoration {
  position: absolute;
  width: 20px;
  height: 20px;
  z-index: 3;
}

.top-left {
  top: 10px;
  left: 10px;
  border-top: 2px solid rgba(5, 217, 232, 0.5);
  border-left: 2px solid rgba(5, 217, 232, 0.5);
}

.top-right {
  top: 10px;
  right: 10px;
  border-top: 2px solid rgba(5, 217, 232, 0.5);
  border-right: 2px solid rgba(5, 217, 232, 0.5);
}

.bottom-left {
  bottom: 10px;
  left: 10px;
  border-bottom: 2px solid rgba(5, 217, 232, 0.5);
  border-left: 2px solid rgba(5, 217, 232, 0.5);
}

.bottom-right {
  bottom: 10px;
  right: 10px;
  border-bottom: 2px solid rgba(5, 217, 232, 0.5);
  border-right: 2px solid rgba(5, 217, 232, 0.5);
}

.member-info {
  padding: 20px;
  position: relative;
}

.member-name {
  font-size: 1.2rem;
  margin-bottom: 15px;
  color: #333333;
  position: relative;
  display: inline-block;
  text-shadow: 1px 1px 2px rgba(138, 43, 226, 0.1);
  font-weight: 600;
}

.info-divider {
  width: 40px;
  height: 2px;
  background: linear-gradient(90deg, #05d9e8, transparent);
  margin-bottom: 15px;
}

.member-title {
  font-size: 0.9rem;
  color: #05d9e8;
  margin-bottom: 8px;
  text-shadow: 1px 1px 2px rgba(5, 217, 232, 0.1);
  font-weight: 500;
}

.member-degree, .member-affiliation {
  font-size: 0.85rem;
  color: #666666;
  margin-bottom: 5px;
}

.card-decoration {
  position: absolute;
  bottom: 10px;
  right: 10px;
  width: 30px;
  height: 30px;
  border-right: 2px solid rgba(255, 42, 109, 0.3);
  border-bottom: 2px solid rgba(255, 42, 109, 0.3);
  opacity: 0.3;
  transition: all 0.3s ease;
}

.member-card:hover .card-decoration {
  width: 40px;
  height: 40px;
  opacity: 0.5;
}

/* Responsive styles */
@media (max-width: 768px) {
  .cyberpunk-title {
    font-size: 2.2rem;
  }

  .team-categories {
    flex-direction: column;
    align-items: center;
  }

  .category-item {
    width: 100%;
    max-width: 300px;
    text-align: center;
  }

  .decoration-line {
    width: 60px;
  }
}
</style>
