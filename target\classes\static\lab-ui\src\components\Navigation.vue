<template>
  <div class="navigation-container">
    <div class="navigation-content">
      <div class="logo-section">
        <div class="logo-item">
<!--          <img src="https://via.placeholder.com/40x40?text=CQU" alt="重庆大学" class="logo-image" />-->
          <span class="logo-text">实验室</span>
        </div>
        <div class="logo-divider"></div>
        <div class="logo-item">
<!--          <img src="https://via.placeholder.com/40x40?text=AI" alt="实验室" class="logo-image" />-->
          <span class="logo-text">门户网站</span>
        </div>
      </div>

      <div class="right-section">
        <div class="nav-links">
          <div
            v-for="item in navItems"
            :key="item.path"
            class="nav-item"
            :class="{ 'active': isActive(item.path) }"
            @click="navigateTo(item.path)"
          >
            {{ currentLang === 'zh' ? item.label : item.enLabel }}
          </div>
        </div>

        <div class="language-selector">
          <span :class="{ 'active': currentLang === 'en' }" @click="changeLang('en')">EN</span>
          <span class="lang-divider">|</span>
          <span :class="{ 'active': currentLang === 'zh' }" @click="changeLang('zh')">中</span>
        </div>

        <div class="search-icon-container" @click="toggleSearch">
          <div class="search-icon">
            <el-icon><Search /></el-icon>
          </div>
        </div>

        <div class="user-section" v-if="isLoggedIn">
          <el-dropdown trigger="click">
            <div class="user-info">
              <el-avatar
                :src="userInfo?.image || 'https://randomuser.me/api/portraits/men/1.jpg'"
                size="small"
                @error="handleAvatarError"
              ></el-avatar>
              <span class="username">{{ userInfo?.username || '用户' }}</span>
            </div>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item @click="showUserProfile">个人资料</el-dropdown-item>
                <el-dropdown-item @click="navigateTo('/messages')">
                  <div class="message-menu-item">
                    <span>私信消息</span>
                    <el-badge v-if="unreadMessageCount > 0" :value="unreadMessageCount" class="message-badge" />
                  </div>
                </el-dropdown-item>
                <el-dropdown-item divided @click="handleLogout">退出登录</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
        <div class="auth-buttons" v-else>
          <el-button size="small" type="primary" @click="navigateTo('/login')">登录</el-button>
          <el-button size="small" @click="navigateTo('/register')">注册</el-button>
        </div>

        <div class="menu-icon" @click="toggleMobileMenu">
          <i class="el-icon-menu"></i>
        </div>
      </div>
    </div>

    <!-- Mobile Menu -->
    <div class="mobile-menu" :class="{ 'active': mobileMenuOpen }">
      <div
        v-for="item in navItems"
        :key="item.path"
        class="mobile-nav-item"
        :class="{ 'active': isActive(item.path) }"
        @click="navigateTo(item.path); toggleMobileMenu()"
      >
        {{ currentLang === 'zh' ? item.label : item.enLabel }}
      </div>

      <div class="mobile-user-section" v-if="isLoggedIn">
        <div class="mobile-user-info" @click="showUserProfile">
          <el-avatar
            :src="userInfo?.image || 'https://randomuser.me/api/portraits/men/1.jpg'"
            size="small"
            @error="handleAvatarError"
          ></el-avatar>
          <span class="mobile-username">{{ userInfo?.username || '用户' }}</span>
        </div>
        <el-button size="small" type="danger" @click="handleLogout">退出登录</el-button>
      </div>
      <div class="mobile-auth-buttons" v-else>
        <el-button size="small" type="primary" @click="navigateTo('/login'); toggleMobileMenu()">登录</el-button>
        <el-button size="small" @click="navigateTo('/register'); toggleMobileMenu()">注册</el-button>
      </div>

      <div class="mobile-language">
        <span :class="{ 'active': currentLang === 'en' }" @click="changeLang('en')">English</span>
        <span :class="{ 'active': currentLang === 'zh' }" @click="changeLang('zh')">中文</span>
      </div>
    </div>

    <!-- Search Overlay -->
    <div class="search-overlay" :class="{ 'active': searchOpen }">
      <div class="close-button" @click="toggleSearch">
        <el-icon><Close /></el-icon>
      </div>
      <div class="search-container">
        <h1 class="search-title">检索你关心的内容</h1>
        <p class="search-subtitle">将在站内全部内容中搜索相关信息</p>
        <div class="search-input-container">
          <el-input
            v-model="searchQuery"
            placeholder="输入关键词搜索..."
            class="search-input"
            :prefix-icon="Search"
            @keyup.enter="performSearch"
            clearable
          >
            <template #append>
              <el-button type="primary" @click="performSearch">搜索</el-button>
            </template>
          </el-input>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { ElMessageBox, ElMessage } from 'element-plus';
import { Search, Close } from '@element-plus/icons-vue';
import { getUserInfo } from '../api/user';
import { getUnreadMessageCount as apiGetUnreadMessageCount } from '../api/message';

const route = useRoute();
const router = useRouter();

// Navigation items
const navItems = [
  { label: '首页', path: '/home', enLabel: 'Home' },
  { label: '实验室概况', path: '/labIntro', enLabel: 'About' },
  { label: '团队成员', path: '/teams', enLabel: 'Team' },
  { label: '新闻动态', path: '/news', enLabel: 'News' },
  { label: '科学研究', path: '/science', enLabel: 'Research' },
  { label: '资源中心', path: '/resources', enLabel: 'Resources' },
  { label: '在线交流', path: '/posts', enLabel: 'Forum' }
];

// Reactive state
const userInfo = ref(null);
const currentLang = ref('zh');
const mobileMenuOpen = ref(false);
const searchOpen = ref(false);
const searchQuery = ref('');
const unreadMessageCount = ref(0);

// Check if user is logged in
const isLoggedIn = computed(() => {
  return !!localStorage.getItem('token');
});

// Check if a nav item is active
const isActive = (path) => {
  if (path === '/home' && route.path === '/') {
    return true;
  }
  return route.path.startsWith(path);
};

// Navigate to a path
const navigateTo = (path) => {
  // Only navigate if we're not already on this path
  if (route.path !== path) {
    router.push(path).catch(err => {
      if (err.name !== 'NavigationDuplicated') {
        console.error('Navigation error:', err);
      }
    });
  }
};

// Toggle mobile menu
const toggleMobileMenu = () => {
  mobileMenuOpen.value = !mobileMenuOpen.value;
  if (mobileMenuOpen.value) {
    searchOpen.value = false;
  }
};

// Toggle search overlay
const toggleSearch = () => {
  searchOpen.value = !searchOpen.value;
  if (searchOpen.value) {
    mobileMenuOpen.value = false;
    // Focus the search input after a short delay to allow the animation to complete
    setTimeout(() => {
      document.querySelector('.search-input input').focus();
    }, 300);
  }
};

// Perform search
const performSearch = () => {
  if (!searchQuery.value.trim()) return;

  // Navigate to search results page with query parameter
  router.push({
    path: '/search',
    query: { q: searchQuery.value }
  });

  // Close the search overlay and reset the query
  searchQuery.value = '';
  toggleSearch();
};

// Change language
const changeLang = (lang) => {
  currentLang.value = lang;
  // Store language preference in localStorage
  localStorage.setItem('language', lang);
  // Show success message in the appropriate language
  if (lang === 'en') {
    ElMessage.success('Language switched to English');
  } else {
    ElMessage.success('语言已切换为中文');
  }
};

// Show user profile
const showUserProfile = () => {
  router.push('/user/profile');
};

// Handle logout
const handleLogout = () => {
  ElMessageBox.confirm('确定要退出登录吗?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    // Clear token and user info
    localStorage.removeItem('token');
    localStorage.removeItem('userId');
    userInfo.value = null;

    // Show success message
    ElMessage.success('已退出登录');

    // Redirect to home page
    router.push('/home');
  }).catch(() => {});
};

// Handle avatar error
const handleAvatarError = (e) => {
  console.error('Navigation: Avatar image failed to load');
  e.target.src = 'https://randomuser.me/api/portraits/men/1.jpg';
};

// Fetch user info
const fetchUserInfo = async () => {
  if (isLoggedIn.value) {
    try {
      console.log('Fetching user info...');
      const response = await getUserInfo();
      console.log('User info response:', response);

      if (response.code === 200 && response.data) {
        userInfo.value = response.data;
        console.log('User avatar URL:', response.data.image);

        // Store userId in localStorage if not already there
        if (!localStorage.getItem('userId') && response.data.id) {
          localStorage.setItem('userId', response.data.id);
          console.log('User ID stored from fetchUserInfo:', response.data.id);
        }

        // Fetch unread message count after getting user info
        await fetchUnreadMessageCount();
      } else {
        console.error('Failed to get user info, response code:', response.code);
        // If we get an error response, clear the token
        //localStorage.removeItem('token');
        //localStorage.removeItem('userId');
        userInfo.value = null;
      }
    } catch (error) {
      console.error('Failed to fetch user info:', error);
      console.error('Error details:', error.message);

      // If there's an error (like token expired), clear the token
      //localStorage.removeItem('token');
      //localStorage.removeItem('userId');
      userInfo.value = null;
    }
  } else {
    console.log('User not logged in, skipping fetchUserInfo');
  }
};

// Fetch unread message count
const fetchUnreadMessageCount = async () => {
  if (isLoggedIn.value) {
    try {
      console.log('Fetching unread message count...');
      const response = await apiGetUnreadMessageCount();
      console.log('Unread message count response:', response);

      if (response.code === 200) {
        unreadMessageCount.value = response.data || 0;
        console.log('Updated unread message count:', unreadMessageCount.value);
      } else {
        console.error('Failed to get unread message count, response code:', response.code);
      }
    } catch (error) {
      console.error('Failed to fetch unread message count:', error);
      console.error('Error details:', error.message);
    }
  } else {
    console.log('User not logged in, skipping fetchUnreadMessageCount');
  }
};

// 定义轮询间隔（毫秒）
const POLLING_INTERVAL = 30000; // 30秒
let pollingTimer = null;

// 开始轮询未读消息
const startPollingUnreadMessages = () => {
  if (pollingTimer) {
    clearInterval(pollingTimer);
  }

  // 立即执行一次
  fetchUnreadMessageCount();

  // 设置定时器定期检查
  pollingTimer = setInterval(() => {
    fetchUnreadMessageCount();
  }, POLLING_INTERVAL);

  console.log('Started polling for unread messages');
};

// 停止轮询
const stopPollingUnreadMessages = () => {
  if (pollingTimer) {
    clearInterval(pollingTimer);
    pollingTimer = null;
    console.log('Stopped polling for unread messages');
  }
};

// Fetch user info on mount
onMounted(async () => {
  try {
    // Check if language preference is stored in localStorage
    const savedLang = localStorage.getItem('language');
    if (savedLang) {
      currentLang.value = savedLang;
    }

    // Add a small delay before fetching user info to ensure token is properly set
    setTimeout(async () => {
      // Fetch user info if logged in
      if (isLoggedIn.value) {
        console.log('Token found, fetching user info...');
        await fetchUserInfo();

        // 开始轮询未读消息
        startPollingUnreadMessages();
      }
    }, 500);
  } catch (error) {
    console.error('初始化失败:', error);
  }
});

// 在组件卸载时清除轮询
onUnmounted(() => {
  stopPollingUnreadMessages();
});
</script>

<style scoped>
.navigation-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 1000;
  background-color: #ffffff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.navigation-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 70px;
  padding: 0 40px;
  width: 100%;
}

.logo-section {
  display: flex;
  align-items: center;
}

.logo-item {
  display: flex;
  align-items: center;
}

.logo-image {
  height: 40px;
  width: 40px;
  margin-right: 10px;
}

.logo-text {
  color: #333333;
  font-weight: bold;
  font-size: 20px;
  white-space: nowrap;
}

.logo-divider {
  height: 20px;
  width: 1px;
  background-color: #dddddd;
  margin: 0 15px;
}

.right-section {
  display: flex;
  align-items: center;
  gap: 15px;
}

.nav-links {
  display: flex;
  gap: 25px;
  margin-right: 15px;
}

.nav-item {
  color: #666666;
  cursor: pointer;
  padding: 5px 0;
  position: relative;
  transition: color 0.3s;
  white-space: nowrap;
  font-size: 22px;
  font-weight: 500;
}

.nav-item:hover, .nav-item.active {
  color: #333333;
}

.nav-item.active::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 100%;
  height: 2px;
  background-color: #05d9e8;
}

.language-selector {
  display: flex;
  align-items: center;
  color: #666666;
  font-size: 20px;
}

.language-selector span {
  cursor: pointer;
  transition: color 0.3s;
}

.language-selector span.active {
  color: #333333;
  font-weight: 500;
}

.lang-divider {
  margin: 0 5px;
}

.search-icon-container {
  display: flex;
  align-items: center;
  cursor: pointer;
}

.search-icon {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 40px;
  height: 40px;
  background-color: #05d9e8;
  border-radius: 4px;
  color: #ffffff;
  font-size: 22px;
  transition: all 0.3s;
  box-shadow: 0 2px 6px rgba(5, 217, 232, 0.3);
}

.search-icon:hover {
  background-color: #04c5d3;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(5, 217, 232, 0.4);
}

.menu-icon {
  color: #666666;
  cursor: pointer;
  transition: color 0.3s;
  font-size: 22px;
}

.menu-icon:hover {
  color: #333333;
}

.user-info {
  display: flex;
  align-items: center;
  cursor: pointer;
}

.username {
  margin-left: 10px;
  color: #333333;
  font-size: 20px;
  font-weight: 500;
}

.message-menu-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.message-badge :deep(.el-badge__content) {
  background-color: #1f6feb;
}

.auth-buttons {
  display: flex;
  gap: 10px;
}

.auth-buttons :deep(.el-button--primary) {
  background-color: #05d9e8;
  border-color: #05d9e8;
  color: #ffffff;
  font-size: 20px;
  font-weight: 500;
  box-shadow: 0 2px 6px rgba(5, 217, 232, 0.3);
  transition: all 0.3s;
}

.auth-buttons :deep(.el-button--primary:hover) {
  background-color: #04c5d3;
  border-color: #04c5d3;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(5, 217, 232, 0.4);
}

.auth-buttons :deep(.el-button--default) {
  background-color: #f0f0f0;
  border-color: #dddddd;
  color: #333333;
  font-size: 20px;
  font-weight: 500;
  transition: all 0.3s;
}

.auth-buttons :deep(.el-button--default:hover) {
  background-color: #e8e8e8;
  border-color: #cccccc;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* Mobile menu */
.menu-icon {
  display: none;
}

.mobile-menu {
  display: none;
  position: fixed;
  top: 70px;
  left: 0;
  width: 100%;
  background-color: #f8f9fa;
  padding: 20px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  transform: translateY(-100%);
  transition: transform 0.3s ease-in-out;
  z-index: 999;
}

.mobile-menu.active {
  transform: translateY(0);
}

.mobile-nav-item {
  padding: 15px 0;
  color: #666666;
  border-bottom: 1px solid #e0e0e0;
  cursor: pointer;
  font-size: 22px;
  font-weight: 500;
}

.mobile-nav-item.active {
  color: #333333;
}

.mobile-language {
  display: flex;
  justify-content: space-around;
  margin-top: 20px;
}

.mobile-language span {
  cursor: pointer;
  color: #666666;
  padding: 10px;
  font-size: 20px;
}

.mobile-language span.active {
  color: #333333;
  background-color: #e8f8fa;
  border-radius: 4px;
  font-weight: 500;
}

.mobile-user-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 15px;
  margin: 20px 0;
  padding: 15px;
  border-top: 1px solid #e0e0e0;
  border-bottom: 1px solid #e0e0e0;
  background-color: #f0f0f0;
  border-radius: 8px;
}

.mobile-user-info {
  display: flex;
  align-items: center;
  gap: 10px;
  cursor: pointer;
  padding: 10px;
  border-radius: 4px;
  transition: all 0.3s;
}

.mobile-user-info:hover {
  background-color: #e8f8fa;
  transform: translateY(-2px);
}

.mobile-username {
  color: #333333;
  font-size: 20px;
  font-weight: 500;
}

.mobile-auth-buttons {
  display: flex;
  justify-content: center;
  gap: 15px;
  margin: 20px 0;
  padding: 15px;
  border-top: 1px solid #e0e0e0;
  border-bottom: 1px solid #e0e0e0;
  background-color: #f0f0f0;
  border-radius: 8px;
}

.mobile-auth-buttons :deep(.el-button--primary) {
  background-color: #05d9e8;
  border-color: #05d9e8;
  color: #ffffff;
  font-size: 20px;
  font-weight: 500;
  box-shadow: 0 2px 6px rgba(5, 217, 232, 0.3);
}

.mobile-auth-buttons :deep(.el-button--default) {
  background-color: #f0f0f0;
  border-color: #dddddd;
  color: #333333;
  font-size: 20px;
  font-weight: 500;
}

/* Search overlay */
.search-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.95);
  z-index: 1001;
  display: flex;
  justify-content: center;
  align-items: center;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s, visibility 0.3s;
  backdrop-filter: blur(5px);
}

.search-overlay.active {
  opacity: 1;
  visibility: visible;
}

.search-container {
  width: 80%;
  max-width: 700px;
  text-align: center;
  padding: 30px;
  background-color: #ffffff;
  border-radius: 12px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.search-title {
  color: #333333;
  font-size: 36px;
  margin-bottom: 10px;
  font-weight: 600;
}

.search-subtitle {
  color: #666666;
  font-size: 20px;
  margin-bottom: 30px;
}

.search-input-container {
  width: 100%;
  margin: 0 auto;
}

.close-button {
  position: absolute;
  top: 20px;
  right: 20px;
  cursor: pointer;
  color: #333333;
  font-size: 24px;
  transition: all 0.3s;
  width: 40px;
  height: 40px;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: rgba(255, 255, 255, 0.8);
  border-radius: 50%;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.close-button:hover {
  color: #05d9e8;
  transform: rotate(90deg);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Element Plus overrides */
:deep(.el-input__inner) {
  background-color: #ffffff;
  border-color: #dddddd;
  color: #333333;
  height: 50px;
  font-size: 20px;
}

:deep(.el-input__prefix) {
  color: #666666;
}

.search-input-container :deep(.el-input__inner) {
  background-color: #ffffff;
  border-color: #e0e0e0;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.search-input-container :deep(.el-input-group__append) {
  background-color: #05d9e8;
  border-color: #05d9e8;
  color: #fff;
  padding: 0 20px;
}

.search-input-container :deep(.el-button) {
  background-color: #05d9e8;
  border-color: #05d9e8;
  color: #fff;
  font-size: 18px;
  font-weight: 500;
}

.search-input-container :deep(.el-button:hover) {
  background-color: #04c5d3;
  border-color: #04c5d3;
}

/* Responsive styles */
@media (max-width: 1200px) {
  .nav-links {
    gap: 20px;
  }

  .logo-text {
    font-size: 18px;
  }

  .nav-item {
    font-size: 18px;
  }
}

@media (max-width: 992px) {
  .right-section .nav-links {
    display: none;
  }

  .menu-icon {
    display: block;
  }

  .mobile-menu {
    display: block;
  }
}

@media (max-width: 768px) {
  .logo-section {
    flex-direction: column;
    align-items: flex-start;
  }

  .logo-divider {
    display: none;
  }

  .language-selector {
    display: none;
  }

  .username {
    display: none;
  }

  .search-container {
    width: 90%;
    padding: 15px;
  }

  .search-title {
    font-size: 24px;
  }

  .search-subtitle {
    font-size: 14px;
    margin-bottom: 20px;
  }

  .close-button {
    top: 10px;
    right: 10px;
  }
}
</style>
