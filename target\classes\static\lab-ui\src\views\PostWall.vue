<template>
  <div class="post-wall-container">
    <!-- Cyberpunk background elements -->
    <div class="cyberpunk-elements">
      <div class="cyberpunk-overlay"></div>
      <div class="cyberpunk-grid"></div>
      <div class="cyberpunk-glitch-effect"></div>
    </div>

    <div class="post-wall-header">
      <div class="title-container">
        <h1>在线交流</h1>
        <p class="cyberpunk-subtitle">分享研究成果，交流学术见解</p>
      </div>

      <div class="category-filter">
        <el-radio-group v-model="selectedCategory" @change="handleCategoryChange">
          <el-radio-button :label="null">全部</el-radio-button>
          <el-radio-button :label="0">新闻</el-radio-button>
          <el-radio-button :label="1">设备</el-radio-button>
          <el-radio-button :label="2">师生</el-radio-button>
          <el-radio-button :label="3">生活</el-radio-button>
        </el-radio-group>
      </div>
    </div>

    <div class="post-grid">
      <div v-for="post in posts" :key="post.id" class="post-card" @click="viewPostDetail(post.id)">
        <div class="post-image">
          <img :src="post.coverUrl || `https://picsum.photos/id/${(parseInt(post.id) % 10) + 1}/400/300`" :alt="post.content" />
          <div class="image-overlay"></div>
          <div class="cyberpunk-card-border"></div>
        </div>
        <div class="post-content">
          <p class="post-text">{{ post.title }}</p>
          <div class="post-info">
            <div class="user-info">
              <img
                :src="post.userBasicVO?.image || 'https://randomuser.me/api/portraits/men/1.jpg'"
                :alt="post.userBasicVO?.username || '匿名用户'"
                class="user-avatar"
                @error="handleImageError"
              />
              <span class="username">{{ post.userBasicVO?.username || '匿名用户' }}</span>
            </div>
            <div class="post-stats">
              <span class="like-count">
                <div class="heart-icon"></div> {{ post.likeCount || 0 }}
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="load-more">
      <el-button v-if="hasMorePosts" @click="loadMorePosts" :loading="loading">加载更多</el-button>
      <p v-else class="no-more-posts">没有更多内容了</p>
    </div>

    <div class="create-post-fab" @click="goToCreatePost" v-if="isLoggedIn">
      <el-button type="primary" circle>
        <i class="el-icon-plus"></i>
      </el-button>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue';
import { useRouter } from 'vue-router';
import { ElMessage } from 'element-plus';
import { getPosts } from '../api/post';

// Reactive state
const posts = ref([]);
const loading = ref(false);
const hasMorePosts = ref(true);
const selectedCategory = ref(null);
const isLoggedIn = ref(!!localStorage.getItem('token'));
const router = useRouter();
const lastPostId = ref(0);

// Load initial posts
onMounted(() => {
  fetchPosts();
});

// Format date for display
const formatDate = (date) => {
  if (!date) return '';
  return new Date(date).toLocaleDateString('zh-CN');
};

// Fetch posts from API
const fetchPosts = async () => {
  try {
    loading.value = true;
    const response = await getPosts(selectedCategory.value, lastPostId.value);

    if (response.code === 200 && response.data) {
      const newPosts = response.data.postVOList || [];

      if (newPosts.length === 0) {
        hasMorePosts.value = false;
      } else {
        posts.value = [...posts.value, ...newPosts];
        lastPostId.value = response.data.lastPostId;
      }
    } else {
      ElMessage.error('获取帖子列表失败');
    }
  } catch (error) {
    console.error('获取帖子列表失败:', error);
    ElMessage.error('获取帖子列表失败，请稍后重试');
  } finally {
    loading.value = false;
  }
};

// Handle category change
const handleCategoryChange = (category) => {
  selectedCategory.value = category;
  posts.value = [];
  lastPostId.value = 0;
  hasMorePosts.value = true;
  fetchPosts();
};

// Load more posts
const loadMorePosts = () => {
  if (lastPostId.value === 1 || lastPostId.value === 2 || lastPostId.value === 3) {
    hasMorePosts.value = false;
    ElMessage.info('没有更多数据了');
    return;
  }
  fetchPosts();
};

// View post detail
const viewPostDetail = (postId) => {
  router.push(`/post/detail/${postId}`);
};

// Go to create post page
const goToCreatePost = () => {
  router.push('/post/create');
};

// Handle image loading errors
const handleImageError = (e) => {
  e.target.src = 'https://randomuser.me/api/portraits/men/1.jpg';
};
</script>

<style scoped>
.post-wall-container {
  min-height: 100vh;
  background-color: #f8f9fa;
  color: #333333;
  padding: 20px;
  padding-top: 80px; /* Space for fixed navigation */
  position: relative;
  overflow: hidden;
}

/* Cyberpunk background elements */
.cyberpunk-elements {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 0;
  pointer-events: none;
}

.cyberpunk-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.7) 0%, rgba(240, 240, 240, 0.5) 100%);
  z-index: 1;
  animation: overlayPulse 8s infinite alternate;
}

.cyberpunk-grid {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image:
    linear-gradient(rgba(5, 217, 232, 0.05) 1px, transparent 1px),
    linear-gradient(90deg, rgba(5, 217, 232, 0.05) 1px, transparent 1px);
  background-size: 40px 40px;
  z-index: 2;
  animation: gridPulse 15s infinite;
}

.cyberpunk-glitch-effect {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: transparent;
  z-index: 3;
  pointer-events: none;
  overflow: hidden;
}

.cyberpunk-glitch-effect::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 42, 109, 0.1), transparent);
  animation: glitchSweep 10s infinite;
}

@keyframes overlayPulse {
  0% {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.7) 0%, rgba(240, 240, 240, 0.5) 100%);
  }
  100% {
    background: linear-gradient(135deg, rgba(248, 249, 250, 0.6) 0%, rgba(233, 236, 239, 0.4) 100%);
  }
}

@keyframes gridPulse {
  0% {
    opacity: 0.2;
    background-size: 40px 40px;
  }
  50% {
    opacity: 0.3;
    background-size: 42px 42px;
  }
  100% {
    opacity: 0.2;
    background-size: 40px 40px;
  }
}

@keyframes glitchSweep {
  0%, 100% {
    left: -100%;
    opacity: 0;
  }
  10%, 90% {
    left: -100%;
    opacity: 0;
  }
  50% {
    left: 100%;
    opacity: 1;
  }
}

.post-wall-header {
  text-align: center;
  margin-bottom: 30px;
  position: relative;
  z-index: 4;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.title-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 15px;
  position: relative;
}

.post-wall-header h1 {
  font-size: 2.8rem;
  margin-bottom: 10px;
  color: #333333;
  text-shadow: 1px 1px 3px rgba(5, 217, 232, 0.3);
  position: relative;
  display: inline-block;
  font-weight: 600;
}

.cyberpunk-subtitle {
  font-size: 1.3rem;
  color: #05d9e8;
  margin-bottom: 25px;
  letter-spacing: 2px;
  position: relative;
  display: inline-block;
  text-shadow: 1px 1px 2px rgba(5, 217, 232, 0.2);
  font-weight: 400;
}

.cyberpunk-subtitle::after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 50%;
  transform: translateX(-50%);
  width: 70%;
  height: 1px;
  background: linear-gradient(90deg, transparent, #05d9e8, transparent);
  animation: lineScan 4s infinite;
}

@keyframes lineScan {
  0%, 100% {
    width: 0;
    opacity: 0;
  }
  50% {
    width: 70%;
    opacity: 1;
  }
}

.category-filter {
  margin: 10px 0 20px;
  position: relative;
  z-index: 4;
  text-align: center;
}

.post-grid {
  display: grid;
  grid-template-columns: repeat(4, minmax(250px, 280px));
  gap: 20px;
  margin-bottom: 40px;
  justify-content: center;
  position: relative;
  z-index: 4;
}

.post-card {
  background-color: rgba(255, 255, 255, 0.95);
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  transition: all 0.3s;
  cursor: pointer;
  position: relative;
  height: 100%;
  display: flex;
  flex-direction: column;
  border: 1px solid rgba(5, 217, 232, 0.1);
}

.post-card:hover {
  transform: translateY(-5px) scale(1.02);
  box-shadow: 0 8px 20px rgba(5, 217, 232, 0.2);
  border-color: rgba(5, 217, 232, 0.3);
}

.post-card:hover .cyberpunk-card-border::before {
  opacity: 1;
}

.post-image {
  position: relative;
  width: 100%;
  height: 0;
  padding-bottom: 75%; /* 4:3 aspect ratio */
  overflow: hidden;
}

.post-image img {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: all 0.5s;
  filter: saturate(1.1) contrast(1.05);
}

.post-card:hover .post-image img {
  transform: scale(1.05);
  filter: saturate(1.2) contrast(1.1) brightness(1.05);
}

.image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(255, 42, 109, 0.1) 0%, rgba(5, 217, 232, 0.1) 100%);
  opacity: 0;
  transition: opacity 0.3s;
  z-index: 2;
}

.post-card:hover .image-overlay {
  opacity: 1;
}

.cyberpunk-card-border {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 3;
}

.cyberpunk-card-border::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border: 2px solid transparent;
  border-image: linear-gradient(45deg, #ff2a6d, #05d9e8);
  border-image-slice: 1;
  opacity: 0;
  transition: opacity 0.3s;
}

.post-content {
  padding: 15px;
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  position: relative;
  z-index: 2;
  background-color: rgba(255, 255, 255, 0.95);
}

.post-text {
  margin: 0 0 12px;
  font-size: 20px;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  color: #333333;
  flex-grow: 1;
  text-shadow: 1px 1px 2px rgba(5, 217, 232, 0.1);
  font-weight: 500;
}

.post-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: auto;
  border-top: 1px solid rgba(5, 217, 232, 0.1);
  padding-top: 10px;
}

.user-info {
  display: flex;
  align-items: center;
}

.user-avatar {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  margin-right: 8px;
  object-fit: cover;
  border: 1px solid rgba(5, 217, 232, 0.2);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.username {
  font-size: 15px;
  color: #05d9e8;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100px;
  font-weight: 500;
}

.post-stats {
  display: flex;
  align-items: center;
  color: #ff2a6d;
  font-size: 12px;
}

.like-count {
  display: flex;
  align-items: center;
  gap: 4px;
  color: #ff2a6d;
  font-size: 12px;
  font-weight: 500;
}

.heart-icon {
  display: inline-block;
  width: 14px;
  height: 12px;
  margin-right: 4px;
  position: relative;
}

.heart-icon:before,
.heart-icon:after {
  content: "";
  position: absolute;
  top: 0;
  width: 7px;
  height: 12px;
  border-radius: 7px 7px 0 0;
  background: #ff2a6d;
  box-shadow: 0 0 5px rgba(255, 42, 109, 0.7);
}

.heart-icon:before {
  left: 0;
  transform: rotate(-45deg);
  transform-origin: 100% 100%;
}

.heart-icon:after {
  left: 7px;
  transform: rotate(45deg);
  transform-origin: 0 100%;
}

.load-more {
  display: flex;
  justify-content: center;
  margin: 30px 0;
  position: relative;
  z-index: 4;
}

.no-more-posts {
  color: #05d9e8;
  font-size: 14px;
  text-align: center;
  text-shadow: 1px 1px 2px rgba(5, 217, 232, 0.2);
  font-weight: 500;
}

.create-post-fab {
  position: fixed;
  bottom: 30px;
  right: 30px;
  z-index: 10;
}

/* Element Plus overrides */
:deep(.el-radio-button__inner) {
  background-color: rgba(255, 255, 255, 0.9);
  border-color: rgba(5, 217, 232, 0.2);
  color: #05d9e8;
  font-weight: 500;
}

:deep(.el-radio-button__original-radio:checked + .el-radio-button__inner) {
  background-color: rgba(5, 217, 232, 0.1);
  border-color: #05d9e8;
  color: #05d9e8;
  box-shadow: -1px 0 0 0 #05d9e8;
}

:deep(.el-button) {
  background-color: #05d9e8;
  border-color: #05d9e8;
  color: #ffffff;
  position: relative;
  overflow: hidden;
  font-weight: 500;
  box-shadow: 0 2px 6px rgba(5, 217, 232, 0.3);
}

:deep(.el-button:hover) {
  background-color: #04c5d3;
  border-color: #04c5d3;
  box-shadow: 0 4px 10px rgba(5, 217, 232, 0.4);
  transform: translateY(-2px);
}
</style>
