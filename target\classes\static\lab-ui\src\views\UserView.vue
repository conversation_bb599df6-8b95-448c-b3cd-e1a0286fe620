<template>
  <div class="user-view-container">
    <div class="profile-header">
      <div class="user-basic-info">
        <div class="avatar-section">
          <el-avatar :size="120" :src="userInfo.avatar || 'https://randomuser.me/api/portraits/men/1.jpg'"></el-avatar>
        </div>
        <div class="user-info-section">
          <h1 class="username">{{ userInfo.username || '加载中...' }}</h1>
          <p class="signature">{{ userInfo.signature || '这个用户很懒，还没有设置个性签名' }}</p>
          <div class="user-stats">
            <div class="stat-item">
              <div class="stat-value">{{ userStats.postCount || 0 }}</div>
              <div class="stat-label">发布</div>
            </div>
            <div class="stat-item">
              <div class="stat-value">{{ userStats.likeCount || 0 }}</div>
              <div class="stat-label">获赞</div>
            </div>
            <div class="stat-item">
              <div class="stat-value">{{ userStats.followCount || 0 }}</div>
              <div class="stat-label">关注</div>
            </div>
          </div>
        </div>
      </div>
      <div class="profile-actions">
        <el-button type="primary" @click="followUser" v-if="!isCurrentUser">
          {{ isFollowing ? '已关注' : '关注' }}
        </el-button>
        <el-button type="default" @click="sendMessage" v-if="!isCurrentUser && isLoggedIn">
          发送私信
        </el-button>
      </div>
    </div>

    <div class="content-tabs">
      <el-tabs v-model="activeTab">
        <el-tab-pane label="发布" name="posts">
          <div class="user-posts">
            <div v-if="loading" class="loading-container">
              <el-skeleton :rows="5" animated />
            </div>
            <div v-else-if="userPosts.length > 0" class="posts-grid">
              <div v-for="post in userPosts" :key="post.id" class="post-card" @click="viewPostDetail(post)">
                <div class="post-image" v-if="post.coverUrl">
                  <img :src="post.coverUrl" alt="帖子封面" />
                </div>
                <div class="post-content">
                  <div class="post-title">{{ post.title }}</div>
                  <div class="post-date">{{ post.date }}</div>
                </div>
              </div>
            </div>
            <div v-else class="no-posts">
              <el-empty description="暂无帖子"></el-empty>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { ElMessage } from 'element-plus';
import { getUserBasicInfo, followUser as apiFollowUser, cancelFollowUser, isFollowedUser } from '../api/user';
import { getUserPosts } from '../api/post';

const router = useRouter();
const route = useRoute();
const userId = ref(null);
const userInfo = ref({});
const userStats = reactive({
  postCount: 0,
  likeCount: 0,
  followCount: 0
});
const userPosts = ref([]);
const loading = ref(true);
const activeTab = ref('posts');
const isFollowing = ref(false);

// Check if user is logged in
const isLoggedIn = computed(() => {
  return !!localStorage.getItem('token');
});

// Check if this is the current user's profile
const isCurrentUser = computed(() => {
  const currentUserId = localStorage.getItem('userId');
  return currentUserId && currentUserId === userId.value?.toString();
});

// 格式化日期
const formatDate = (dateStr) => {
  if (!dateStr) return '未知';
  const date = new Date(dateStr);
  return date.toLocaleDateString('zh-CN').replace(/\//g, '-');
};

// 获取用户信息
const fetchUserInfo = async () => {
  try {
    console.log('UserView: Fetching user info for ID:', userId.value);

    const response = await getUserBasicInfo(userId.value);
    console.log('UserView: User info response:', response);

    if (response.code === 200 && response.data) {
      userInfo.value = response.data;

      // 填充统计数据
      userStats.postCount = response.data.postCount || 0;
      userStats.likeCount = response.data.likeCount || 0;
      userStats.followCount = response.data.followCount || 0;
    } else {
      console.error('UserView: Failed to get user info, response code:', response.code);
      ElMessage.error('获取用户信息失败');
    }
  } catch (error) {
    console.error('UserView: Failed to fetch user info:', error);
    console.error('UserView: Error details:', error.message);
    ElMessage.error('获取用户信息失败，请稍后重试');
  }
};

// 获取用户帖子
const fetchUserPosts = async () => {
  try {
    const response = await getUserPosts(userId.value);
    console.log('UserView: User posts response:', response);

    if (response.code === 200 && response.data) {
      userPosts.value = response.data.postVOList.map(post => ({
        id: post.id,
        title: post.title || post.content.split('\n')[0] || '无标题',
        date: formatDate(post.createdAt),
        coverUrl: post.coverUrl
      }));
    } else {
      console.error('UserView: Failed to get user posts, response code:', response.code);
    }
  } catch (error) {
    console.error('UserView: Failed to fetch user posts:', error);
    ElMessage.error('获取用户帖子失败，请稍后重试');
  } finally {
    loading.value = false;
  }
};

// 检查关注状态
const checkFollowStatus = async () => {
  if (!isLoggedIn.value || isCurrentUser.value) {
    return;
  }

  try {
    const response = await isFollowedUser(userId.value);
    if (response.code === 200) {
      isFollowing.value = response.data;
    }
  } catch (error) {
    console.error('Failed to check follow status:', error);
  }
};

// 关注用户
const followUser = async () => {
  if (!isLoggedIn.value) {
    ElMessage.warning('请先登录后再关注用户');
    router.push('/login');
    return;
  }

  try {
    if (isFollowing.value) {
      // 取消关注
      const response = await cancelFollowUser(userId.value);
      console.log('Cancel follow response:', response);

      // Check both response.data and response directly
      if (response.code === 200) {
        isFollowing.value = false;
        ElMessage.success('已取消关注');

        // 更新统计数据
        if (userStats.followCount > 0) {
          userStats.followCount--;
        }
      } else {
        ElMessage.error(response.message || response.msg || '取消关注失败');
      }
    } else {
      // 关注用户
      const response = await apiFollowUser(userId.value);
      console.log('Follow user response:', response);

      // Check both response.data and response directly
      if (response.code === 200) {
        isFollowing.value = true;
        ElMessage.success('关注成功');

        // 更新统计数据
        userStats.followCount++;
      } else {
        ElMessage.error(response.message || response.msg || '关注失败');
      }
    }
  } catch (error) {
    console.error('Follow operation failed:', error);
    console.error('Error details:', error.message, error.response);
    ElMessage.error('操作失败，请稍后重试');
  }
};

// 发送私信
const sendMessage = () => {
  if (!isLoggedIn.value) {
    ElMessage.warning('请先登录后再发送私信');
    router.push('/login');
    return;
  }

  // Navigate to messages page with this user pre-selected
  router.push({
    path: '/messages',
    query: { userId: userId.value }
  });
};

// 查看帖子详情
const viewPostDetail = (post) => {
  // Always navigate to the post detail page when clicking on a post in search results
  router.push(`/post/detail/${post.id}`);

  // Log the navigation for debugging
  console.log(`Navigating to post detail page for post ID: ${post.id}, category: ${post.category}`);
};

// 生命周期钩子
onMounted(async () => {
  console.log('UserView: Component mounted');

  // Get user ID from route params
  userId.value = route.params.id;

  if (!userId.value) {
    console.error('UserView: No user ID provided');
    ElMessage.error('未找到用户ID');
    router.push('/home');
    return;
  }

  try {
    console.log('UserView: Fetching user data...');
    // 获取用户信息和帖子
    await fetchUserInfo();
    await fetchUserPosts();

    // 检查关注状态
    await checkFollowStatus();

    console.log('UserView: Data fetched successfully');
  } catch (error) {
    console.error('UserView: Failed to initialize user view:', error);
    console.error('UserView: Error details:', error.message);
    ElMessage.error('加载用户资料失败，请稍后重试');
  }
});
</script>

<style scoped>
.user-view-container {
  max-width: 1200px;
  margin: 80px auto 40px;
  padding: 0 20px;
  color: #333333;
}

.profile-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 40px;
  background-color: #ffffff;
  border-radius: 10px;
  padding: 30px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(31, 111, 235, 0.1);
}

.user-basic-info {
  display: flex;
  align-items: center;
}

.avatar-section {
  margin-right: 30px;
}

.user-info-section {
  display: flex;
  flex-direction: column;
}

.username {
  font-size: 2rem;
  margin: 0 0 10px 0;
  color: #333333;
  font-weight: 600;
}

.signature {
  color: #666666;
  margin-bottom: 20px;
  font-size: 1rem;
}

.user-stats {
  display: flex;
  gap: 30px;
}

.stat-item {
  text-align: center;
}

.stat-value {
  font-size: 1.5rem;
  font-weight: bold;
  color: #1f6feb;
}

.stat-label {
  font-size: 0.9rem;
  color: #666666;
}

.profile-actions {
  display: flex;
  gap: 10px;
}

.content-tabs {
  background-color: #ffffff;
  border-radius: 10px;
  padding: 20px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(31, 111, 235, 0.1);
}

.posts-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 20px;
  margin-top: 20px;
}

.post-card {
  background-color: #f8f9fa;
  border-radius: 8px;
  overflow: hidden;
  cursor: pointer;
  transition: transform 0.3s, box-shadow 0.3s;
  height: 100%;
  display: flex;
  flex-direction: column;
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.post-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
  border-color: rgba(31, 111, 235, 0.2);
}

.post-image {
  height: 150px;
  overflow: hidden;
}

.post-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.post-content {
  padding: 15px;
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  background-color: #ffffff;
}

.post-title {
  font-weight: bold;
  margin-bottom: 10px;
  font-size: 1rem;
  color: #333333;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.post-date {
  color: #666666;
  font-size: 0.8rem;
  margin-top: auto;
}

.loading-container {
  padding: 20px;
}

.no-posts {
  padding: 40px 0;
  text-align: center;
}

/* Element Plus overrides */
:deep(.el-tabs__item) {
  color: #666666;
  font-size: 16px;
}

:deep(.el-tabs__item.is-active) {
  color: #1f6feb;
  font-weight: 500;
}

:deep(.el-tabs__active-bar) {
  background-color: #1f6feb;
}

:deep(.el-tabs__nav-wrap::after) {
  background-color: #e0e0e0;
}

:deep(.el-button--primary) {
  background-color: #1f6feb;
  border-color: #1f6feb;
  color: #ffffff;
  font-weight: 500;
  box-shadow: 0 2px 6px rgba(31, 111, 235, 0.3);
  transition: all 0.3s;
}

:deep(.el-button--primary:hover) {
  background-color: #0d47a1;
  border-color: #0d47a1;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(31, 111, 235, 0.4);
}

:deep(.el-button--default) {
  background-color: #f0f0f0;
  border-color: #dddddd;
  color: #333333;
  font-weight: 500;
  transition: all 0.3s;
}

:deep(.el-button--default:hover) {
  background-color: #e8e8e8;
  border-color: #cccccc;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

@media (max-width: 768px) {
  .profile-header {
    flex-direction: column;
  }

  .profile-actions {
    margin-top: 20px;
    align-self: flex-end;
  }

  .user-basic-info {
    flex-direction: column;
    text-align: center;
  }

  .avatar-section {
    margin-right: 0;
    margin-bottom: 20px;
  }

  .user-stats {
    justify-content: center;
  }
}
</style>
