import request from '../utils/request';

/**
 * 检查是否是好友关系（互相关注）
 * @param {String} userId - 用户ID
 * @returns {Promise} - 返回是否是好友
 */
export function checkIfFriend(userId) {
  console.log('API: Checking if friend with userId:', userId);
  return request.get('/message/check-friend', {
    params: { userId }
  })
    .then(response => {
      console.log('API: Check if friend response:', response);
      return response;
    })
    .catch(error => {
      console.error('API: Check if friend error:', error);
      throw error;
    });
}

/**
 * 获取已发送给某人的消息数量
 * @param {String} receiverId - 接收者ID
 * @returns {Promise} - 返回消息数量
 */
export function getMessageCount(receiverId) {
  console.log('API: Getting message count for receiverId:', receiverId);
  return request.get('/message/count', {
    params: { receiverId }
  })
    .then(response => {
      console.log('API: Get message count response:', response);
      return response;
    })
    .catch(error => {
      console.error('API: Get message count error:', error);
      throw error;
    });
}

/**
 * 发送私信
 * @param {Object} messageData - 消息数据
 * @param {Number} messageData.receiverId - 接收者ID
 * @param {String} messageData.content - 消息内容
 * @param {Number} messageData.messageType - 消息类型 0-普通消息 1-朋友分享 2-系统消息
 * @returns {Promise} - 返回发送结果
 */
export function sendMessage(messageData) {
  console.log('API: Sending message to user ID:', messageData.receiverId);
  return request.post('/message/send', messageData)
    .then(response => {
      console.log('API: Send message response:', response);
      return response;
    })
    .catch(error => {
      console.error('API: Send message error:', error);
      throw error;
    });
}

/**
 * 获取与某人的历史消息
 * @param {String} friendId - 好友ID
 * @param {String} lastMessageId - 最后一条消息ID，用于分页
 * @returns {Promise} - 返回消息列表
 */
export function getHistoryMessages(friendId, lastMessageId = '0') {
  console.log(`API: Getting messages with friendId: ${friendId}, lastMessageId: ${lastMessageId}`);
  return request.get('/message/list', {
    params: {
      friendId,
      lastMessageId
    }
  })
    .then(response => {
      console.log('API: Get history messages response:', response);
      return response;
    })
    .catch(error => {
      console.error('API: Get history messages error:', error);
      throw error;
    });
}

/**
 * 获取最近联系人列表
 * @returns {Promise} - 返回联系人列表
 */
export function getRecentContacts() {
  console.log('API: Getting recent contacts');
  return request.get('/message/contacts')
    .then(response => {
      console.log('API: Get recent contacts response:', response);
      return response;
    })
    .catch(error => {
      console.error('API: Get recent contacts error:', error);
      throw error;
    });
}

/**
 * 获取未读消息数
 * @returns {Promise} - 返回未读消息数
 */
export function getUnreadMessageCount() {
  console.log('API: Getting unread message count');
  return request.get('/message/unread/count')
    .then(response => {
      console.log('API: Get unread count response:', response);
      return response;
    })
    .catch(error => {
      console.error('API: Get unread count error:', error);
      throw error;
    });
}

/**
 * 标记与某人的所有消息为已读
 * @param {String} friendId - 好友ID
 * @returns {Promise} - 返回是否成功
 */
export function markMessagesAsRead(friendId) {
  console.log('API: Marking messages as read for friendId:', friendId);
  return request.post('/message/read', null, {
    params: {
      friendId
    }
  })
    .then(response => {
      console.log('API: Mark as read response:', response);
      return response;
    })
    .catch(error => {
      console.error('API: Mark as read error:', error);
      throw error;
    });
}
