<template>
  <div class="science-container">
    <!-- Cyberpunk background elements -->
    <div class="cyberpunk-overlay"></div>
    <div class="cyberpunk-grid"></div>
    <div class="cyberpunk-scanline"></div>
    <div class="cyberpunk-glitch-effect"></div>

    <div class="science-header">
      <h1 class="glitch-text" data-text="研究方向">研究方向</h1>
      <div class="science-subtitle">探索人工智能的前沿领域</div>
      <div class="header-line"></div>
    </div>

    <div class="research-areas">
      <div
        v-for="(area, index) in researchAreas"
        :key="index"
        class="research-area"
        @click="showResearchDetail(area)"
      >
        <div class="research-icon">
          <i :class="area.icon"></i>
        </div>
        <div class="research-title">{{ area.title }}</div>
        <div class="research-description">{{ area.description }}</div>
        <div class="research-area-glow"></div>
      </div>
    </div>

    <!-- Research Detail Dialog -->
    <el-dialog
      v-model="showDetail"
      :title="selectedArea ? selectedArea.title : ''"
      width="70%"
      class="research-detail-dialog"
      destroy-on-close
      :append-to-body="false"
      :modal="true"
      :close-on-click-modal="true"
      :show-close="true"
      @closed="handleDialogClosed"
    >
      <div class="research-detail-content" v-if="selectedArea">
        <div class="research-detail-header">
          <div class="researcher-info">
            <img :src="getResearcherAvatar(selectedArea.title)" alt="Researcher" class="researcher-avatar">
            <div class="researcher-details">
              <div class="researcher-name">{{ getResearcherName(selectedArea.title) }}</div>
              <div class="researcher-title">研究方向负责人</div>
            </div>
          </div>
          <div class="research-date">更新日期: {{ getCurrentDate() }}</div>
        </div>

        <div class="research-detail-body">
          <div class="research-detail-text">
            <h3>研究背景</h3>
            <p>{{ selectedArea.background || generateBackground(selectedArea.title) }}</p>

            <h3>研究内容</h3>
            <p>{{ selectedArea.content || generateContent(selectedArea.title) }}</p>

            <h3>应用前景</h3>
            <p>{{ selectedArea.applications || generateApplications(selectedArea.title) }}</p>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount, watch } from 'vue';
import { useRouter, useRoute } from 'vue-router';

// 研究领域数据
const researchAreas = [
  {
    icon: 'el-icon-cpu',
    title: '机器学习与深度学习',
    description: '研究先进的机器学习算法和深度学习模型，解决复杂场景下的智能决策问题。'
  },
  {
    icon: 'el-icon-data-analysis',
    title: '大数据分析与挖掘',
    description: '开发高效的大数据处理技术，从海量数据中提取有价值的信息和知识。'
  },
  {
    icon: 'el-icon-monitor',
    title: '计算机视觉',
    description: '研究图像识别、目标检测、场景理解等视觉智能技术，赋能智能监控、自动驾驶等领域。'
  },
  {
    icon: 'el-icon-chat-dot-round',
    title: '自然语言处理',
    description: '探索语言理解与生成技术，推动智能对话、机器翻译、文本分析等应用发展。'
  },
  {
    icon: 'el-icon-connection',
    title: '知识图谱与推理',
    description: '构建结构化知识体系，实现智能推理与决策，支持智能问答和知识服务。'
  },
  {
    icon: 'el-icon-mobile',
    title: '智能交互技术',
    description: '研究人机交互新模式，打造更自然、高效、智能的交互体验。'
  },
  {
    icon: 'el-icon-cpu',
    title: '强化学习',
    description: '研究智能体通过与环境交互学习最优策略的方法，应用于游戏、机器人控制等领域。'
  },
  {
    icon: 'el-icon-data-analysis',
    title: '联邦学习',
    description: '探索在保护数据隐私的前提下，多方协作训练AI模型的技术，促进数据安全与AI发展的平衡。'
  },
  {
    icon: 'el-icon-monitor',
    title: '多模态学习',
    description: '研究融合视觉、语言、声音等多种模态信息的AI技术，实现更全面的智能感知与理解。'
  }
];

// 状态变量
const showDetail = ref(false);
const selectedArea = ref(null);

// 获取路由参数
const router = useRouter();
const route = useRoute();

// 显示研究详情
const showResearchDetail = (area) => {
  // Prevent duplicate dialogs by checking if we're already showing this area
  if (showDetail.value && selectedArea.value && selectedArea.value.title === area.title) {
    return;
  }

  // First close any existing dialog to prevent duplication
  showDetail.value = false;
  selectedArea.value = null;

  // Small timeout to ensure dialog is fully closed before opening a new one
  setTimeout(() => {
    selectedArea.value = area;
    showDetail.value = true;

    // Update URL with the selected area without reloading the page
    const areaId = encodeURIComponent(area.title);
    router.replace({
      path: route.path,
      query: { area: areaId }
    }).catch(err => {
      if (err.name !== 'NavigationDuplicated') {
        console.error('Navigation error:', err);
      }
    });
  }, 100);
};

// 检查URL参数，如果有area参数，则自动显示对应的研究领域详情
onMounted(() => {
  // Reset dialog state on mount to prevent duplicates
  showDetail.value = false;
  selectedArea.value = null;

  // Check for URL parameters after a short delay
  setTimeout(() => {
    const areaParam = route.query.area;
    if (areaParam) {
      // Find the research area that matches the URL parameter
      const decodedAreaTitle = decodeURIComponent(areaParam);
      const matchedArea = researchAreas.find(area => area.title === decodedAreaTitle);

      if (matchedArea) {
        // Directly set values instead of using showResearchDetail to avoid duplicate dialogs
        selectedArea.value = matchedArea;
        showDetail.value = true;
      }
    }
  }, 300);
});

// Close dialog when navigating away
watch(() => route.path, (newPath) => {
  if (showDetail.value) {
    showDetail.value = false;
  }
});

// Clean up when component is unmounted
onBeforeUnmount(() => {
  if (showDetail.value) {
    showDetail.value = false;
  }
});

// Handle dialog closed event
const handleDialogClosed = () => {
  // Clear the area parameter from the URL when dialog is closed
  if (route.query.area) {
    router.replace({
      path: route.path,
      query: {}
    }).catch(err => {
      if (err.name !== 'NavigationDuplicated') {
        console.error('Navigation error:', err);
      }
    });
  }
};

// 获取当前日期
const getCurrentDate = () => {
  return new Date().toLocaleDateString('zh-CN');
};

// 获取研究者头像
const getResearcherAvatar = (title) => {
  // 根据研究方向返回不同的头像
  const hash = title.length % 10 + 20;
  return `https://randomuser.me/api/portraits/${hash % 2 === 0 ? 'men' : 'women'}/${hash}.jpg`;
};

// 获取研究者姓名
const getResearcherName = (title) => {
  const names = ['张教授', '李研究员', '王博士', '刘教授', '陈研究员', '林博士'];
  return names[title.length % names.length];
};



// 生成研究背景
const generateBackground = (title) => {
  return `${title}是人工智能领域的重要研究方向，近年来随着计算能力的提升和算法的创新，该领域取得了显著进展。我们实验室在该方向的研究始于2015年，经过多年的积累，已形成了完整的理论体系和技术路线。目前，我们的研究团队由5名教授、10名博士后和20余名研究生组成，致力于解决该领域的前沿科学问题和关键技术挑战。`;
};

// 生成研究内容
const generateContent = (title) => {
  return `我们的研究主要集中在以下几个方面：\n\n1. 理论基础：探索${title}的数学基础和理论框架，提出新的模型和算法。\n\n2. 算法优化：针对现有算法的局限性，开发更高效、更鲁棒的新型算法。\n\n3. 应用研究：将${title}技术应用于实际场景，解决实际问题。\n\n4. 跨学科融合：结合其他学科知识，拓展${title}的应用边界。\n\n我们已在国际顶级期刊和会议上发表论文50余篇，获得国家和省部级科研项目10余项，申请专利30余项。`;
};

// 生成应用前景
const generateApplications = (title) => {
  return `${title}技术具有广阔的应用前景，主要体现在以下几个方面：\n\n1. 智能制造：提升生产效率和产品质量，降低成本。\n\n2. 医疗健康：辅助疾病诊断、药物研发和个性化治疗。\n\n3. 智慧城市：优化城市管理和服务，提升市民生活质量。\n\n4. 金融服务：风险评估、智能投顾和反欺诈等。\n\n5. 教育培训：个性化学习和智能教学辅助。\n\n未来，随着技术的不断成熟和应用场景的拓展，${title}将在更多领域发挥重要作用，创造更大的社会价值和经济效益。`;
};


</script>

<style scoped>
.science-container {
  min-height: 100vh;
  background-color: #f8f9fa;
  color: #666666;
  padding: 20px;
  padding-top: 80px; /* Space for fixed navigation */
  position: relative;
  z-index: 1; /* Ensure it's below the navigation bar */
  overflow: hidden;
}

/* Cyberpunk background elements */
.cyberpunk-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.7) 0%, rgba(240, 240, 240, 0.5) 100%);
  z-index: -2;
  animation: overlayPulse 8s infinite alternate;
}

.cyberpunk-grid {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image:
    linear-gradient(rgba(5, 217, 232, 0.05) 1px, transparent 1px),
    linear-gradient(90deg, rgba(5, 217, 232, 0.05) 1px, transparent 1px);
  background-size: 40px 40px;
  z-index: -1;
  animation: gridPulse 15s infinite;
}

.cyberpunk-scanline {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: repeating-linear-gradient(
    to bottom,
    transparent 0%,
    rgba(5, 217, 232, 0.02) 0.5%,
    transparent 1%
  );
  z-index: 0;
  pointer-events: none;
}

.cyberpunk-glitch-effect {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: transparent;
  z-index: 0;
  pointer-events: none;
  overflow: hidden;
}

.cyberpunk-glitch-effect::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(138, 43, 226, 0.1), transparent);
  animation: glitchSweep 10s infinite;
}

.science-header {
  max-width: 1200px;
  margin: 0 auto 60px;
  text-align: center;
  position: relative;
  z-index: 2;
}

.science-header h1 {
  font-size: 3rem;
  margin-bottom: 15px;
  color: #05d9e8;
  text-shadow: 1px 1px 3px rgba(5, 217, 232, 0.3);
  position: relative;
  display: inline-block;
  font-weight: 600;
}

.glitch-text {
  position: relative;
}

.glitch-text::before,
.glitch-text::after {
  content: attr(data-text);
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0.4;
}

.glitch-text::before {
  color: #ff2a6d;
  z-index: -1;
  animation: glitch-animation 3s infinite linear alternate-reverse;
}

.glitch-text::after {
  color: #8a2be2;
  z-index: -2;
  animation: glitch-animation 2s infinite linear alternate;
}

.header-line {
  width: 150px;
  height: 3px;
  background: linear-gradient(90deg, transparent, #05d9e8, transparent);
  margin: 0 auto 30px;
  position: relative;
}

.header-line::before {
  content: '';
  position: absolute;
  top: -3px;
  left: 0;
  width: 100%;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(138, 43, 226, 0.3), transparent);
}

.science-subtitle {
  font-size: 1.3rem;
  color: #666666;
  margin-bottom: 30px;
  letter-spacing: 1px;
  font-weight: 500;
}

.research-areas {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 25px;
  max-width: 1200px;
  margin: 0 auto;
  position: relative;
  z-index: 2;
}

.research-area {
  background-color: rgba(255, 255, 255, 0.95);
  border-radius: 8px;
  padding: 30px;
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  cursor: pointer;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(5, 217, 232, 0.1);
  position: relative;
  overflow: hidden;
}

.research-area::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background:
    linear-gradient(90deg, rgba(5, 217, 232, 0.02) 1px, transparent 1px),
    linear-gradient(rgba(5, 217, 232, 0.02) 1px, transparent 1px);
  background-size: 20px 20px;
  z-index: 0;
}

.research-area-glow {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle at center, rgba(5, 217, 232, 0.05) 0%, transparent 70%);
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: 0;
}

.research-area:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: 0 8px 25px rgba(5, 217, 232, 0.15);
  border-color: rgba(5, 217, 232, 0.3);
}

.research-area:hover .research-area-glow {
  opacity: 1;
}

.research-icon {
  font-size: 2.8rem;
  margin-bottom: 20px;
  color: #05d9e8;
  text-shadow: 1px 1px 3px rgba(5, 217, 232, 0.2);
  position: relative;
  z-index: 1;
  transition: all 0.3s ease;
}

.research-area:hover .research-icon {
  transform: scale(1.1);
  color: #05d9e8;
  text-shadow: 1px 1px 5px rgba(5, 217, 232, 0.4);
}

.research-title {
  font-size: 1.3rem;
  font-weight: bold;
  margin-bottom: 15px;
  color: #333333;
  position: relative;
  z-index: 1;
  transition: color 0.3s ease;
}

.research-area:hover .research-title {
  color: #05d9e8;
}

.research-description {
  font-size: 0.95rem;
  color: #666666;
  line-height: 1.6;
  position: relative;
  z-index: 1;
}

/* Research Detail Dialog Styles */
:deep(.research-detail-dialog .el-dialog) {
  background-color: rgba(255, 255, 255, 0.98);
  border-radius: 10px;
  color: #333333;
  max-width: 900px;
  z-index: 999; /* Ensure dialog is below navigation */
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(5, 217, 232, 0.1);
  overflow: hidden;
}

:deep(.research-detail-dialog .el-dialog__header) {
  padding: 20px;
  border-bottom: 1px solid rgba(5, 217, 232, 0.1);
  position: relative;
  background-color: rgba(248, 249, 250, 0.9);
}

:deep(.research-detail-dialog .el-dialog__header)::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(5, 217, 232, 0.3), transparent);
}

:deep(.research-detail-dialog .el-dialog__title) {
  color: #05d9e8;
  font-size: 1.6rem;
  text-shadow: 1px 1px 2px rgba(5, 217, 232, 0.2);
  font-weight: 600;
}

:deep(.research-detail-dialog .el-dialog__headerbtn .el-dialog__close) {
  color: #05d9e8;
}

:deep(.research-detail-dialog .el-dialog__body) {
  padding: 25px;
  background:
    linear-gradient(90deg, rgba(5, 217, 232, 0.01) 1px, transparent 1px),
    linear-gradient(rgba(5, 217, 232, 0.01) 1px, transparent 1px);
  background-size: 20px 20px;
}

.research-detail-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 25px;
  padding-bottom: 15px;
  border-bottom: 1px solid rgba(5, 217, 232, 0.1);
}

.researcher-info {
  display: flex;
  align-items: center;
}

.researcher-avatar {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  margin-right: 15px;
  border: 2px solid rgba(5, 217, 232, 0.2);
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
}

.researcher-name {
  font-weight: bold;
  margin-bottom: 5px;
  color: #333333;
  font-size: 1.1rem;
}

.researcher-title {
  font-size: 0.9rem;
  color: #05d9e8;
  letter-spacing: 0.5px;
}

.research-date {
  font-size: 0.9rem;
  color: #666666;
  background-color: rgba(5, 217, 232, 0.05);
  padding: 5px 10px;
  border-radius: 4px;
  border: 1px solid rgba(5, 217, 232, 0.1);
}

.research-detail-body {
  margin-bottom: 30px;
}

.research-detail-text {
  margin-bottom: 30px;
  background-color: rgba(255, 255, 255, 0.9);
  padding: 25px;
  border-radius: 10px;
  border: 1px solid rgba(5, 217, 232, 0.1);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
  position: relative;
  overflow: hidden;
}

.research-detail-text::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background:
    linear-gradient(90deg, rgba(5, 217, 232, 0.01) 1px, transparent 1px),
    linear-gradient(rgba(5, 217, 232, 0.01) 1px, transparent 1px);
  background-size: 20px 20px;
  z-index: 0;
  pointer-events: none;
}

.research-detail-text::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(5, 217, 232, 0.02) 0%, rgba(138, 43, 226, 0.02) 100%);
  pointer-events: none;
  z-index: 0;
}

.research-detail-text h3 {
  color: #05d9e8;
  margin: 20px 0 15px;
  font-size: 1.3rem;
  position: relative;
  display: inline-block;
  text-shadow: 1px 1px 2px rgba(5, 217, 232, 0.1);
  z-index: 1;
  font-weight: 600;
}

.research-detail-text h3:first-child {
  margin-top: 0;
}

.research-detail-text h3::after {
  content: '';
  position: absolute;
  bottom: -5px;
  left: 0;
  width: 100%;
  height: 1px;
  background: linear-gradient(90deg, #05d9e8, transparent);
}

.research-detail-text p {
  line-height: 1.8;
  margin-bottom: 20px;
  white-space: pre-line;
  color: #666666;
  position: relative;
  z-index: 1;
}






/* Animations */
@keyframes glitch-animation {
  0% {
    transform: translate(0);
  }
  20% {
    transform: translate(-2px, 2px);
  }
  40% {
    transform: translate(-2px, -2px);
  }
  60% {
    transform: translate(2px, 2px);
  }
  80% {
    transform: translate(2px, -2px);
  }
  100% {
    transform: translate(0);
  }
}

@keyframes overlayPulse {
  0% {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.7) 0%, rgba(240, 240, 240, 0.5) 100%);
  }
  100% {
    background: linear-gradient(135deg, rgba(248, 249, 250, 0.6) 0%, rgba(233, 236, 239, 0.4) 100%);
  }
}

@keyframes gridPulse {
  0% {
    opacity: 0.3;
    background-size: 40px 40px;
  }
  50% {
    opacity: 0.5;
    background-size: 42px 42px;
  }
  100% {
    opacity: 0.3;
    background-size: 40px 40px;
  }
}

@keyframes glitchSweep {
  0%, 100% {
    left: -100%;
    opacity: 0;
  }
  10%, 90% {
    left: -100%;
    opacity: 0;
  }
  50% {
    left: 100%;
    opacity: 1;
  }
}

/* Responsive styles */
@media (max-width: 768px) {
  .research-areas {
    grid-template-columns: 1fr;
  }

  .science-header h1 {
    font-size: 2.2rem;
  }

  .science-subtitle {
    font-size: 1rem;
  }
}
</style>