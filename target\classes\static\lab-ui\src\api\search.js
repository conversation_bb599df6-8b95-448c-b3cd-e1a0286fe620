import request from '@/utils/request';

/**
 * 全站搜索
 * @param {String} keyword - 搜索关键词
 * @param {Number} page - 页码（从0开始）
 * @param {Number} size - 每页大小
 * @returns {Promise} - 返回搜索结果
 */
export function search(keyword, page = 0, size = 10) {
  console.log('Searching for:', keyword);
  return request.get('/search', {
    params: {
      keyword,
      page,
      size
    },
    headers: {
      'Content-Type': 'application/json',
      'token': localStorage.getItem('token') || '',
      'userId': localStorage.getItem('userId') || ''
    },
    timeout: 30000 // Increase timeout to 30 seconds
  });
}

/**
 * 搜索用户
 * @param {String} keyword - 搜索关键词
 * @param {Number} page - 页码（从0开始）
 * @param {Number} size - 每页大小
 * @returns {Promise} - 返回用户搜索结果
 */
export function searchUsers(keyword, page = 0, size = 10) {
  return request.get('/search/users', {
    params: {
      keyword,
      page,
      size
    },
    headers: {
      'Content-Type': 'application/json',
      'token': localStorage.getItem('token') || '',
      'userId': localStorage.getItem('userId') || ''
    },
    timeout: 30000 // Increase timeout to 30 seconds
  });
}

/**
 * 搜索帖子
 * @param {String} keyword - 搜索关键词
 * @param {Number} page - 页码（从0开始）
 * @param {Number} size - 每页大小
 * @returns {Promise} - 返回帖子搜索结果
 */
export function searchPosts(keyword, page = 0, size = 10) {
  return request.get('/search/posts', {
    params: {
      keyword,
      page,
      size
    },
    headers: {
      'Content-Type': 'application/json',
      'token': localStorage.getItem('token') || '',
      'userId': localStorage.getItem('userId') || ''
    },
    timeout: 30000 // Increase timeout to 30 seconds
  });
}

/**
 * 简单测试搜索接口
 * @returns {Promise} - 返回测试结果
 */
export function testSearch() {
  console.log('Testing search API...');
  return request.get('/search/simple-test', {
    headers: {
      'Content-Type': 'application/json',
      'token': localStorage.getItem('token') || '',
      'userId': localStorage.getItem('userId') || ''
    },
    timeout: 30000 // Increase timeout to 30 seconds
  });
}
