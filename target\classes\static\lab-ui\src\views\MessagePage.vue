<template>
  <div class="message-page-container">
    <!-- 赛博朋克风格背景元素 -->
    <div class="cyberpunk-container">
      <div class="cyberpunk-overlay"></div>
      <div class="cyberpunk-background"></div>
      <div class="cyberpunk-grid"></div>
      <div class="cyberpunk-scanline"></div>
      <div class="cyberpunk-glitch-effect"></div>
    </div>

    <div class="message-header">
      <h1>私信消息</h1>
      <p>与其他用户进行私密交流</p>
    </div>

    <div class="message-content">
      <div class="contact-list">
        <div class="contact-search">
          <el-input
            v-model="searchQuery"
            placeholder="搜索联系人"
            prefix-icon="Search"
            clearable
            class="cyberpunk-input"
          />
          <el-button type="primary" @click="showNewMessageDialog" class="cyberpunk-button">
            <el-icon><Plus /></el-icon> 新建私信
          </el-button>
        </div>

        <div class="contact-items">
          <el-empty v-if="loading" description="加载中..." />

          <div
            v-else-if="filteredContacts.length > 0"
            v-for="contact in filteredContacts"
            :key="contact.id"
            class="contact-item"
            :class="{ active: selectedContact && selectedContact.id === contact.id }"
            @click="selectContact(contact)"
          >
            <el-avatar :src="contact.avatar" :size="40">{{ contact.username.charAt(0) }}</el-avatar>
            <div class="contact-info">
              <div class="contact-name">{{ contact.username }}</div>
              <div class="contact-preview">{{ getLastMessage(contact) }}</div>
            </div>
            <div class="contact-meta">
              <div class="contact-time">{{ contact.lastMessageTime || '' }}</div>
              <div v-if="contact.unreadCount > 0" class="unread-badge">{{ contact.unreadCount }}</div>
            </div>
          </div>

          <div v-else-if="!loading && contacts.length === 0" class="no-contacts">
            <p>暂无联系人，点击"新建私信"开始聊天</p>
            <el-button type="primary" @click="showNewMessageDialog" class="cyberpunk-button mt-10">
              <el-icon><Plus /></el-icon> 新建私信
            </el-button>
            <el-button type="info" @click="createTestMessage" class="cyberpunk-button mt-10 ml-10">
              创建测试消息
            </el-button>
          </div>

          <div v-else-if="!loading && contacts.length > 0 && filteredContacts.length === 0" class="no-contacts">
            没有找到匹配的联系人
          </div>
        </div>
      </div>

      <div class="message-chat">
        <template v-if="selectedContact">
          <!-- Chat header with user info -->
          <div class="chat-header">
            <div class="chat-title">{{ selectedContact.username }}</div>
            <div class="chat-subtitle" v-if="selectedContact.signature">{{ selectedContact.signature }}</div>

            <!-- 非好友关系警告 -->
            <div v-if="!isFriend" class="friend-status-warning">
              <el-alert
                title="消息限制提醒"
                type="info"
                :closable="false"
                show-icon
                class="cyberpunk-alert"
              >
                <template #default>
                  <div>
                    <p>您与该用户不是互相关注关系，最多只能发送3条消息</p>
                    <p v-if="messageCount > 0">已发送: {{ messageCount }}/3</p>
                  </div>
                </template>
              </el-alert>
            </div>
          </div>

          <!-- Message content area - takes up all available space -->
          <div class="chat-content-wrapper">
            <!-- Messages area with scrolling -->
            <div class="chat-messages" ref="messageContainer">
              <div v-if="loadingMessages" class="loading-messages">
                <el-icon class="loading-icon" :size="24"><Loading /></el-icon>
                <span>加载消息中...</span>
              </div>

              <!-- 当没有消息时显示欢迎界面 -->
              <div v-else-if="messages.length === 0" class="empty-messages">
                <div class="welcome-message">
                  <div class="welcome-icon">
                    <el-icon :size="48"><ChatDotRound /></el-icon>
                  </div>
                  <div class="welcome-text">
                    <p>您与 {{ selectedContact ? selectedContact.username : '该用户' }} 的聊天记录将显示在这里</p>
                    <p v-if="!isFriend" class="friend-limit-note">
                      您与该用户不是互相关注的关系，最多只能发送3条消息
                    </p>
                    <p class="start-chat-hint">暂无消息记录，发送一条消息开始聊天吧！</p>
                  </div>
                </div>
              </div>

              <!-- 当有消息时显示消息列表 -->
              <div v-else class="messages-container" style="display: flex !important; flex-direction: column; width: 100%;">
                <div class="load-more" v-if="lastMessageId !== '0'" @click="loadMoreMessages">
                  加载更多消息
                </div>

                <!-- Message list -->
                <div
                  v-for="message in messages"
                  :key="message.messageId"
                  class="message-item"
                  :class="{ 'message-self': message.senderId === currentUserId.toString() }"
                  style="display: flex; margin-bottom: 10px; width: auto; max-width: 90%;"
                >
                  <el-avatar
                    :src="message.senderId === currentUserId.toString() ? currentUserAvatar : selectedContact.avatar"
                    :size="24"
                  >
                    {{ (message.senderId === currentUserId.toString() ? currentUsername : selectedContact.username).charAt(0) }}
                  </el-avatar>

                  <div class="message-bubble"
                    :class="{
                      'message-system': message.senderId === 'system' || message.messageType === 2,
                      'message-sending': message.status === 0,
                      'message-sent': message.status === 1,
                      'message-failed': message.status === 2,
                      'message-retrying': message.status === 3
                    }"
                    style="display: inline-block; min-height: 30px; padding: 8px 12px; margin: 0 8px; position: relative;"
                  >
                    <div class="message-text" style="font-size: 0.85rem; line-height: 1.4; word-break: break-word; white-space: normal;">{{ message.messageContent }}</div>
                    <div class="message-time-container">
                      <div class="message-time">{{ formatTime(message.createTime) }}</div>
                      <div v-if="message.senderId === currentUserId.toString()" class="message-status">
                        <el-icon v-if="message.status === 0" class="status-icon sending"><Loading /></el-icon>
                        <el-icon v-else-if="message.status === 1" class="status-icon sent"><Check /></el-icon>
                        <div v-else-if="message.status === 2" class="status-failed-container">
                          <el-icon class="status-icon failed"><Close /></el-icon>
                          <el-button
                            type="danger"
                            size="small"
                            class="retry-button"
                            @click="retryMessage(message)"
                          >
                            重试
                          </el-button>
                        </div>
                        <el-icon v-else-if="message.status === 3" class="status-icon retrying"><RefreshRight /></el-icon>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Input area with fixed height -->
            <div class="chat-input">
              <el-input
                v-model="newMessage"
                type="textarea"
                :rows="3"
                placeholder="输入消息..."
                resize="none"
                @keyup.enter.ctrl="sendMessage"
                class="cyberpunk-input"
                :disabled="!isFriend && messageCount >= 3"
              />
              <div class="input-actions">
                <span class="hint">按 Ctrl+Enter 发送</span>
                <el-button
                  type="primary"
                  @click="sendMessage"
                  :disabled="!newMessage.trim() || sendingMessage || (!isFriend && messageCount >= 3)"
                  :loading="sendingMessage"
                  class="cyberpunk-button"
                >
                  发送
                </el-button>
                <span v-if="!isFriend && messageCount >= 3" class="limit-reached">
                  已达到消息上限，请互相关注后继续聊天
                </span>
              </div>
            </div>
          </div>
        </template>

        <div v-else class="no-chat-selected">
          <el-empty description="选择一个联系人开始聊天" />
        </div>
      </div>
    </div>

    <!-- 新建私信对话框 -->
    <el-dialog
      v-model="newMessageDialogVisible"
      title="新建私信"
      width="500px"
      class="cyberpunk-dialog"
    >
      <el-form :model="newMessageForm" label-width="80px">
        <el-form-item label="接收者ID">
          <el-input
            v-model.number="newMessageForm.receiverId"
            placeholder="请输入接收者ID"
            class="cyberpunk-input"
          />
          <div class="form-hint">输入要发送私信的用户ID</div>
        </el-form-item>
        <el-form-item label="消息类型">
          <el-select v-model="newMessageForm.messageType" placeholder="请选择消息类型" class="cyberpunk-input">
            <el-option :value="0" label="普通消息" />
            <el-option :value="1" label="朋友分享" />
            <el-option :value="2" label="系统消息" />
          </el-select>
          <div class="form-hint">互相关注的用户可以无限发送消息，非互关用户最多发送3条</div>
        </el-form-item>
        <el-form-item label="消息内容">
          <el-input
            v-model="newMessageForm.content"
            type="textarea"
            :rows="4"
            placeholder="请输入消息内容"
            class="cyberpunk-input"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="newMessageDialogVisible = false" class="cyberpunk-button-cancel">取消</el-button>
          <el-button
            type="primary"
            @click="createNewMessage"
            :disabled="!isNewMessageFormValid"
            class="cyberpunk-button"
          >
            发送
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, nextTick, watch } from 'vue';
import { useRoute } from 'vue-router';
import { ElMessage, ElNotification } from 'element-plus';
import { Plus, Search, Loading, Check, Close, RefreshRight, ChatDotRound } from '@element-plus/icons-vue';
import { getUserInfo } from '../api/user';
import {
  sendMessage as apiSendMessage,
  getHistoryMessages,
  getRecentContacts,
  markMessagesAsRead,
  getUnreadMessageCount,
  checkIfFriend,
  getMessageCount
} from '../api/message';

// 当前用户数据
const currentUserId = ref('');
const currentUsername = ref('');
const currentUserAvatar = ref('');
const loading = ref(true);
const isFriend = ref(false);
const messageCount = ref(0);

// 联系人列表
const contacts = ref([]);

// 消息缓存 - 用于保存每个联系人的消息，避免切换时重新加载
const contactMessagesCache = {};

// 搜索和筛选
const searchQuery = ref('');
const filteredContacts = computed(() => {
  if (!searchQuery.value) return contacts.value;
  return contacts.value.filter(contact =>
    contact.username.toLowerCase().includes(searchQuery.value.toLowerCase())
  );
});

// 选中的联系人
const selectedContact = ref(null);
const selectContact = async (contact) => {
  // 确保contact对象有效
  if (!contact || !contact.id) {
    console.error('Invalid contact object:', contact);
    return;
  }

  // 如果已经选中了这个联系人，不做任何操作
  if (selectedContact.value && selectedContact.value.id === contact.id) {
    console.log('Contact already selected, skipping:', contact.id);
    return;
  }

  console.log('Selecting contact:', contact);
  console.log('Contact ID type:', typeof contact.id);

  // 保存当前消息列表状态，以便在切换回来时恢复
  if (selectedContact.value) {
    console.log('Saving message state for previous contact:', selectedContact.value.id);
    // 使用联系人ID作为键，保存消息列表和最后消息ID
    const previousContactId = selectedContact.value.id;
    contactMessagesCache[previousContactId] = {
      messages: [...messages.value],
      lastMessageId: lastMessageId.value
    };
    console.log(`Cached ${messages.value.length} messages for contact ${previousContactId}`);
  }

  // 更新选中的联系人
  selectedContact.value = contact;
  loadingMessages.value = true; // 显示加载状态

  // 确保消息区域可见
  nextTick(() => {
    // 强制重新渲染消息区域
    if (messageContainer.value) {
      console.log('Message container found, ensuring visibility');
      messageContainer.value.style.display = 'flex';
      messageContainer.value.style.flexDirection = 'column';
    }
  });

  // 检查是否有缓存的消息
  if (contactMessagesCache[contact.id]) {
    console.log('Found cached messages for contact:', contact.id);
    messages.value = contactMessagesCache[contact.id].messages;
    lastMessageId.value = contactMessagesCache[contact.id].lastMessageId;
    console.log(`Restored ${messages.value.length} messages from cache`);

    // 即使有缓存，也要检查是否有新消息，但使用更长的延迟以避免重复加载
    setTimeout(() => {
      // 检查消息是否已经加载，避免重复刷新
      if (messages.value.length > 0) {
        console.log('Messages already loaded, skipping refresh');
      } else {
        refreshMessages(contact.id);
      }
    }, 1000);
  } else {
    console.log('No cached messages found, loading from server');
    // 如果没有缓存，重置消息状态并从服务器加载
    lastMessageId.value = '0';
    messages.value = []; // 清空消息列表
  }

  try {
    // 检查是否是好友关系（互相关注）
    const friendResponse = await checkIfFriend(contact.id);
    console.log('Friend check response:', friendResponse);
    isFriend.value = friendResponse.data;

    // 如果不是好友，获取已发送消息数量
    if (!isFriend.value) {
      const countResponse = await getMessageCount(contact.id);
      console.log('Message count response:', countResponse);
      messageCount.value = countResponse.data;
    } else {
      messageCount.value = 0;
    }

    // 如果没有缓存消息，从服务器加载
    if (!contactMessagesCache[contact.id]) {
      console.log('Loading messages from server for contact:', contact.id);
      try {
        // 确保使用字符串ID
        const contactIdStr = contact.id.toString();
        console.log('Loading messages with contactIdStr:', contactIdStr);

        // 直接调用API而不是通过loadMessages函数，以确保完全控制流程
        const response = await getHistoryMessages(contactIdStr, '0');
        console.log('Direct API message response:', response);

        if (response && response.code === 200 && response.data) {
          if (response.data.messageList && response.data.messageList.length > 0) {
            console.log(`Received ${response.data.messageList.length} messages directly from API`);

            // 确保消息按时间顺序排列，并且去重
            const uniqueMessages = removeDuplicateMessages(response.data.messageList);
            console.log(`Filtered ${response.data.messageList.length} messages to ${uniqueMessages.length} unique messages`);

            messages.value = uniqueMessages.sort((a, b) => {
              const timeA = new Date(a.createTime).getTime();
              const timeB = new Date(b.createTime).getTime();
              return timeA - timeB; // 升序排列，旧消息在前
            });

            lastMessageId.value = response.data.lastMessageId || '0';

            // 更新缓存
            contactMessagesCache[contactIdStr] = {
              messages: [...messages.value],
              lastMessageId: lastMessageId.value
            };

            console.log(`Cached ${messages.value.length} messages for contact ${contactIdStr}`);
          } else {
            console.log('No messages found in direct API response');
            messages.value = [];
          }
        } else {
          console.warn('Invalid response from direct API call:', response);
          messages.value = [];
        }
      } catch (error) {
        console.error('Error in direct API call:', error);
        // 回退到原始方法
        await loadMessages(contact.id);
      }

      console.log('Messages loaded, count:', messages.value.length);
    }

    // 确保消息区域可见
    nextTick(() => {
      console.log('Ensuring message container is visible after loading messages');
      if (messageContainer.value) {
        messageContainer.value.style.display = 'flex';
        messageContainer.value.style.flexDirection = 'column';
      }
      // 滚动到底部
      scrollToBottom();
    });

    // 标记消息为已读
    try {
      const readResponse = await markMessagesAsRead(contact.id);
      console.log('Mark as read response:', readResponse);

      // 更新未读消息数
      if (contact.unreadCount) {
        contact.unreadCount = 0;
      }
    } catch (readError) {
      console.error('Error marking messages as read:', readError);
    }
  } catch (error) {
    console.error('Error in selectContact:', error);
    console.error('Error details:', error.message, error.response);
    ElMessage.error('加载聊天记录失败，请稍后重试');
    isFriend.value = false;
    messageCount.value = 0;

    // 确保即使出错也显示空消息状态
    if (messages.value.length === 0) {
      console.log('Setting empty messages array due to error');
    }
  } finally {
    loadingMessages.value = false;
  }
};

// 消息列表
const messages = ref([]);
const messageContainer = ref(null);
const lastMessageId = ref('0');
const loadingMessages = ref(false);

// 加载消息
const loadMessages = async (friendId) => {
  if (loadingMessages.value) return;

  loadingMessages.value = true;
  try {
    // 确保friendId是字符串类型
    const friendIdStr = friendId.toString();
    console.log('Loading messages for friendId:', friendIdStr, 'Type:', typeof friendIdStr, 'lastMessageId:', lastMessageId.value);

    // 添加错误处理和重试逻辑
    let retryCount = 0;
    const maxRetries = 3; // 增加重试次数
    let success = false;
    let response;

    while (!success && retryCount <= maxRetries) {
      try {
        if (retryCount > 0) {
          console.log(`Retry attempt ${retryCount} for loading messages...`);
        }

        response = await getHistoryMessages(friendIdStr, lastMessageId.value);
        console.log('Message response:', response);

        // 检查是否有错误消息表明Redis序列化问题
        if (response && response.code === 500 && response.message &&
            (response.message.includes('序列化') ||
             response.message.includes('serialization') ||
             response.message.includes('序列') ||
             response.message.includes('查询失败'))) {
          console.warn('Detected Redis serialization error, retrying...');
          retryCount++;
          await new Promise(resolve => setTimeout(resolve, 1500)); // 等待更长时间
          continue; // 跳过当前循环迭代，尝试重试
        }

        success = true;
      } catch (retryError) {
        retryCount++;
        console.error('Error details in retry:', retryError.message, retryError.response);

        if (retryCount <= maxRetries) {
          console.warn(`Error loading messages, retrying (${retryCount}/${maxRetries})...`, retryError);
          // 指数退避策略 - 每次重试等待时间更长
          await new Promise(resolve => setTimeout(resolve, 1000 * retryCount));
        } else {
          throw retryError; // Re-throw if all retries failed
        }
      }
    }

    if (response && response.code === 200) {
      // 确保response.data存在，即使为空对象也可以
      if (response.data) {
        console.log('Response data structure:', JSON.stringify(response.data));

        // 检查messageList是否存在
        if (!response.data.messageList) {
          console.error('messageList is missing in response data:', response.data);
          ElMessage.warning('消息列表格式错误，请联系管理员');
          messages.value = [];
          return;
        }

        const newMessages = response.data.messageList || [];
        console.log('New messages received:', newMessages.length);
        console.log('Message sample:', newMessages.length > 0 ? JSON.stringify(newMessages[0]) : 'No messages');

        // 如果是首次加载，直接替换消息列表
        if (lastMessageId.value === '0') {
          // 确保消息按时间顺序排列，旧消息在上，新消息在下，并且去重
          const uniqueMessages = removeDuplicateMessages(newMessages);
          console.log(`Filtered ${newMessages.length} messages to ${uniqueMessages.length} unique messages`);

          messages.value = uniqueMessages.sort((a, b) => {
            const timeA = new Date(a.createTime).getTime();
            const timeB = new Date(b.createTime).getTime();
            return timeA - timeB; // 升序排列，旧消息在前
          });
          console.log('Messages after assignment:', messages.value.length);

          // 调试消息显示问题
          console.log('Message sample after assignment:',
            messages.value.length > 0 ?
            JSON.stringify(messages.value[0]) :
            'No messages');

          // 检查DOM元素
          setTimeout(() => {
            const messagesContainer = document.querySelector('.messages-container');
            const messageItems = document.querySelectorAll('.message-item');
            console.log('Messages container found:', !!messagesContainer);
            console.log('Message items found:', messageItems.length);

            if (messagesContainer) {
              console.log('Messages container display:', window.getComputedStyle(messagesContainer).display);
              console.log('Messages container visibility:', window.getComputedStyle(messagesContainer).visibility);
              console.log('Messages container height:', window.getComputedStyle(messagesContainer).height);
            }
          }, 100);
        } else {
          // 加载更多历史消息时，将新消息添加到列表顶部
          // 确保新加载的消息也是按时间顺序排列
          const sortedNewMessages = newMessages.sort((a, b) => {
            const timeA = new Date(a.createTime).getTime();
            const timeB = new Date(b.createTime).getTime();
            return timeA - timeB; // 升序排列，旧消息在前
          });
          messages.value = [...sortedNewMessages, ...messages.value];
          console.log('Messages after append:', messages.value.length);
        }

        lastMessageId.value = response.data.lastMessageId || '0';
        console.log('Updated lastMessageId:', lastMessageId.value);

        // 滚动到底部
        await nextTick();
        scrollToBottom();
      } else {
        console.warn('Response data is null or undefined:', response);
        // 如果是首次加载，显示一个空消息状态
        if (lastMessageId.value === '0') {
          messages.value = [];
          console.log('Set empty messages array due to null response data');
        }
      }
    } else {
      // 处理服务器返回的错误
      console.error('Server returned error for message loading:', response);

      // 如果是首次加载，显示一个空消息状态
      if (lastMessageId.value === '0') {
        // 清空消息列表，让空状态显示
        messages.value = [];
        console.log('Set empty messages array due to error response');
      }

      if (response) {
        // 检查是否是Redis序列化错误
        if (response.code === 500 && response.message &&
            (response.message.includes('序列化') ||
             response.message.includes('serialization') ||
             response.message.includes('序列') ||
             response.message.includes('查询失败'))) {

          console.warn('Redis serialization error detected in response');

          // 尝试从数据库直接获取消息
          ElMessage({
            message: '正在尝试从数据库获取消息...',
            type: 'info',
            duration: 2000
          });

          // 延迟一点时间后重新尝试加载消息
          setTimeout(async () => {
            try {
              console.log('Retrying message load after Redis error');
              const retryResponse = await getHistoryMessages(friendIdStr, lastMessageId.value);

              if (retryResponse && retryResponse.code === 200 && retryResponse.data) {
                console.log('Successfully retrieved messages after Redis error');

                // 处理成功获取的消息
                const newMessages = retryResponse.data.messageList || [];

                if (newMessages.length > 0) {
                  // 确保消息按时间顺序排列，并且去重
                  const uniqueMessages = removeDuplicateMessages(newMessages);
                  console.log(`Filtered ${newMessages.length} messages to ${uniqueMessages.length} unique messages`);

                  messages.value = uniqueMessages.sort((a, b) => {
                    const timeA = new Date(a.createTime).getTime();
                    const timeB = new Date(b.createTime).getTime();
                    return timeA - timeB; // 升序排列，旧消息在前
                  });

                  lastMessageId.value = retryResponse.data.lastMessageId || '0';

                  // 滚动到底部
                  await nextTick();
                  scrollToBottom();

                  ElMessage.success('成功加载消息');
                } else {
                  messages.value = [];
                }
              }
            } catch (finalError) {
              console.error('Final attempt to load messages failed:', finalError);
            }
          }, 2000);
        } else {
          // 其他类型的错误
          ElMessage({
            message: '加载消息失败：' + (response.message || '服务器错误'),
            type: 'warning',
            duration: 3000
          });
        }
      }
    }
  } catch (error) {
    console.error('加载消息错误', error);
    console.error('Error details:', error.message, error.response);

    // 检查是否是Redis序列化错误
    const errorMessage = error.message || '';
    const responseMessage = error.response?.data?.message || '';
    const isRedisError = errorMessage.includes('serialization') ||
                         errorMessage.includes('序列化') ||
                         responseMessage.includes('serialization') ||
                         responseMessage.includes('序列化');

    if (isRedisError) {
      console.warn('Detected Redis serialization error in exception');

      // 显示友好提示
      ElMessage({
        message: '正在尝试从数据库获取消息...',
        type: 'info',
        duration: 2000
      });

      // 延迟一点时间后重新尝试加载消息
      setTimeout(async () => {
        try {
          console.log('Final retry attempt after Redis error');
          const finalResponse = await getHistoryMessages(friendIdStr, lastMessageId.value);

          if (finalResponse && finalResponse.code === 200 && finalResponse.data) {
            const newMessages = finalResponse.data.messageList || [];

            if (newMessages.length > 0) {
              // 确保消息按时间顺序排列，并且去重
              const uniqueMessages = removeDuplicateMessages(newMessages);
              console.log(`Filtered ${newMessages.length} messages to ${uniqueMessages.length} unique messages`);

              messages.value = uniqueMessages.sort((a, b) => {
                const timeA = new Date(a.createTime).getTime();
                const timeB = new Date(b.createTime).getTime();
                return timeA - timeB; // 升序排列，旧消息在前
              });

              lastMessageId.value = finalResponse.data.lastMessageId || '0';

              // 滚动到底部
              await nextTick();
              scrollToBottom();

              ElMessage.success('成功加载消息');

              // 更新加载状态
              loadingMessages.value = false;
            }
          }
        } catch (finalError) {
          console.error('Final attempt to load messages failed:', finalError);
          loadingMessages.value = false;

          // 如果是首次加载，显示一个空消息状态
          if (lastMessageId.value === '0') {
            // 清空消息列表，让空状态显示
            messages.value = [];
            console.log('Set empty messages array after all retries failed');
          }
        }
      }, 2000);
    } else {
      // 如果是首次加载，显示一个空消息状态
      if (lastMessageId.value === '0') {
        // 清空消息列表，让空状态显示
        messages.value = [];
        console.log('Set empty messages array due to exception');
      }

      ElMessage({
        message: '加载消息失败，请稍后重试',
        type: 'warning',
        duration: 3000
      });

      loadingMessages.value = false;
    }
  } finally {
    // 只有在不是Redis错误的情况下才在这里设置loadingMessages为false
    // Redis错误情况下会在异步重试后设置
    if (!error || !(error.message || '').includes('serialization')) {
      loadingMessages.value = false;
    }
  }
};

// 加载更多消息
const loadMoreMessages = async () => {
  if (selectedContact.value && lastMessageId.value !== '0') {
    await loadMessages(selectedContact.value.id);
  }
};

// 刷新消息 - 检查是否有新消息但不清空现有消息
const refreshMessages = async (friendId) => {
  if (!friendId || loadingMessages.value) return;

  // 确保friendId是字符串类型
  const friendIdStr = friendId.toString();
  console.log('Refreshing messages for friendId:', friendIdStr, 'Type:', typeof friendIdStr);

  try {
    // 获取最新消息，但使用特殊标记表示这是刷新操作
    const response = await getHistoryMessages(friendIdStr, '0');
    console.log('Refresh messages response:', response);

    if (response && response.code === 200 && response.data && response.data.messageList) {
      const newMessages = response.data.messageList || [];
      console.log(`Received ${newMessages.length} messages in refresh`);

      // 如果当前没有消息，直接使用服务器返回的消息
      if (messages.value.length === 0) {
        // 确保消息按时间顺序排列，并且去重
        const uniqueMessages = removeDuplicateMessages(newMessages);
        console.log(`Filtered ${newMessages.length} messages to ${uniqueMessages.length} unique messages`);

        messages.value = uniqueMessages.sort((a, b) => {
          const timeA = new Date(a.createTime).getTime();
          const timeB = new Date(b.createTime).getTime();
          return timeA - timeB; // 升序排列，旧消息在前
        });
        console.log(`Set ${messages.value.length} messages from refresh`);

        // 更新缓存
        if (selectedContact.value && selectedContact.value.id === friendId) {
          contactMessagesCache[friendId] = {
            messages: [...messages.value],
            lastMessageId: response.data.lastMessageId || '0'
          };
        }

        // 滚动到底部
        await nextTick();
        scrollToBottom();
        return;
      }

      // 如果已有消息，检查是否有新消息
      if (newMessages.length > 0) {
        // 创建一个Map来存储现有消息，以messageId为键
        const existingMessagesMap = new Map();
        messages.value.forEach(msg => {
          existingMessagesMap.set(msg.messageId, true);
        });

        // 过滤出真正的新消息
        const trulyNewMessages = newMessages.filter(msg => !existingMessagesMap.has(msg.messageId));

        console.log(`Found ${trulyNewMessages.length} truly new messages`);

        if (trulyNewMessages.length > 0) {
          // 按时间顺序排列新消息
          const sortedNewMessages = trulyNewMessages.sort((a, b) => {
            const timeA = new Date(a.createTime).getTime();
            const timeB = new Date(b.createTime).getTime();
            return timeA - timeB;
          });

          // 将新消息添加到现有消息列表
          messages.value = [...messages.value, ...sortedNewMessages];
          console.log(`Updated message list, now has ${messages.value.length} messages`);

          // 更新缓存
          if (selectedContact.value && selectedContact.value.id === friendId) {
            contactMessagesCache[friendId] = {
              messages: [...messages.value],
              lastMessageId: response.data.lastMessageId || '0'
            };
            console.log(`Updated cache for contact ${friendId}`);
          }

          // 标记为已读
          await markMessagesAsRead(friendId);

          // 滚动到底部
          await nextTick();
          scrollToBottom();
        } else {
          console.log('No new messages found in refresh');
        }
      }
    }
  } catch (error) {
    console.error('Error refreshing messages:', error);
    console.error('Error details:', error.message, error.response);
  }
};

// 去除重复消息的辅助函数
const removeDuplicateMessages = (messages) => {
  const uniqueMessages = [];
  const messageIds = new Set();

  for (const message of messages) {
    if (!messageIds.has(message.messageId)) {
      messageIds.add(message.messageId);
      uniqueMessages.push(message);
    }
  }

  return uniqueMessages;
};

// 滚动到底部
const scrollToBottom = () => {
  if (messageContainer.value) {
    console.log('Scrolling to bottom, container height:', messageContainer.value.scrollHeight);
    messageContainer.value.scrollTop = messageContainer.value.scrollHeight;
  } else {
    console.warn('Message container ref is not available for scrolling');
  }
};

// 监听消息变化，自动滚动到底部
watch(messages, (newMessages) => {
  console.log('Messages changed:', newMessages.length, 'messages');
  console.log('Message content sample:', newMessages.length > 0 ? newMessages[0].messageContent : 'No messages');

  // 调试消息显示问题
  if (newMessages.length > 0) {
    console.log('Message container exists:', !!messageContainer.value);
    console.log('Messages container exists:', !!document.querySelector('.messages-container'));

    // 强制重新渲染消息列表
    setTimeout(() => {
      console.log('Re-checking message display after timeout');
      console.log('Messages container visibility:',
        document.querySelector('.messages-container') ?
        window.getComputedStyle(document.querySelector('.messages-container')).display :
        'Container not found');
    }, 500);
  }

  nextTick(() => {
    scrollToBottom();
  });
});

// 发送新消息
const newMessage = ref('');
const sendingMessage = ref(false);
const sendMessage = async () => {
  if (!newMessage.value.trim() || !selectedContact.value || sendingMessage.value) return;

  sendingMessage.value = true;

  // 保存消息内容，以便在发送失败时恢复
  const messageContent = newMessage.value.trim();

  // 先清空输入框，提升用户体验
  newMessage.value = '';

  // 创建临时消息对象
  const tempMessageId = 'temp-' + Date.now().toString();
  const newMessageObj = {
    messageId: tempMessageId,
    senderId: currentUserId.value,
    messageContent: messageContent,
    createTime: new Date(),
    status: 0, // 发送中
    messageType: 0
  };

  // 先添加到本地消息列表（添加到末尾，表示最新消息）
  console.log('Adding new message to list:', newMessageObj);
  messages.value.push(newMessageObj); // 添加到数组末尾，确保新消息显示在底部

  // 滚动到底部
  await nextTick();
  scrollToBottom();

  try {
    const messageData = {
      receiverId: parseInt(selectedContact.value.id),
      content: messageContent,
      messageType: 0 // 普通消息
    };

    console.log('Sending message:', messageData);

    // 添加重试逻辑
    let retryCount = 0;
    const maxRetries = 2;
    let success = false;
    let response;

    while (!success && retryCount <= maxRetries) {
      try {
        if (retryCount > 0) {
          console.log(`Retry attempt ${retryCount} for sending message...`);

          // 更新消息状态为重试中
          const msgIndex = messages.value.findIndex(m => m.messageId === tempMessageId);
          if (msgIndex !== -1) {
            messages.value[msgIndex].status = 3; // 重试中
          }
        }

        // 确保使用正确的Content-Type
        response = await apiSendMessage({
          receiverId: parseInt(selectedContact.value.id),
          content: messageContent,
          messageType: 0
        });
        console.log('Send message response:', response);
        success = true;
      } catch (retryError) {
        retryCount++;
        if (retryCount <= maxRetries) {
          console.warn(`Error sending message, retrying (${retryCount}/${maxRetries})...`, retryError);
          await new Promise(resolve => setTimeout(resolve, 1000)); // Wait 1 second before retry
        } else {
          throw retryError; // Re-throw if all retries failed
        }
      }
    }

    if (response && response.code === 200) {
      // 更新消息状态为已发送
      const msgIndex = messages.value.findIndex(m => m.messageId === tempMessageId);
      if (msgIndex !== -1) {
        messages.value[msgIndex].status = 1; // 已发送
        messages.value[msgIndex].messageId = response.data?.messageId || Date.now().toString();
      }

      // 更新联系人最后消息
      const contactIndex = contacts.value.findIndex(c => c.id === selectedContact.value.id);
      if (contactIndex !== -1) {
        contacts.value[contactIndex].lastMessage = messageContent;
        contacts.value[contactIndex].lastMessageTime = '刚刚';

        // 将该联系人移到列表顶部
        const contact = contacts.value.splice(contactIndex, 1)[0];
        contacts.value.unshift(contact);
      }

      ElMessage.success('发送成功');

      // 如果不是好友关系，更新已发送消息数量
      if (!isFriend.value) {
        messageCount.value++;
        console.log('Updated message count for non-friend:', messageCount.value);
      }

      // 刷新消息列表以确保与服务器一致，但不清空现有消息
      setTimeout(() => {
        if (selectedContact.value) {
          console.log('Refreshing message list after send');
          refreshMessages(selectedContact.value.id);
        }
      }, 500);
    } else {
      // 更新消息状态为发送失败
      const msgIndex = messages.value.findIndex(m => m.messageId === tempMessageId);
      if (msgIndex !== -1) {
        messages.value[msgIndex].status = 2; // 发送失败
      }

      // 显示更明显的错误提示
      ElMessage({
        message: '发送失败：' + (response?.message || '服务器错误'),
        type: 'error',
        duration: 5000,
        showClose: true
      });

      // 如果是消息数量限制的错误，显示特殊提示
      if (response?.message && response.message.includes('最多只能发送3条消息')) {
        ElNotification({
          title: '消息限制',
          message: '您与该用户不是互相关注的关系，最多只能发送3条消息。请先互相关注后再继续聊天。',
          type: 'warning',
          duration: 10000,
          position: 'top-right'
        });
      }
    }
  } catch (error) {
    console.error('发送消息错误', error);
    console.error('Error details:', error.message, error.response);

    // 更新消息状态为发送失败
    const msgIndex = messages.value.findIndex(m => m.messageId === tempMessageId);
    if (msgIndex !== -1) {
      messages.value[msgIndex].status = 2; // 发送失败
    }

    // 恢复消息内容到输入框，方便用户重新发送
    if (newMessage.value === '') {
      newMessage.value = messageContent;
    }

    ElMessage({
      message: '发送失败，请稍后重试',
      type: 'error',
      duration: 5000,
      showClose: true
    });
  } finally {
    sendingMessage.value = false;
  }
};

// 新建私信
const newMessageDialogVisible = ref(false);
const newMessageForm = ref({
  receiverId: '',
  messageType: 0,
  content: ''
});

const showNewMessageDialog = () => {
  newMessageDialogVisible.value = true;
  newMessageForm.value = {
    receiverId: '',
    messageType: 0,
    content: ''
  };
};

const isNewMessageFormValid = computed(() => {
  return (
    newMessageForm.value.receiverId &&
    newMessageForm.value.content.trim() &&
    [0, 1, 2].includes(newMessageForm.value.messageType)
  );
});

const createNewMessage = async () => {
  if (!isNewMessageFormValid.value) return;

  try {
    // 确保使用正确的Content-Type和数据格式
    const response = await apiSendMessage({
      receiverId: parseInt(newMessageForm.value.receiverId),
      content: newMessageForm.value.content.trim(),
      messageType: newMessageForm.value.messageType
    });

    if (response.code === 200) {
      ElMessage.success('发送成功');

      // 刷新联系人列表
      await fetchContacts();

      // 检查是否已有该联系人
      const existingContact = contacts.value.find(c => c.id === newMessageForm.value.receiverId.toString());

      if (existingContact) {
        // 选中该联系人
        selectContact(existingContact);
      }

      newMessageDialogVisible.value = false;
    } else {
      // 显示更明显的错误提示
      ElMessage({
        message: '发送失败：' + response.message,
        type: 'error',
        duration: 5000,
        showClose: true
      });

      // 如果是消息数量限制的错误，显示特殊提示
      if (response.message && response.message.includes('最多只能发送3条消息')) {
        ElNotification({
          title: '消息限制',
          message: '您与该用户不是互相关注的关系，最多只能发送3条消息。请先互相关注后再继续聊天。',
          type: 'warning',
          duration: 10000,
          position: 'top-right'
        });
      }
    }
  } catch (error) {
    console.error('发送消息错误', error);
    ElMessage.error('发送失败，请稍后重试');
  }
};

// 重试发送失败的消息
const retryMessage = async (message) => {
  if (!message || message.status !== 2) return;

  // 更新消息状态为重试中
  const msgIndex = messages.value.findIndex(m => m.messageId === message.messageId);
  if (msgIndex !== -1) {
    messages.value[msgIndex].status = 3; // 重试中
  }

  try {
    // 确保使用正确的Content-Type和数据格式
    const messageData = {
      receiverId: parseInt(selectedContact.value.id),
      content: message.messageContent,
      messageType: message.messageType || 0
    };

    console.log('Retrying message:', messageData);
    // 使用明确的Content-Type
    const response = await apiSendMessage(messageData);
    console.log('Retry message response:', response);

    if (response && response.code === 200) {
      // 更新消息状态为已发送
      if (msgIndex !== -1) {
        messages.value[msgIndex].status = 1; // 已发送
        messages.value[msgIndex].messageId = response.data?.messageId || message.messageId;
      }

      ElMessage.success('重试发送成功');

      // 如果不是好友关系，更新已发送消息数量
      if (!isFriend.value) {
        messageCount.value++;
      }
    } else {
      // 更新消息状态为发送失败
      if (msgIndex !== -1) {
        messages.value[msgIndex].status = 2; // 发送失败
      }

      ElMessage({
        message: '重试失败：' + (response?.message || '服务器错误'),
        type: 'error',
        duration: 5000
      });
    }
  } catch (error) {
    console.error('重试发送消息错误', error);

    // 更新消息状态为发送失败
    const msgIndex = messages.value.findIndex(m => m.messageId === message.messageId);
    if (msgIndex !== -1) {
      messages.value[msgIndex].status = 2; // 发送失败
    }

    ElMessage({
      message: '重试失败，请稍后再试',
      type: 'error',
      duration: 5000
    });
  }
};

// 获取联系人最后一条消息
const getLastMessage = (contact) => {
  console.log(`Getting last message for contact ${contact.id} (${contact.username})`);

  // 如果联系人有缓存的消息，使用缓存中的最后一条消息
  if (contactMessagesCache[contact.id] && contactMessagesCache[contact.id].messages.length > 0) {
    const cachedMessages = contactMessagesCache[contact.id].messages;
    const lastCachedMessage = cachedMessages[cachedMessages.length - 1];
    console.log(`Found cached message for ${contact.username}:`, lastCachedMessage.messageContent);
    return lastCachedMessage.messageContent;
  }

  // 如果联系人有lastMessage属性，使用它
  if (contact.lastMessage && contact.lastMessage.trim() !== '') {
    console.log(`Using contact's lastMessage for ${contact.username}:`, contact.lastMessage);
    return contact.lastMessage;
  }

  // 如果没有任何消息，显示"暂无消息"
  console.log(`No message found for ${contact.username}, showing default text`);
  return '暂无消息';
};

// 格式化时间
const formatTime = (time) => {
  if (!time) return '';

  // 确保time是一个有效的日期对象或日期字符串
  console.log('Formatting time:', time, 'Type:', typeof time);

  let date;
  try {
    date = new Date(time);
    // 检查日期是否有效
    if (isNaN(date.getTime())) {
      console.error('Invalid date:', time);
      return '';
    }
  } catch (error) {
    console.error('Error parsing date:', time, error);
    return '';
  }

  const now = new Date();
  const diff = now - date;

  // 今天的消息显示时间
  if (diff < 24 * 60 * 60 * 1000 && date.getDate() === now.getDate()) {
    return `${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`;
  }

  // 昨天的消息
  if (diff < 48 * 60 * 60 * 1000 && date.getDate() === now.getDate() - 1) {
    return '昨天';
  }

  // 更早的消息显示日期
  return `${date.getMonth() + 1}月${date.getDate()}日`;
};



// 获取联系人列表
const fetchContacts = async () => {
  try {
    const response = await getRecentContacts();
    console.log('Fetched contacts response:', response);
    if (response.code === 200) {
      // 确保response.data是数组
      if (Array.isArray(response.data)) {
        contacts.value = response.data.map(contact => ({
          id: contact.id.toString(),
          username: contact.username,
          avatar: contact.image || '',
          signature: contact.signature,
          lastMessage: contact.lastMessage || '',
          lastMessageTime: contact.lastMessageTime || '',
          unreadCount: contact.unreadCount || 0
        }));
        console.log('Processed contacts:', contacts.value);
      } else {
        console.error('Contacts data is not an array:', response.data);
        // 如果后端返回的不是数组，创建一个空数组
        contacts.value = [];

        // 如果没有联系人，创建一个测试联系人
        if (currentUserId.value) {
          createTestContact();
        }
      }
    } else {
      console.error('Failed to get contacts:', response);
      ElMessage.error('获取联系人列表失败：' + response.message);
      // 如果失败，创建一个测试联系人
      if (currentUserId.value) {
        createTestContact();
      }
    }
  } catch (error) {
    console.error('获取联系人列表错误', error);
    console.error('Error details:', error.message, error.response);
    ElMessage.error('获取联系人列表失败，请稍后重试');

    // 如果出错，创建一个测试联系人
    if (currentUserId.value) {
      createTestContact();
    }
  }
};

// 创建测试联系人（当没有联系人时使用）
const createTestContact = () => {
  // 确保不重复添加
  if (contacts.value.some(c => c.id === '1')) {
    return;
  }

  // 添加一个测试联系人
  contacts.value.push({
    id: '1', // 使用ID 1作为测试用户
    username: '测试用户',
    avatar: '',
    signature: '这是一个测试用户',
    lastMessage: '欢迎使用私信功能',
    lastMessageTime: new Date().toLocaleString(),
    unreadCount: 0
  });
  console.log('Created test contact');
};

// 创建测试消息
const createTestMessage = async () => {
  try {
    // 先确保有测试联系人
    createTestContact();

    // 如果有联系人，选中第一个
    if (contacts.value.length > 0) {
      await selectContact(contacts.value[0]);

      // 创建一个测试消息
      // 确保使用正确的Content-Type和数据格式
      const messageData = {
        receiverId: parseInt(contacts.value[0].id), // 使用第一个联系人的ID
        content: '这是一条测试消息',
        messageType: 0 // 普通消息
      };

      console.log('Sending test message to:', messageData.receiverId);
      // 使用明确的Content-Type
      const response = await apiSendMessage(messageData);

      if (response.code === 200) {
        ElMessage.success('测试消息发送成功');
        // 刷新联系人列表
        await fetchContacts();
        // 重新选中第一个联系人
        if (contacts.value.length > 0) {
          await selectContact(contacts.value[0]);
        }
      } else {
        console.error('Test message failed:', response);
        ElMessage.error('测试消息发送失败：' + response.message);

        // 即使发送失败，也确保选中了联系人
        if (selectedContact.value === null && contacts.value.length > 0) {
          await selectContact(contacts.value[0]);
        }
      }
    } else {
      ElMessage.warning('无法创建测试消息，请先添加联系人');
    }
  } catch (error) {
    console.error('发送测试消息错误', error);
    console.error('Error details:', error.message, error.response);
    ElMessage.error('发送测试消息失败，请稍后重试');

    // 即使出错，也确保选中了联系人
    if (selectedContact.value === null && contacts.value.length > 0) {
      await selectContact(contacts.value[0]);
    }
  }
};

// 获取当前用户信息
const fetchCurrentUserInfo = async () => {
  try {
    const response = await getUserInfo();
    if (response.code === 200) {
      currentUserId.value = response.data.id.toString();
      currentUsername.value = response.data.username;
      currentUserAvatar.value = response.data.image || '';
    } else {
      ElMessage.error('获取用户信息失败：' + response.message);
    }
  } catch (error) {
    console.error('获取用户信息错误', error);
    ElMessage.error('获取用户信息失败，请稍后重试');
  }
};



// 获取路由参数
const route = useRoute();

// 初始化
onMounted(async () => {
  loading.value = true;
  try {
    console.log('MessagePage: Component mounted');

    // 获取当前用户信息
    await fetchCurrentUserInfo();
    console.log('Current user info loaded:', {
      id: currentUserId.value,
      username: currentUsername.value
    });

    // 获取联系人列表
    await fetchContacts();
    console.log('Contacts loaded:', contacts.value.length);

    // 检查是否有userId查询参数（从搜索页面或用户页面跳转过来）
    const userId = route.query.userId;
    if (userId) {
      console.log('Found userId in query params:', userId, 'Type:', typeof userId);
      console.log('Current contacts:', contacts.value.map(c => ({ id: c.id, username: c.username })));

      // 查找联系人列表中是否已有该用户 - 确保类型一致进行比较
      console.log('Searching for contact with userId:', userId, 'Type:', typeof userId);
      console.log('Contact IDs for comparison:', contacts.value.map(c => ({ id: c.id, type: typeof c.id })));

      // 转换为字符串进行比较，确保类型一致
      const userIdStr = userId.toString();
      console.log('Using userIdStr for comparison:', userIdStr, 'Type:', typeof userIdStr);

      // 使用更宽松的比较方式，确保能找到匹配的联系人
      let existingContact = contacts.value.find(c => c.id.toString() === userIdStr);

      // 如果没找到，尝试使用数字比较
      if (!existingContact && !isNaN(parseInt(userId))) {
        console.log('Trying numeric comparison');
        const userIdNum = parseInt(userId);
        existingContact = contacts.value.find(c => parseInt(c.id) === userIdNum);
      }

      if (existingContact) {
        console.log('Found existing contact in contacts list:', existingContact);
        // 如果已有该联系人，选中它
        await selectContact(existingContact);

        // 确保消息区域可见
        nextTick(() => {
          console.log('Ensuring message area is visible for existing contact');
          if (document.querySelector('.message-chat')) {
            document.querySelector('.message-chat').style.display = 'flex';
          }
        });
      } else {
        console.log('Contact not found in recent contacts, fetching user info');
        // 如果没有该联系人，需要获取用户信息并创建新的联系人
        try {
          const userResponse = await getUserInfo(parseInt(userId));
          console.log('User info response:', userResponse);

          if (userResponse.code === 200 && userResponse.data) {
            // 创建一个新的联系人对象
            const newContact = {
              id: userId.toString(),
              username: userResponse.data.username,
              avatar: userResponse.data.image || '',
              signature: userResponse.data.signature || '',
              lastMessage: '',
              lastMessageTime: '',
              unreadCount: 0
            };
            console.log('Created new contact:', newContact);

            // 添加到联系人列表的首位
            contacts.value.unshift(newContact);

            // 确保联系人列表已更新
            await nextTick();

            // 再次检查联系人是否已添加到列表中
            console.log('Contacts after adding new contact:', contacts.value.map(c => ({ id: c.id, username: c.username })));

            // 确保新联系人在列表中
            const addedContact = contacts.value.find(c => c.id === newContact.id);
            if (addedContact) {
              console.log('Confirmed new contact was added to list:', addedContact);
            } else {
              console.warn('New contact was not found in contacts list after adding!');
            }

            // 选中该联系人
            console.log('Selecting new contact:', newContact);
            await selectContact(newContact);

            // 确保消息区域可见
            nextTick(() => {
              console.log('Ensuring message area is visible for new contact');
              if (document.querySelector('.message-chat')) {
                document.querySelector('.message-chat').style.display = 'flex';
              }
            });

            // 检查是否已经有消息历史 - 确保userId是字符串类型
            const userIdStr = userId.toString();
            console.log('Checking message history for userId:', userIdStr, 'Type:', typeof userIdStr);

            try {
              const historyResponse = await getHistoryMessages(userIdStr, '0');
              console.log('History check response:', historyResponse);

              // 如果没有消息历史，打开新建私信对话框
              if (!historyResponse.data || !historyResponse.data.messageList || historyResponse.data.messageList.length === 0) {
                console.log('No message history found, showing new message dialog');
                // 打开新建私信对话框，预填接收者ID
                newMessageForm.value.receiverId = parseInt(userId);
                newMessageDialogVisible.value = true;
              } else {
                console.log('Message history found, not showing dialog');
                // 有消息历史，不需要打开对话框
                ElMessage.success(`已加载与 ${newContact.username} 的聊天记录`);

                // 强制加载消息到UI中，无论当前状态如何
                console.log('Loading messages from history response');

                // 确保消息按时间顺序排列
                const sortedMessages = historyResponse.data.messageList.sort((a, b) => {
                  const timeA = new Date(a.createTime).getTime();
                  const timeB = new Date(b.createTime).getTime();
                  return timeA - timeB; // 升序排列，旧消息在前
                });

                console.log('Sorted messages:', sortedMessages);

                // 直接设置消息列表
                messages.value = sortedMessages;
                lastMessageId.value = historyResponse.data.lastMessageId || '0';

                // 更新缓存
                contactMessagesCache[userIdStr] = {
                  messages: [...messages.value],
                  lastMessageId: lastMessageId.value
                };

                console.log(`Updated messages array with ${messages.value.length} messages`);
                console.log('First message:', messages.value.length > 0 ? messages.value[0] : 'No messages');
                console.log('Last message:', messages.value.length > 0 ? messages.value[messages.value.length - 1] : 'No messages');

                // 确保消息容器可见
                nextTick(() => {
                  console.log('Ensuring message container is visible after loading history');
                  if (messageContainer.value) {
                    messageContainer.value.style.display = 'flex';
                    messageContainer.value.style.flexDirection = 'column';
                    // 滚动到底部
                    scrollToBottom();
                  } else {
                    console.warn('Message container ref not available after loading history');
                  }
                });
              }
            } catch (historyError) {
              console.error('Error checking message history:', historyError);
              ElMessage.error('加载聊天记录失败，请稍后重试');

              // 即使出错，也打开新建私信对话框
              newMessageForm.value.receiverId = parseInt(userId);
              newMessageDialogVisible.value = true;
            }
          }
        } catch (error) {
          console.error('获取用户信息失败', error);
          console.error('Error details:', error.message, error.response);
          ElMessage.error('获取用户信息失败，请稍后重试');

          // 如果获取用户信息失败，确保至少有一个联系人可选
          if (contacts.value.length === 0) {
            createTestContact();
          }
        }
      }

      // 延迟清除URL中的userId参数，确保联系人已经完全加载
      setTimeout(() => {
        if (window.history.replaceState) {
          const newUrl = window.location.pathname;
          window.history.replaceState({path: newUrl}, '', newUrl);
          console.log('Cleared userId from URL after delay');
        }
      }, 1000); // 延迟1秒清除URL参数
    } else if (contacts.value.length > 0) {
      console.log('No userId specified, selecting first contact');
      // 如果没有指定userId且有联系人，默认选中第一个
      await selectContact(contacts.value[0]);
    } else {
      console.log('No contacts available, creating test contact');
      // 如果没有联系人，创建一个测试联系人并选中它
      createTestContact();
      if (contacts.value.length > 0) {
        await selectContact(contacts.value[0]);
      }
    }
  } catch (error) {
    console.error('初始化错误', error);
    console.error('Error details:', error.message, error.response);
    ElMessage.error('初始化失败，请稍后重试');

    // 如果初始化失败，确保至少有一个联系人可选
    if (contacts.value.length === 0) {
      createTestContact();
      if (contacts.value.length > 0) {
        await selectContact(contacts.value[0]);
      }
    }
  } finally {
    loading.value = false;
  }

  // 监听消息容器的滚动事件，实现上拉加载更多
  nextTick(() => {
    if (messageContainer.value) {
      console.log('Adding scroll event listener to message container');
      messageContainer.value.addEventListener('scroll', () => {
        if (messageContainer.value.scrollTop === 0 && !loadingMessages.value && lastMessageId.value !== '0') {
          console.log('Scrolled to top, loading more messages');
          loadMoreMessages();
        }
      });
    }

    // 确保消息区域可见
    console.log('Ensuring message area is visible after component mounted');
    if (document.querySelector('.message-chat')) {
      document.querySelector('.message-chat').style.display = 'flex';
    }
    if (messageContainer.value) {
      messageContainer.value.style.display = 'flex';
      messageContainer.value.style.flexDirection = 'column';
    }

    // 如果有消息，确保滚动到底部
    if (messages.value.length > 0) {
      console.log(`Scrolling to bottom for ${messages.value.length} messages after component mounted`);
      scrollToBottom();
    }

    // 强制重新渲染消息列表
    if (selectedContact.value && messages.value.length > 0) {
      console.log('Force refreshing message display');
      const tempMessages = [...messages.value];
      messages.value = [];
      setTimeout(() => {
        messages.value = tempMessages;
        nextTick(() => {
          scrollToBottom();
        });
      }, 100);
    }
  });
});

// 监听selectedContact变化，确保消息区域始终可见
watch(selectedContact, (newContact) => {
  if (newContact) {
    console.log('Selected contact changed, ensuring message area visibility');
    nextTick(() => {
      // 确保消息区域可见
      if (document.querySelector('.message-chat')) {
        document.querySelector('.message-chat').style.display = 'flex';
      }
      // 确保消息容器可见
      if (messageContainer.value) {
        messageContainer.value.style.display = 'flex';
        messageContainer.value.style.flexDirection = 'column';
      }
    });
  }
});
</script>

<style scoped>
.message-page-container {
  min-height: 100vh;
  background-color: #f8f9fa;
  color: #333333;
  padding: 20px;
  padding-top: 80px; /* Space for fixed navigation */
  position: relative;
  z-index: 1;
}

/* Cyberpunk background elements */
.cyberpunk-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
  overflow: hidden;
}

.cyberpunk-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(240, 240, 240, 0.7) 100%);
  z-index: 1;
}

.cyberpunk-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: url('https://images.unsplash.com/photo-1550745165-9bc0b252726f?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80') center/cover no-repeat;
  filter: saturate(0.9) brightness(0.8) hue-rotate(0deg);
  z-index: 0;
}

.cyberpunk-grid {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background:
    linear-gradient(90deg, rgba(31, 111, 235, 0.05) 1px, transparent 1px),
    linear-gradient(0deg, rgba(31, 111, 235, 0.05) 1px, transparent 1px);
  background-size: 20px 20px;
  z-index: 2;
}

.cyberpunk-scanline {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(to bottom, transparent 50%, rgba(0, 0, 0, 0.03) 51%, transparent 52%);
  background-size: 100% 4px;
  animation: scanline 6s linear infinite;
  opacity: 0.1;
  z-index: 3;
}

.cyberpunk-glitch-effect {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle at center, transparent 90%, rgba(31, 111, 235, 0.1) 100%);
  z-index: 4;
  pointer-events: none;
}

@keyframes scanline {
  0% {
    background-position: 0 0;
  }
  100% {
    background-position: 0 100%;
  }
}

.message-header {
  margin-bottom: 30px;
  text-align: center;
  position: relative;
}

.message-header h1 {
  font-size: 2.5rem;
  margin-bottom: 10px;
  color: #1f6feb;
  text-shadow: 1px 1px 3px rgba(88, 166, 255, 0.3);
  letter-spacing: 1px;
  font-weight: 600;
}

.message-header p {
  font-size: 1.1rem;
  color: #666666;
  font-weight: 500;
}

.message-content {
  display: flex;
  height: calc(100vh - 200px);
  background-color: rgba(255, 255, 255, 0.95);
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(31, 111, 235, 0.2);
  backdrop-filter: blur(5px);
  position: relative; /* 确保子元素可以相对于此定位 */
}

.contact-list {
  width: 300px;
  border-right: 1px solid rgba(0, 0, 0, 0.08);
  display: flex;
  flex-direction: column;
  background-color: rgba(248, 249, 250, 0.9);
  height: 100%;
}

.contact-search {
  padding: 15px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.08);
}

.contact-search .el-button {
  margin-top: 10px;
  width: 100%;
}

.cyberpunk-input :deep(.el-input__inner) {
  background-color: rgba(255, 255, 255, 0.9) !important;
  border: 1px solid rgba(31, 111, 235, 0.3) !important;
  color: #333333 !important;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
}

.cyberpunk-button {
  background: linear-gradient(135deg, #1f6feb, #0d47a1) !important;
  border: none !important;
  box-shadow: 0 2px 8px rgba(31, 111, 235, 0.3) !important;
  position: relative;
  overflow: hidden;
  font-weight: 500;
}

.cyberpunk-button::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(
    to bottom right,
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 0.2) 50%,
    rgba(255, 255, 255, 0) 100%
  );
  transform: rotate(45deg);
  animation: shine 3s infinite;
  pointer-events: none;
}

@keyframes shine {
  0% {
    left: -100%;
    top: -100%;
  }
  100% {
    left: 100%;
    top: 100%;
  }
}

.contact-items {
  flex: 1;
  overflow-y: auto;
  scrollbar-width: thin;
  scrollbar-color: #1f6feb #f8f9fa;
}

.contact-items::-webkit-scrollbar {
  width: 6px;
}

.contact-items::-webkit-scrollbar-track {
  background: #f8f9fa;
}

.contact-items::-webkit-scrollbar-thumb {
  background-color: #1f6feb;
  border-radius: 3px;
}

.contact-item {
  display: flex;
  align-items: center;
  padding: 15px;
  cursor: pointer;
  transition: all 0.3s ease;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  position: relative;
}

.contact-item:hover {
  background-color: rgba(31, 111, 235, 0.05);
  transform: translateX(5px);
}

.contact-item.active {
  background: linear-gradient(90deg, rgba(31, 111, 235, 0.1), rgba(31, 111, 235, 0.05));
  border-left: 3px solid #1f6feb;
}

.contact-info {
  margin-left: 12px;
  flex: 1;
  overflow: hidden;
}

.contact-name {
  font-weight: bold;
  margin-bottom: 4px;
  color: #333333;
}

.contact-preview {
  font-size: 0.85rem;
  color: #666666;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.active .contact-preview {
  color: #333333;
}

.contact-meta {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

.contact-time {
  font-size: 0.75rem;
  color: #666666;
  margin-bottom: 5px;
}

.active .contact-time {
  color: #333333;
}

.unread-badge {
  background-color: #1f6feb;
  color: white;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.7rem;
  box-shadow: 0 2px 5px rgba(31, 111, 235, 0.3);
  font-weight: 500;
}

.no-contacts {
  padding: 20px;
  text-align: center;
  color: #666666;
  font-style: italic;
}

.mt-10 {
  margin-top: 10px;
}

.ml-10 {
  margin-left: 10px;
}

.message-chat {
  flex: 1;
  display: flex !important; /* 强制显示，确保始终可见 */
  flex-direction: column;
  background-color: rgba(255, 255, 255, 0.9);
  height: 100%; /* Ensure it takes full height of parent */
  overflow: hidden; /* Prevent overflow issues */
  min-width: 0; /* 确保在flex布局中可以正确缩放 */
}

.chat-content-wrapper {
  display: flex !important; /* 强制显示，确保始终可见 */
  flex-direction: column;
  flex: 1;
  overflow: hidden;
  height: 100%;
  visibility: visible !important; /* 确保始终可见 */
  min-height: 300px; /* 确保有足够的最小高度 */
  position: relative; /* 确保子元素可以相对于此定位 */
  z-index: 1; /* 确保在其他元素之上 */
}

.chat-header {
  padding: 15px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.08);
  display: flex;
  flex-direction: column;
  background: linear-gradient(90deg, rgba(31, 111, 235, 0.05), transparent);
}

.chat-title {
  font-size: 1.2rem;
  font-weight: bold;
  color: #1f6feb;
}

.chat-subtitle {
  font-size: 0.9rem;
  color: #666666;
  margin-top: 5px;
}

.friend-status-warning {
  margin-top: 10px;
}

.limit-reached {
  color: #ff4949;
  font-size: 0.9rem;
  margin-left: 10px;
  font-weight: 500;
}

.chat-messages {
  flex: 1; /* Take up all available space */
  padding: 15px;
  overflow-y: auto; /* Enable vertical scrolling */
  display: flex !important; /* 强制显示，确保始终可见 */
  flex-direction: column;
  scrollbar-width: thin;
  scrollbar-color: #1f6feb #f8f9fa;
  min-height: 200px; /* 确保有足够的最小高度 */
  height: auto; /* 允许高度自适应 */
  width: 100%; /* 确保宽度填满父容器 */
  position: relative; /* 确保子元素可以相对于此定位 */
  visibility: visible !important; /* 确保始终可见 */
  z-index: 1; /* 确保在其他元素之上 */
}

.chat-messages::-webkit-scrollbar {
  width: 6px;
}

.chat-messages::-webkit-scrollbar-track {
  background: #f8f9fa;
}

.chat-messages::-webkit-scrollbar-thumb {
  background-color: #1f6feb;
  border-radius: 3px;
}

.loading-messages {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px;
  color: #666666;
}

.loading-icon {
  animation: spin 1s linear infinite;
  margin-bottom: 10px;
  color: #1f6feb;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.load-more {
  align-self: center;
  padding: 8px 15px;
  background-color: rgba(31, 111, 235, 0.1);
  color: #1f6feb;
  border-radius: 15px;
  margin-bottom: 15px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.9rem;
  font-weight: 500;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

.load-more:hover {
  background-color: rgba(31, 111, 235, 0.2);
  transform: translateY(-2px);
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.08);
}

.messages-container {
  display: flex;
  flex-direction: column;
  width: 100%;
  align-items: flex-start; /* 确保消息从左侧开始 */
  min-height: 100px; /* 确保有足够的最小高度 */
  padding: 10px 0; /* 增加上下内边距 */
  overflow-y: auto; /* 允许垂直滚动 */
}

.message-item {
  display: flex;
  margin-bottom: 6px; /* 减少间距，使消息更紧凑 */
  max-width: 90%; /* 保持最大宽度 */
  animation: fadeIn 0.3s ease;
  width: auto; /* 允许宽度自适应内容 */
  align-items: flex-start; /* 顶部对齐头像和气泡，避免气泡过高 */
  min-height: 20px; /* 更小的最小高度 */
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

.message-self {
  align-self: flex-end;
  flex-direction: row-reverse;
}

.message-bubble {
  display: inline-block;
  min-height: 30px; /* 最小高度，允许内容增长 */
  padding: 8px 12px; /* 增加内边距，使文本有更多空间 */

  /* 保留其他必要属性 */
  margin: 0 8px;
  border-radius: 16px;
  background-color: rgba(240, 240, 240, 0.9);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  backdrop-filter: blur(5px);
  max-width: 280px;
  box-sizing: border-box;
  font-size: 14px;
  color: #333333;
  position: relative; /* 确保时间容器可以相对定位 */
}

.message-self .message-bubble {
  background-color: #1f6feb; /* 蓝色，与系统主题一致 */
  border-radius: 16px; /* 保持完全圆角，与参考图一致 */
}

.message-item:not(.message-self) .message-bubble {
  background-color: #ffffff; /* 白色背景，与参考图一致 */
  color: #333333; /* 深色文字，提高可读性 */
  border-radius: 16px; /* 保持完全圆角，与参考图一致 */
}

/* Empty messages and welcome message styles */
.empty-messages {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  padding: 20px;
  color: #666666;
}

.welcome-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 12px;
  padding: 30px;
  max-width: 80%;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(0, 0, 0, 0.08);
}

.welcome-icon {
  margin-bottom: 20px;
  color: #1f6feb;
  background-color: rgba(31, 111, 235, 0.1);
  border-radius: 50%;
  padding: 15px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 3px 8px rgba(31, 111, 235, 0.1);
}

.welcome-text p {
  margin: 8px 0;
  line-height: 1.5;
  color: #333333;
}

.friend-limit-note {
  color: #f85149;
  font-size: 0.9em;
  margin-top: 10px;
  font-weight: 500;
}

.start-chat-hint {
  color: #1f6feb;
  font-weight: bold;
  margin-top: 15px;
  font-size: 1.1em;
}



.message-system {
  background-color: rgba(255, 165, 0, 0.2) !important;
  border: 1px dashed #ff9800 !important;
  color: #ff9800 !important;
  font-style: italic;
  margin: 10px auto !important;
  max-width: 90% !important;
  text-align: center;
  align-self: center !important;
}

.message-text {
  font-size: 0.85rem; /* 字体大小 */
  line-height: 1.4; /* 行高比例，更好的可读性 */
  max-width: 100%; /* 确保不会超出父容器 */
  width: 100%; /* 使用全宽 */
  padding: 0; /* 移除内边距 */
  margin: 0; /* 确保没有外边距 */
  word-break: break-word; /* 允许长单词换行 */
  white-space: normal; /* 允许文本换行 */
}

.message-time-container {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  position: absolute;
  bottom: -12px; /* 调整位置，使其更靠近气泡 */
  right: 5px;
  font-size: 0.65rem;
  opacity: 0; /* 默认隐藏 */
  transition: opacity 0.2s ease;
}

/* 鼠标悬停时显示时间 */
.message-bubble:hover .message-time-container {
  opacity: 1;
}

.message-time {
  font-size: 0.65rem; /* 减小字体大小 */
  color: rgba(102, 102, 102, 0.7); /* 深色文字适合浅色背景 */
  text-align: right;
  margin-top: 2px; /* 减小上边距 */
  white-space: nowrap; /* 防止时间戳换行 */
}

.message-self .message-time {
  color: rgba(255, 255, 255, 0.7);
}

.message-status {
  margin-left: 5px;
  display: flex;
  align-items: center;
}

.status-icon {
  font-size: 12px;
}

.status-failed-container {
  display: flex;
  align-items: center;
  gap: 5px;
}

.retry-button {
  padding: 2px 5px;
  font-size: 10px;
  height: auto;
  line-height: 1;
  margin-left: 5px;
}

.sending {
  color: #909399;
  animation: spin 1s linear infinite;
}

.sent {
  color: #67c23a;
}

.failed {
  color: #f56c6c;
}

.retrying {
  color: #e6a23c;
  animation: spin 1s linear infinite;
}

.message-sending {
  opacity: 0.8;
}

.message-failed {
  border: 1px solid rgba(245, 108, 108, 0.5) !important;
}

.message-retrying {
  border: 1px solid rgba(230, 162, 60, 0.5) !important;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.chat-input {
  padding: 15px;
  border-top: 1px solid rgba(200, 200, 200, 0.3);
  background-color: rgba(248, 249, 250, 0.9);
  flex-shrink: 0; /* Prevent input area from shrinking */
}

.chat-input .el-textarea :deep(.el-textarea__inner) {
  background-color: rgba(255, 255, 255, 0.95) !important;
  border: 1px solid rgba(31, 111, 235, 0.3) !important;
  color: #333333 !important;
  resize: none;
  height: 80px !important; /* Fixed height for consistency */
  min-height: 80px !important;
  max-height: 80px !important;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
}

.input-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 10px;
}

.hint {
  font-size: 0.8rem;
  color: #8b949e;
}

.no-chat-selected {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #8b949e;
}

.no-messages {
  text-align: center;
  padding: 30px;
  color: #8b949e;
  font-style: italic;
}

/* Dialog styling */
:deep(.el-dialog) {
  background-color: rgba(255, 255, 255, 0.98) !important;
  border: 1px solid rgba(31, 111, 235, 0.3) !important;
  border-radius: 10px !important;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1) !important;
  backdrop-filter: blur(10px) !important;
}

:deep(.el-dialog__title) {
  color: #1f6feb !important;
  font-weight: 600 !important;
}

:deep(.el-dialog__body) {
  color: #333333 !important;
}

:deep(.el-form-item__label) {
  color: #333333 !important;
  font-weight: 500 !important;
}

:deep(.el-select) {
  width: 100%;
}

:deep(.el-select__popper) {
  background-color: rgba(255, 255, 255, 0.98) !important;
  border: 1px solid rgba(31, 111, 235, 0.3) !important;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1) !important;
}

:deep(.el-select-dropdown__item) {
  color: #333333 !important;
}

:deep(.el-select-dropdown__item.hover),
:deep(.el-select-dropdown__item:hover) {
  background-color: rgba(31, 111, 235, 0.1) !important;
}

:deep(.el-select-dropdown__item.selected) {
  background-color: rgba(31, 111, 235, 0.2) !important;
  color: #1f6feb !important;
  font-weight: 500 !important;
}

.form-hint {
  font-size: 0.8rem;
  color: #666666;
  margin-top: 5px;
  font-style: italic;
}

.cyberpunk-button-cancel {
  background-color: rgba(240, 240, 240, 0.9) !important;
  border: 1px solid rgba(200, 200, 200, 0.5) !important;
  color: #333333 !important;
}

/* Responsive design */
@media (max-width: 768px) {
  .message-content {
    flex-direction: column;
    height: calc(100vh - 500px);
  }

  .contact-list {
    width: 100%;
    height: 200px;
    border-right: none;
    border-bottom: 1px solid rgba(200, 200, 200, 0.3);
    flex-shrink: 0; /* Prevent contact list from shrinking */
  }

  .message-chat {
    flex: 1; /* Take remaining space */
    min-height: 0; /* Allow proper scrolling */
  }

  .chat-messages {
    min-height: 20px; /* Ensure minimum height for messages area */
  }

  .message-item {
    max-width: 90%;
  }
}

.message-content-text {
  margin-bottom: 5px;
  word-break: break-word;
}

.message-time {
  font-size: 0.7rem;
  color: #8b949e;
  text-align: right;
}

.message-self .message-time {
  color: #c9d1d9;
}

.chat-input {
  padding: 15px;
  border-top: 1px solid rgba(200, 200, 200, 0.3);
}

.input-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 10px;
}

.hint {
  font-size: 0.8rem;
  color: #666666;
}

.no-chat-selected {
  display: flex;
  flex: 1;
  justify-content: center;
  align-items: center;
  color: #666666;
}

.no-messages {
  text-align: center;
  color: #666666;
  margin: 20px 0;
}

.no-contacts {
  text-align: center;
  color: #666666;
  padding: 20px;
}

/* 自定义Element Plus样式 */
:deep(.el-input__inner),
:deep(.el-textarea__inner) {
  background-color: #ffffff;
  border-color: #e0e0e0;
  color: #333333;
}

:deep(.el-input__inner:focus),
:deep(.el-textarea__inner:focus) {
  border-color: #1f6feb;
  box-shadow: 0 0 5px rgba(31, 111, 235, 0.2);
}

/* 自定义Alert样式 */
:deep(.cyberpunk-alert) {
  background-color: rgba(31, 111, 235, 0.05) !important;
  border-color: rgba(31, 111, 235, 0.2) !important;
  color: #333333 !important;
}

:deep(.cyberpunk-alert .el-alert__title) {
  color: #1f6feb !important;
  font-weight: bold;
}

:deep(.cyberpunk-alert .el-alert__icon) {
  color: #1f6feb !important;
}

:deep(.cyberpunk-alert p) {
  margin: 5px 0;
  color: #333333;
}

:deep(.el-button--primary) {
  background-color: #1f6feb;
  border-color: #1f6feb;
}

:deep(.el-dialog) {
  background-color: #ffffff;
  border-radius: 8px;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
}

:deep(.el-dialog__title) {
  color: #1f6feb;
  font-weight: 600;
}

:deep(.el-form-item__label) {
  color: #333333;
  font-weight: 500;
}

:deep(.el-select-dropdown) {
  background-color: #ffffff;
  border-color: #e0e0e0;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

:deep(.el-select-dropdown__item) {
  color: #333333;
}

:deep(.el-select-dropdown__item.hover) {
  background-color: rgba(31, 111, 235, 0.1);
}

:deep(.el-empty__description) {
  color: #666666;
}
</style>
