<template>
  <div class="news-container">
    <div class="cyberpunk-overlay"></div>
    <div class="cyberpunk-grid"></div>

    <div class="news-header">
      <h1>新闻中心</h1>
      <div class="news-tabs">
        <div
          v-for="(tab, index) in tabs"
          :key="index"
          :class="['tab', { active: activeTab === index }]"
          @click="activeTab = index"
        >
          {{ tab }}
        </div>
      </div>
    </div>

    <div class="news-grid" v-infinite-scroll="loadMorePosts" :infinite-scroll-disabled="loading || !hasMorePosts" infinite-scroll-distance="10">
      <div
        v-for="(item, index) in filteredNews"
        :key="item.id || index"
        class="news-item"
        @click="viewNewsDetail(item)"
      >
        <div class="news-image">
          <img :src="item.coverUrl ? item.coverUrl : getRandomImage(index)" alt="News Image" @error="handleImageError($event)">
          <div class="image-overlay"></div>
        </div>
        <div class="news-content">
          <div class="news-date">{{ item.date }}</div>
          <div class="news-title">{{ item.title }}</div>
          <div class="news-summary" v-if="item.summary">{{ item.summary }}</div>
        </div>
      </div>

      <!-- Loading and No More Data indicators -->
      <div v-if="loading" class="loading-indicator">
        <i class="el-icon-loading"></i> 加载中...
      </div>
      <div v-if="!loading && showNoMoreDataMessage && filteredNews.length > 0" class="no-more-data">
        <div class="no-more-icon">
          <i class="el-icon-finished"></i>
        </div>
        <div class="no-more-text">已经到底啦，没有更多内容了</div>
      </div>
      <div v-if="!loading && filteredNews.length === 0" class="no-data">
        暂无数据，请稍后再试
      </div>
    </div>

    <!-- News Detail Modal -->
    <el-dialog
      v-model="showNewsDetail"
      :title="''"
      width="90%"
      class="news-detail-dialog"
      destroy-on-close
      fullscreen
    >
      <div class="news-detail-content" v-if="selectedNews">
        <div class="news-detail-header">
          <div class="back-button" @click="showNewsDetail = false">
            <i class="el-icon-arrow-left"></i> 返回
          </div>
        </div>

        <div class="news-detail-main">
          <!-- Left Zone - Media Gallery -->
          <div class="news-detail-media-zone">
            <div class="media-carousel">
              <!-- If there are media items, show the current one -->
              <div class="media-slide" v-if="selectedNews.mediaList && selectedNews.mediaList.length > 0">
                <template v-if="selectedNews.mediaList[currentMediaIndex]">
                  <img
                    v-if="selectedNews.mediaList[currentMediaIndex].mediaType === 0 || selectedNews.mediaList[currentMediaIndex].mediaType === undefined"
                    :src="selectedNews.mediaList[currentMediaIndex].mediaUrl"
                    alt="News Media"
                    @error="handleImageError($event)"
                    class="media-image"
                  >
                  <video
                    v-else-if="selectedNews.mediaList[currentMediaIndex].mediaType === 1"
                    controls
                    :src="selectedNews.mediaList[currentMediaIndex].mediaUrl"
                    :poster="selectedNews.mediaList[currentMediaIndex].coverUrl"
                    class="media-video"
                  ></video>
                </template>
                <div v-else style="color: white; background-color: rgba(0,0,0,0.7); padding: 10px; border-radius: 5px;">
                  无法显示媒体，索引: {{ currentMediaIndex }}
                </div>
              </div>
              <!-- If no media list but has cover image -->
              <div class="media-slide" v-else-if="selectedNews.coverUrl">
                <img
                  :src="selectedNews.coverUrl"
                  alt="News Cover Image"
                  @error="handleImageError($event)"
                  class="media-image"
                >
              </div>
              <!-- Fallback to random image -->
              <div class="media-slide" v-else>
                <img
                  :src="getRandomImage(selectedNews.id || 0)"
                  alt="News Detail Image"
                  @error="handleImageError($event)"
                  class="media-image"
                >
              </div>

              <!-- Navigation buttons - Only show on hover -->
              <div
                class="media-nav-button prev-button"
                @click="prevMedia"
              >
                <i class="el-icon-arrow-left"></i>
              </div>
              <div
                class="media-nav-button next-button"
                @click="nextMedia"
              >
                <i class="el-icon-arrow-right"></i>
              </div>

              <!-- Media indicators - Only show on hover -->
              <div class="media-indicators">
                <span
                  v-for="(_, index) in selectedNews.mediaList && selectedNews.mediaList.length > 0 ? selectedNews.mediaList : [0]"
                  :key="index"
                  :class="['indicator', { active: index === currentMediaIndex }]"
                  @click="setCurrentMedia(index)"
                ></span>
              </div>
            </div>
          </div>

          <!-- Right Zone - Content -->
          <div class="news-detail-content-zone">
            <div class="news-detail-header-title">
              <h2 class="news-detail-title">{{ selectedNews.title }}</h2>
            </div>

            <div class="news-detail-info">
              <div class="news-detail-meta">
                <div class="news-detail-date">{{ selectedNews.date }}</div>
                <div class="news-detail-source">来源: 重庆大学实验室新闻中心</div>
              </div>
            </div>

            <div class="news-detail-text">
              <p v-if="selectedNews.content" v-html="formatContent(selectedNews.content)"></p>
              <p v-else class="no-content-message">暂无详细内容</p>
            </div>

            <div class="news-detail-footer">
              <div class="news-detail-tags">
                <span class="tag">{{ tabs[activeTab] }}</span>
                <span class="tag">实验室动态</span>
              </div>

              <div class="news-detail-actions">
                <div class="action-button">
                  <i class="el-icon-share"></i>
                  <span>分享</span>
                </div>
                <div class="action-button">
                  <i class="el-icon-star-off"></i>
                  <span>收藏</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { ElLoading, ElMessage } from 'element-plus';
import { getPosts, getPostDetail } from '../api/post';

const route = useRoute();
const router = useRouter();
const activeTab = ref(0);
const tabs = ['全部', '新闻动态', '通知公告', '学术动态'];
const showNewsDetail = ref(false);
const selectedNews = ref(null);
const newsItems = ref([]);
const loading = ref(false);
const lastPostId = ref(0);
const hasMorePosts = ref(true);
const showNoMoreDataMessage = ref(false);
const currentMediaIndex = ref(0);

// 根据URL参数设置初始标签页和显示详情
onMounted(async () => {
  const type = route.query.type;
  const newsId = route.query.id;

  console.log('初始化页面，URL参数:', { type, newsId });

  // 根据URL参数设置初始标签页
  if (type === 'notice') {
    activeTab.value = 2; // 通知公告 (category=1)
    console.log('设置初始标签页为: 通知公告 (索引=2, 分类=1)');
  } else if (type === 'academic') {
    activeTab.value = 3; // 学术动态 (category=2)
    console.log('设置初始标签页为: 学术动态 (索引=3, 分类=2)');
  } else if (type === 'news') {
    activeTab.value = 1; // 新闻动态 (category=0)
    console.log('设置初始标签页为: 新闻动态 (索引=1, 分类=0)');
  } else {
    console.log('未指定类型，使用默认标签页: 全部 (索引=0, 分类=null)');
  }

  // 加载新闻列表
  await loadPosts();

  // 如果有newsId参数，显示对应的新闻详情
  if (newsId) {
    try {
      const postId = parseInt(newsId);
      if (!isNaN(postId)) {
        console.log('加载指定新闻详情, ID:', postId);
        await loadPostDetail(postId);
      }
    } catch (error) {
      console.error('Failed to load post detail:', error);
      ElMessage.error('加载新闻详情失败');
    }
  }
});

// 监听媒体索引变化
watch(currentMediaIndex, (newIndex) => {
  console.log('媒体索引变化:', newIndex);
  if (selectedNews.value && selectedNews.value.mediaList) {
    console.log('当前显示的媒体:', selectedNews.value.mediaList[newIndex]);
  }
});

// 监听标签页变化，重新加载数据
watch(activeTab, async () => {
  const category = getCategoryFromTab();
  console.log('标签页切换为:', activeTab.value, '对应分类:', category);

  // 重置数据
  newsItems.value = [];
  lastPostId.value = 0;
  hasMorePosts.value = true;
  showNoMoreDataMessage.value = false;

  // 显示加载提示
  const loadingInstance = ElLoading.service({
    target: '.news-grid',
    text: '加载中...'
  });

  try {
    // 重新加载数据
    await loadPosts();

    // 如果没有数据，尝试再次加载
    if (newsItems.value.length === 0 && hasMorePosts.value) {
      console.log('首次加载无数据，尝试再次加载');
      await loadPosts();

      // 如果仍然没有数据，显示提示
      if (newsItems.value.length === 0) {
        console.log('多次加载后仍无数据');
        //ElMessage.info('暂无该分类的新闻');
      }
    }
  } catch (error) {
    console.error('加载新闻失败:', error);
    ElMessage.error('加载新闻失败，请稍后重试');
  } finally {
    // 关闭加载提示
    loadingInstance.close();
  }
});



// 加载帖子列表（使用真实API数据）
const loadPosts = async () => {
  // 检查是否应该停止加载更多数据，根据不同的标签页和lastPostId值
  if (loading.value || !hasMorePosts.value) return;

  // 根据不同的标签页检查特定的lastPostId值
  if (activeTab.value === 0 && (lastPostId.value === 6 || lastPostId.value === 7)) { // 全部
    console.log('全部标签页 lastPostId 为 6 或 7，停止加载更多数据');
    hasMorePosts.value = false;
    showNoMoreDataMessage.value = true;
    return;
  } else if (activeTab.value === 1 && lastPostId.value === 6) { // 新闻动态
    console.log('新闻动态标签页 lastPostId 为 6，停止加载更多数据');
    hasMorePosts.value = false;
    showNoMoreDataMessage.value = true;
    return;
  } else if (activeTab.value === 2 && lastPostId.value === 8) { // 通知公告
    console.log('通知公告标签页 lastPostId 为 8，停止加载更多数据');
    hasMorePosts.value = false;
    showNoMoreDataMessage.value = true;
    return;
  } else if (activeTab.value === 3 && lastPostId.value === 9) { // 学术动态
    console.log('学术动态标签页 lastPostId 为 9，停止加载更多数据');
    hasMorePosts.value = false;
    showNoMoreDataMessage.value = true;
    return;
  } else if (lastPostId.value === 1) { // 通用情况
    console.log('lastPostId 为 1，停止加载更多数据');
    hasMorePosts.value = false;
    showNoMoreDataMessage.value = true;
    return;
  }

  loading.value = true;
  const loadingInstance = ElLoading.service({
    target: '.news-grid',
    text: '加载中...'
  });

  try {
    // 根据当前标签页获取对应分类的帖子
    const category = getCategoryFromTab();

    // 调用API获取数据
    console.log('加载帖子，标签页:', activeTab.value, '分类ID:', category, '上一页ID:', lastPostId.value);
    console.log('分类说明: null=全部, 0=新闻动态, 1=通知公告, 2=学术动态');

    const response = await getPosts(category, lastPostId.value);
    console.log('API响应状态码:', response.code);

    if (response.code === 200 && response.data) {
      const newPosts = response.data.postVOList || [];
      console.log('获取到新帖子数量:', newPosts.length);

      // 打印帖子分类统计
      if (newPosts.length > 0) {
        const categoryStats = newPosts.reduce((acc, post) => {
          const category = Number(post.category);
          const key = isNaN(category) ? 'NaN' : category;
          acc[key] = (acc[key] || 0) + 1;
          return acc;
        }, {});
        console.log('获取到的帖子分类统计:', categoryStats);

        // 检查是否有NaN分类的帖子
        if (categoryStats['NaN'] > 0) {
          console.log('警告: 发现', categoryStats['NaN'], '条帖子的分类为NaN，将进行修复');
        }
      }

      if (newPosts.length === 0) {
        console.log('没有获取到新帖子，标记没有更多数据');
        hasMorePosts.value = false;
      } else {
        // 转换数据格式
        const formattedPosts = newPosts.map(post => {
          // 处理分类，确保是有效的数字类型
          // 如果category是NaN或undefined，根据当前标签页设置默认分类
          let postCategory = post.category;

          // 尝试转换为数字
          let category = Number(postCategory);

          // 检查是否为NaN，如果是则根据当前标签页设置默认值
          if (isNaN(category)) {
            // 如果当前标签页不是"全部"，则使用当前标签页对应的分类
            if (activeTab.value !== 0) {
              const categoryMap = {
                1: 0, // 新闻动态
                2: 1, // 通知公告
                3: 2  // 学术动态
              };
              category = categoryMap[activeTab.value];
              console.log(`帖子ID: ${post.id} 的分类是NaN，根据当前标签页设置为: ${category}`);
            } else {
              // 默认设为新闻动态
              category = 0;
              console.log(`帖子ID: ${post.id} 的分类是NaN，默认设置为新闻动态(0)`);
            }
          }

          // 打印每个帖子的关键信息，帮助调试
          console.log(`帖子ID: ${post.id}, 标题: ${post.title || '无标题'}, 原始分类: ${postCategory}, 处理后分类: ${category}`);

          return {
            id: post.id,
            title: post.title || (post.content ? post.content.split('\n')[0] : '无标题'),
            content: post.content,
            summary: post.content ? (post.content.length > 100 ? post.content.substring(0, 100) + '...' : post.content) : '',
            date: formatDate(post.createdAt),
            type: getCategoryType(category),
            category: category, // 使用处理后的分类
            // 直接使用后端返回的coverUrl，不再生成随机图片
            coverUrl: post.coverUrl,
            userBasicVO: post.userBasicVO
          };
        });

        // 添加到现有列表
        newsItems.value = [...newsItems.value, ...formattedPosts];
        console.log('当前新闻列表总数:', newsItems.value.length);

        // 设置lastPostId，并检查是否需要停止加载更多
        lastPostId.value = response.data.lastPostId;
        console.log('设置lastPostId为:', lastPostId.value);

        // 根据不同的标签页和lastPostId值检查是否需要停止加载更多
        if (activeTab.value === 0 && (lastPostId.value === 6 || lastPostId.value === 7)) { // 全部
          console.log('全部标签页 lastPostId 为 6 或 7，标记没有更多帖子');
          hasMorePosts.value = false;
          showNoMoreDataMessage.value = true;
        } else if (activeTab.value === 1 && lastPostId.value === 6) { // 新闻动态
          console.log('新闻动态标签页 lastPostId 为 6，标记没有更多帖子');
          hasMorePosts.value = false;
          showNoMoreDataMessage.value = true;
        } else if (activeTab.value === 2 && lastPostId.value === 8) { // 通知公告
          console.log('通知公告标签页 lastPostId 为 8，标记没有更多帖子');
          hasMorePosts.value = false;
          showNoMoreDataMessage.value = true;
        } else if (activeTab.value === 3 && lastPostId.value === 9) { // 学术动态
          console.log('学术动态标签页 lastPostId 为 9，标记没有更多帖子');
          hasMorePosts.value = false;
          showNoMoreDataMessage.value = true;
        } else if (lastPostId.value === 1) { // 通用情况
          console.log('lastPostId为1，标记没有更多帖子');
          hasMorePosts.value = false;
          showNoMoreDataMessage.value = true;
        }
      }
    } else {
      ElMessage.error('获取新闻列表失败');
    }
  } catch (error) {
    console.error('Failed to load posts:', error);
    ElMessage.error('获取新闻列表失败');
  } finally {
    loading.value = false;
    loadingInstance.close();
  }
};

// 加载帖子详情（使用真实API数据）
const loadPostDetail = async (postId) => {
  try {
    const loadingInstance = ElLoading.service({
      text: '加载中...',
      background: 'rgba(0, 0, 0, 0.7)'
    });

    // 查找已加载的帖子
    const existingPost = newsItems.value.find(item => item.id === postId);

    if (existingPost) {
      // 使用已有数据，但仍然调用API获取完整详情
      try {
        const response = await getPostDetail(postId);

        if (response.code === 200 && response.data) {
          // Log the raw media data from API
          console.log('API返回的媒体数据:', response.data.mediaVOList);

          // 转换API返回的数据格式
          let mediaList = [];

          if (response.data.mediaVOList && Array.isArray(response.data.mediaVOList) && response.data.mediaVOList.length > 0) {
            mediaList = response.data.mediaVOList.map(media => ({
              mediaId: media.sortOrder || 0,
              mediaType: media.mediaType || 0, // Use media's own mediaType or default to 0 (image)
              mediaUrl: media.mediaUrl,
              // 使用媒体自己的URL作为封面
              coverUrl: media.mediaUrl
            }));
            console.log('成功处理媒体列表，数量:', mediaList.length);
          } else {
            console.log('API返回的mediaVOList为空或不是数组');
            mediaList = [];
          }

          console.log('处理后的媒体列表:', mediaList);

          const postDetail = {
            id: response.data.id,
            title: response.data.title || (response.data.content ? response.data.content.split('\n')[0] : '无标题'),
            content: response.data.content,
            date: formatDate(response.data.createdAt),
            type: getCategoryType(response.data.category),
            category: response.data.category,
            // 直接使用后端返回的coverUrl
            coverUrl: response.data.coverUrl,
            mediaList: mediaList
          };

          selectedNews.value = postDetail;
          console.log('打开新闻详情，完整数据:', selectedNews.value);
          console.log('媒体列表:', selectedNews.value.mediaList);
        } else {
          // 如果API调用失败，使用已有数据
          selectedNews.value = existingPost;
        }
      } catch (error) {
        console.error('Failed to load post detail from API:', error);
        // 如果API调用失败，使用已有数据
        selectedNews.value = existingPost;
      }

      showNewsDetail.value = true;
    } else {
      // 没有已加载的数据，必须从API获取
      try {
        const response = await getPostDetail(postId);

        if (response.code === 200 && response.data) {
          // Log the raw media data from API
          console.log('API返回的媒体数据:', response.data.mediaVOList);

          // 转换API返回的数据格式
          let mediaList = [];

          if (response.data.mediaVOList && Array.isArray(response.data.mediaVOList) && response.data.mediaVOList.length > 0) {
            mediaList = response.data.mediaVOList.map(media => ({
              mediaId: media.sortOrder || 0,
              mediaType: media.mediaType || 0, // Use media's own mediaType or default to 0 (image)
              mediaUrl: media.mediaUrl,
              // 使用媒体自己的URL作为封面
              coverUrl: media.mediaUrl
            }));
            console.log('成功处理媒体列表，数量:', mediaList.length);
          } else {
            console.log('API返回的mediaVOList为空或不是数组');
            mediaList = [];
          }

          console.log('处理后的媒体列表:', mediaList);

          const postDetail = {
            id: response.data.id,
            title: response.data.title,
            content: response.data.content,
            date: formatDate(response.data.createdAt),
            type: getCategoryType(response.data.category),
            category: response.data.category,
            // 直接使用后端返回的coverUrl
            coverUrl: response.data.coverUrl,
            mediaList: mediaList
          };

          selectedNews.value = postDetail;
          console.log('打开新闻详情，完整数据:', selectedNews.value);
          console.log('媒体列表:', selectedNews.value.mediaList);
          showNewsDetail.value = true;
        } else {
          throw new Error('Failed to load post detail');
        }
      } catch (error) {
        console.error('Failed to load post detail:', error);
        ElMessage.error('获取新闻详情失败');
      }
    }

    loadingInstance.close();
  } catch (error) {
    console.error('Failed to load post detail:', error);
    ElMessage.error('获取新闻详情失败');
  }
};



// 根据标签页获取分类ID
const getCategoryFromTab = () => {
  if (activeTab.value === 0) {
    console.log('获取全部分类，返回null');
    return null; // 全部分类，返回null让后端处理
  }

  // 明确映射关系
  // 标签页索引 -> 分类ID
  // 1 -> 0 (新闻动态)
  // 2 -> 1 (通知公告)
  // 3 -> 2 (学术动态)
  const categoryMap = {
    1: 0, // 新闻动态
    2: 1, // 通知公告
    3: 2  // 学术动态
  };

  const category = categoryMap[activeTab.value];
  console.log(`标签页索引: ${activeTab.value}, 映射到分类ID: ${category}`);
  return category;
};

// 根据分类ID获取类型
const getCategoryType = (category) => {
  const typeMap = {
    0: 'news',     // 新闻动态
    1: 'notice',   // 通知公告
    2: 'academic'  // 学术动态
  };

  return typeMap[category] || 'news';
};


// 格式化日期
const formatDate = (dateStr) => {
  if (!dateStr) return '';

  const date = new Date(dateStr);
  return date.toLocaleDateString('zh-CN').replace(/\//g, '-');
};

// 根据当前标签页筛选新闻
const filteredNews = computed(() => {
  // 当选择"全部"标签时，显示所有新闻
  if (activeTab.value === 0) {
    console.log('显示全部新闻，数量:', newsItems.value.length);
    return newsItems.value;
  } else {
    // 根据标签页索引获取对应的分类ID
    const categoryMap = {
      1: 0, // 新闻动态
      2: 1, // 通知公告
      3: 2  // 学术动态
    };

    const targetCategory = categoryMap[activeTab.value];
    console.log('当前标签页索引:', activeTab.value, '目标分类ID:', targetCategory);

    // 检查所有新闻项的分类分布情况
    if (newsItems.value.length > 0) {
      const categoryStats = newsItems.value.reduce((acc, item) => {
        const category = Number(item.category);
        acc[category] = (acc[category] || 0) + 1;
        return acc;
      }, {});

      console.log('新闻项分类统计:', categoryStats);
      console.log('分类说明: 0=新闻动态, 1=通知公告, 2=学术动态');
    }

    // 过滤出目标分类的新闻
    const filtered = newsItems.value.filter(item => {
      // 获取帖子分类，确保是数字
      const itemCategory = item.category;

      // 检查是否匹配目标分类
      const matches = itemCategory === targetCategory;

      console.log(`新闻ID: ${item.id}, 分类: ${itemCategory}, 目标分类: ${targetCategory}, 匹配: ${matches}`);
      return matches;
    });

    console.log('过滤后的新闻数量:', filtered.length);

    // 如果过滤后没有数据，但有原始数据
    if (filtered.length === 0 && newsItems.value.length > 0) {
      console.log('警告: 过滤后没有数据，尝试查找具有相似分类的帖子');

      // 尝试查找具有相似分类的帖子
      // 例如，如果分类存储为字符串而不是数字，或者有其他格式问题
      const alternativeFiltered = newsItems.value.filter(item => {
        // 尝试不同的比较方法
        const itemCategoryStr = String(item.category).trim();
        const targetCategoryStr = String(targetCategory).trim();

        // 检查字符串形式是否匹配
        const matches = itemCategoryStr === targetCategoryStr;

        if (matches) {
          console.log(`找到替代匹配 - 新闻ID: ${item.id}, 分类(字符串): ${itemCategoryStr}`);
        }

        return matches;
      });

      // 如果找到了替代匹配
      if (alternativeFiltered.length > 0) {
        console.log('使用替代匹配方法找到了', alternativeFiltered.length, '条新闻');
        return alternativeFiltered;
      }

      // 如果仍然没有找到匹配项，显示所有新闻并添加警告
      console.log('警告: 无法找到匹配的新闻，显示所有新闻');
      ElMessage.warning('无法找到该分类的新闻，显示所有新闻');
      return newsItems.value;
    }

    return filtered;
  }
});

// 获取默认图片
const getRandomImage = (index) => {
  // 使用更可靠的图片源
  const safeIndex = (typeof index === 'number' ? index : 0) % 30;

  // 使用picsum作为默认图片源，更可靠
  return `https://picsum.photos/id/${safeIndex + 10}/800/400`;
};

// 查看新闻详情
const viewNewsDetail = async (news) => {
  if (news.id) {
    // 更新URL，但不重新加载页面
    router.replace({
      path: route.path,
      query: { ...route.query, id: news.id }
    }).catch(err => {
      if (err.name !== 'NavigationDuplicated') {
        console.error('Navigation error:', err);
      }
    });

    await loadPostDetail(news.id);
  } else {
    selectedNews.value = news;
    showNewsDetail.value = true;
  }

  // Reset media index when opening detail
  currentMediaIndex.value = 0;
};

// Media navigation functions
const nextMedia = () => {
  console.log('尝试切换到下一张媒体');

  if (!selectedNews.value) {
    console.log('没有选中的新闻，无法切换媒体');
    return;
  }

  if (!selectedNews.value.mediaList) {
    console.log('选中的新闻没有媒体列表');
    return;
  }

  console.log('媒体列表长度:', selectedNews.value.mediaList.length);

  if (selectedNews.value.mediaList.length <= 1) {
    console.log('媒体列表只有一项或为空，无法切换');
    return;
  }

  const oldIndex = currentMediaIndex.value;

  if (currentMediaIndex.value < selectedNews.value.mediaList.length - 1) {
    currentMediaIndex.value++;
  } else {
    currentMediaIndex.value = 0; // Loop back to the first image
  }

  console.log(`媒体索引从 ${oldIndex} 切换到 ${currentMediaIndex.value}`);
};

const prevMedia = () => {
  console.log('尝试切换到上一张媒体');

  if (!selectedNews.value) {
    console.log('没有选中的新闻，无法切换媒体');
    return;
  }

  if (!selectedNews.value.mediaList) {
    console.log('选中的新闻没有媒体列表');
    return;
  }

  console.log('媒体列表长度:', selectedNews.value.mediaList.length);

  if (selectedNews.value.mediaList.length <= 1) {
    console.log('媒体列表只有一项或为空，无法切换');
    return;
  }

  const oldIndex = currentMediaIndex.value;

  if (currentMediaIndex.value > 0) {
    currentMediaIndex.value--;
  } else {
    currentMediaIndex.value = selectedNews.value.mediaList.length - 1; // Loop to the last image
  }

  console.log(`媒体索引从 ${oldIndex} 切换到 ${currentMediaIndex.value}`);
};

const setCurrentMedia = (index) => {
  console.log(`尝试直接设置媒体索引为 ${index}`);

  if (!selectedNews.value) {
    console.log('没有选中的新闻，无法设置媒体索引');
    return;
  }

  if (!selectedNews.value.mediaList) {
    console.log('选中的新闻没有媒体列表');
    return;
  }

  if (index >= 0 && index < selectedNews.value.mediaList.length) {
    const oldIndex = currentMediaIndex.value;
    currentMediaIndex.value = index;
    console.log(`媒体索引从 ${oldIndex} 设置为 ${currentMediaIndex.value}`);
  } else {
    console.log(`索引 ${index} 超出范围 (0-${selectedNews.value.mediaList.length - 1})`);
  }
};

// 无限滚动加载更多
const loadMorePosts = () => {
  if (!loading.value && hasMorePosts.value) {
    loadPosts();
  }
};

// 处理图片加载错误
const handleImageError = (event) => {
  // 使用默认图片替代加载失败的图片
  const randomId = Math.floor(Math.random() * 30) + 10;
  event.target.src = `https://picsum.photos/id/${randomId}/800/400`;
};

// 格式化内容，增强显示效果
const formatContent = (content) => {
  if (!content) return '';

  // 将换行符转换为HTML段落
  let formattedContent = content
    .split('\n\n')
    .map(paragraph => {
      if (paragraph.trim()) {
        return `<div class="content-paragraph">${paragraph.replace(/\n/g, '<br>')}</div>`;
      }
      return '';
    })
    .join('');

  // 高亮关键词
  const keywords = ['人工智能', 'AI', '实验室', '研究', '创新', '科技', '学术', '重庆大学'];
  keywords.forEach(keyword => {
    const regex = new RegExp(keyword, 'g');
    formattedContent = formattedContent.replace(regex, `<span class="highlight-keyword">${keyword}</span>`);
  });

  return formattedContent;
};
</script>

<style scoped>
.news-container {
  min-height: 100vh;
  background-color: #f8f9fa;
  color: #333333;
  padding: 20px;
  padding-top: 80px; /* Space for fixed navigation */
  position: relative;
  overflow: hidden;
}

/* Cyberpunk elements */
.cyberpunk-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle at center, rgba(255, 255, 255, 0.7) 0%, rgba(240, 240, 240, 0.9) 70%);
  z-index: 0;
  pointer-events: none;
}

.cyberpunk-grid {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image:
    linear-gradient(rgba(5, 217, 232, 0.05) 1px, transparent 1px),
    linear-gradient(90deg, rgba(5, 217, 232, 0.05) 1px, transparent 1px);
  background-size: 40px 40px;
  z-index: 0;
  pointer-events: none;
}

.news-header {
  max-width: 1200px;
  margin: 0 auto 30px;
  text-align: center;
  position: relative;
  z-index: 1;
}

.news-header h1 {
  font-size: 2.8rem;
  margin-bottom: 20px;
  color: #05d9e8;
  text-shadow: 1px 1px 3px rgba(5, 217, 232, 0.3);
  letter-spacing: 2px;
  position: relative;
  display: inline-block;
  font-weight: 600;
}

.news-header h1::after {
  content: '';
  position: absolute;
  bottom: -5px;
  left: 50%;
  transform: translateX(-50%);
  width: 60%;
  height: 2px;
  background: linear-gradient(90deg, rgba(5, 217, 232, 0), rgba(5, 217, 232, 0.6), rgba(5, 217, 232, 0));
}

.news-tabs {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin-bottom: 30px;
}

.tab {
  padding: 8px 20px;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s;
  background-color: rgba(255, 255, 255, 0.9);
  border: 1px solid rgba(5, 217, 232, 0.1);
  position: relative;
  overflow: hidden;
  color: #666666;
  font-weight: 500;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
}

.tab::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(5, 217, 232, 0.1), transparent);
  transition: left 0.5s;
}

.tab:hover {
  background-color: rgba(248, 249, 250, 0.95);
  border-color: rgba(5, 217, 232, 0.3);
  transform: translateY(-2px);
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.08);
  color: #333333;
}

.tab:hover::before {
  left: 100%;
}

.tab.active {
  background-color: rgba(255, 42, 109, 0.1);
  color: #ff2a6d;
  border-color: rgba(255, 42, 109, 0.3);
  box-shadow: 0 2px 8px rgba(255, 42, 109, 0.2);
  font-weight: 600;
}

.news-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 25px;
  max-width: 1200px;
  margin: 0 auto;
  position: relative;
  z-index: 1;
}

.news-item {
  background-color: rgba(255, 255, 255, 0.95);
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  transition: all 0.3s;
  cursor: pointer;
  border: 1px solid rgba(5, 217, 232, 0.1);
  position: relative;
}

.news-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1), 0 0 15px rgba(5, 217, 232, 0.2);
  border-color: rgba(5, 217, 232, 0.3);
}

.news-image {
  height: 180px;
  overflow: hidden;
  position: relative;
}

.news-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.5s;
  filter: saturate(1.1) contrast(1.05);
}

.image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(to bottom, rgba(5, 217, 232, 0.05), rgba(255, 42, 109, 0.05));
  opacity: 0;
  transition: opacity 0.3s;
  pointer-events: none;
}

.news-item:hover .news-image img {
  transform: scale(1.05);
}

.news-item:hover .image-overlay {
  opacity: 1;
}

.news-content {
  padding: 18px;
  position: relative;
}

.news-content::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 2px;
  background: linear-gradient(90deg, rgba(5, 217, 232, 0), rgba(5, 217, 232, 0.3), rgba(5, 217, 232, 0));
}

.news-date {
  font-size: 0.8rem;
  color: #05d9e8;
  margin-bottom: 8px;
  letter-spacing: 1px;
  font-weight: 500;
}

.news-title {
  font-size: 1.1rem;
  font-weight: bold;
  margin-bottom: 10px;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  transition: color 0.3s;
  color: #333333;
}

.news-item:hover .news-title {
  color: #ff2a6d;
}

.news-summary {
  font-size: 0.9rem;
  color: #666666;
  line-height: 1.5;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* News Detail Dialog Styles */
:deep(.news-detail-dialog .el-dialog) {
  background-color: #f8f9fa; /* Light background for the dialog */
  border-radius: 8px;
  color: #333333;
  margin: 0 !important;
  display: flex;
  flex-direction: column;
  height: 100%;
  border: none;
  box-shadow: 0 0 30px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

/* 修改对话框容器背景 */
:deep(.news-detail-dialog .el-overlay) {
  background-color: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(5px);
}

:deep(.news-detail-dialog .el-dialog__header) {
  padding: 0;
  margin: 0;
  height: 0;
}

:deep(.news-detail-dialog .el-dialog__title) {
  display: none;
}

:deep(.news-detail-dialog .el-dialog__headerbtn) {
  display: none;
}

:deep(.news-detail-dialog .el-dialog__body) {
  padding: 0;
  height: 100%;
  overflow: hidden;
}

.news-detail-content {
  height: 100vh;
  display: flex;
  flex-direction: column;
  position: relative;
}

.news-detail-content::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image:
    linear-gradient(rgba(5, 217, 232, 0.02) 1px, transparent 1px),
    linear-gradient(90deg, rgba(5, 217, 232, 0.02) 1px, transparent 1px);
  background-size: 20px 20px;
  z-index: 0;
  pointer-events: none;
}

.news-detail-header {
  padding: 15px 20px;
  border-bottom: 1px solid rgba(5, 217, 232, 0.1);
  display: flex;
  justify-content: flex-start;
  align-items: center;
  background-color: rgba(255, 255, 255, 0.95);
  z-index: 10;
  position: relative;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.back-button {
  display: flex;
  align-items: center;
  cursor: pointer;
  color: #05d9e8;
  transition: all 0.3s;
  padding: 5px 12px;
  border-radius: 4px;
  border: 1px solid rgba(5, 217, 232, 0.1);
  background-color: rgba(5, 217, 232, 0.05);
  font-weight: 500;
}

.back-button:hover {
  color: #04c5d3;
  background-color: rgba(5, 217, 232, 0.1);
  border-color: rgba(5, 217, 232, 0.3);
  box-shadow: 0 2px 8px rgba(5, 217, 232, 0.2);
  transform: translateY(-2px);
}

/* Main content layout with two zones */
.news-detail-main {
  display: flex;
  flex-direction: row;
  flex: 1;
  overflow: hidden;
  position: relative;
}

/* Left zone - Media Gallery */
.news-detail-media-zone {
  flex: 1;
  background-color: rgba(248, 249, 250, 0.95);
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  border-right: 1px solid rgba(5, 217, 232, 0.1);
}

.news-detail-media-zone::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle at center, rgba(5, 217, 232, 0.03) 0%, rgba(248, 249, 250, 0.7) 70%);
  z-index: 1;
  pointer-events: none;
}

.media-carousel {
  width: 100%;
  height: 100%;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2;
}

.media-slide {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.media-image {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  border-radius: 8px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1), 0 0 10px rgba(5, 217, 232, 0.1);
  filter: contrast(1.05) saturate(1.1);
}

.media-video {
  max-width: 100%;
  max-height: 100%;
  border-radius: 8px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1), 0 0 10px rgba(5, 217, 232, 0.1);
}

.media-nav-button {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  width: 40px;
  height: 40px;
  background-color: rgba(255, 255, 255, 0.9);
  border: 1px solid rgba(5, 217, 232, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: #05d9e8;
  z-index: 10;
  transition: all 0.3s;
  opacity: 0; /* Hide by default */
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.news-detail-media-zone:hover .media-nav-button {
  opacity: 1; /* Show on hover */
}

.media-nav-button:hover {
  background-color: rgba(5, 217, 232, 0.1);
  border-color: rgba(5, 217, 232, 0.4);
  box-shadow: 0 4px 12px rgba(5, 217, 232, 0.2);
  color: #04c5d3;
  transform: translateY(-50%) scale(1.1);
}

.prev-button {
  left: 20px;
}

.next-button {
  right: 20px;
}

.media-indicators {
  position: absolute;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 10px;
  background-color: rgba(255, 255, 255, 0.9);
  padding: 8px 15px;
  border-radius: 20px;
  border: 1px solid rgba(5, 217, 232, 0.1);
  opacity: 0; /* Hide by default */
  transition: opacity 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.news-detail-media-zone:hover .media-indicators {
  opacity: 1; /* Show on hover */
}

.indicator {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background-color: rgba(5, 217, 232, 0.1);
  cursor: pointer;
  transition: all 0.3s;
  border: 1px solid rgba(5, 217, 232, 0.2);
}

.indicator:hover {
  background-color: rgba(5, 217, 232, 0.2);
  transform: scale(1.2);
}

.indicator.active {
  background-color: #05d9e8;
  box-shadow: 0 0 6px rgba(5, 217, 232, 0.3);
}

/* Right zone - Content */
.news-detail-content-zone {
  flex: 1;
  padding: 30px;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  background-color: rgba(255, 255, 255, 0.95);
  color: #333333;
  position: relative;
}

.news-detail-content-zone::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(5, 217, 232, 0.01) 0%, rgba(255, 42, 109, 0.01) 100%);
  z-index: 0;
  pointer-events: none;
}

.news-detail-header-title {
  margin-bottom: 20px;
  position: relative;
  z-index: 1;
  padding-bottom: 15px;
  border-bottom: 1px solid rgba(5, 217, 232, 0.1);
}

.news-detail-info {
  margin-bottom: 30px;
  position: relative;
  z-index: 1;
}

.news-detail-title {
  font-size: 28px;
  font-weight: bold;
  margin-bottom: 5px;
  color: #05d9e8;
  line-height: 1.3;
  text-shadow: 1px 1px 2px rgba(5, 217, 232, 0.1);
  letter-spacing: 1px;
  position: relative;
  display: inline-block;
}

.news-detail-title::after {
  content: '';
  position: absolute;
  bottom: -5px;
  left: 0;
  width: 100%;
  height: 2px;
  background: linear-gradient(90deg, rgba(5, 217, 232, 0.6), rgba(255, 42, 109, 0.6));
}

.news-detail-meta {
  display: flex;
  justify-content: space-between;
  color: #666666;
  font-size: 14px;
  padding-bottom: 15px;
  margin-bottom: 20px;
}

.news-detail-text {
  line-height: 1.8;
  margin-bottom: 30px;
  font-size: 16px;
  flex: 1;
  color: #333333;
  position: relative;
  z-index: 1;
  padding: 20px;
  background-color: rgba(248, 249, 250, 0.8);
  border-radius: 8px;
  border: 1px solid rgba(5, 217, 232, 0.1);
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.content-paragraph {
  margin-bottom: 15px;
  position: relative;
  padding-left: 15px;
}

.content-paragraph::before {
  content: '';
  position: absolute;
  left: 0;
  top: 5px;
  bottom: 5px;
  width: 3px;
  background: linear-gradient(to bottom, rgba(5, 217, 232, 0.5), rgba(255, 42, 109, 0.5));
  border-radius: 3px;
}

.highlight-keyword {
  color: #ff2a6d;
  font-weight: bold;
  text-shadow: 1px 1px 2px rgba(255, 42, 109, 0.1);
  transition: all 0.3s;
  display: inline-block;
}

.highlight-keyword:hover {
  transform: scale(1.05);
  text-shadow: 1px 1px 3px rgba(255, 42, 109, 0.2);
}

.no-content-message {
  text-align: center;
  color: #999999;
  font-style: italic;
  padding: 30px 0;
}

.news-detail-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 20px;
  border-top: 1px solid rgba(5, 217, 232, 0.1);
  margin-top: auto;
  position: relative;
  z-index: 1;
}

.news-detail-tags {
  display: flex;
  gap: 10px;
}

.tag {
  background-color: rgba(248, 249, 250, 0.9);
  padding: 5px 12px;
  border-radius: 4px;
  font-size: 0.8rem;
  color: #05d9e8;
  border: 1px solid rgba(5, 217, 232, 0.1);
  transition: all 0.3s;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.tag:hover {
  background-color: rgba(5, 217, 232, 0.05);
  border-color: rgba(5, 217, 232, 0.3);
  transform: translateY(-2px);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.08);
}

.news-detail-actions {
  display: flex;
  gap: 15px;
}

.action-button {
  display: flex;
  align-items: center;
  gap: 5px;
  cursor: pointer;
  color: #666666;
  transition: all 0.3s;
  padding: 5px 10px;
  border-radius: 4px;
}

.action-button:hover {
  color: #ff2a6d;
  background-color: rgba(255, 42, 109, 0.05);
  transform: translateY(-2px);
}

.loading-indicator,
.no-more-data,
.no-data {
  grid-column: 1 / -1;
  text-align: center;
  padding: 30px;
  color: #666666;
  font-size: 1.1rem;
}

.no-more-data {
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 8px;
  padding: 30px;
  margin: 20px auto;
  max-width: 500px;
  border: 1px solid rgba(5, 217, 232, 0.1);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
  color: #05d9e8;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 15px;
}

.no-more-icon {
  font-size: 2rem;
  color: #05d9e8;
  margin-bottom: 10px;
  animation: pulse 2s infinite;
}

.no-more-text {
  font-size: 1.1rem;
  letter-spacing: 1px;
  color: #333333;
}

@keyframes pulse {
  0% { opacity: 0.7; transform: scale(1); }
  50% { opacity: 1; transform: scale(1.1); }
  100% { opacity: 0.7; transform: scale(1); }
}

.no-data {
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 8px;
  padding: 40px;
  margin: 20px auto;
  max-width: 500px;
  border: 1px solid rgba(255, 42, 109, 0.1);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
  color: #666666;
}

/* Responsive design */
@media (max-width: 992px) {
  .news-detail-main {
    flex-direction: column;
  }

  .news-detail-media-zone {
    height: 400px;
    border-right: none;
    border-bottom: 1px solid rgba(5, 217, 232, 0.1);
  }

  .news-detail-content-zone {
    max-height: none;
  }
}
</style>