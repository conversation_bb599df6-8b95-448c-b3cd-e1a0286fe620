import request from '../utils/request';

/**
 * 获取资源列表
 * @param {Number} category - 分类 1-书籍 2-实验指南 3-视频教程 4-软件工具
 * @param {Number} lastResourceId - 上一页最后一条资源ID，用于分页
 * @returns {Promise} - 返回资源列表
 */
export function getResources(category = null, lastResourceId = 0) {
  return request.get('/resource/list', {
    params: {
      category,
      lastResourceId
    }
  });
}

/**
 * 获取资源详情
 * @param {Number} resourceId - 资源ID
 * @returns {Promise} - 返回资源详情
 */
export function getResourceDetail(resourceId) {
  return request.get('/resource/detail', {
    params: {
      resourceId
    }
  });
}


/**
 * 更新资源
 * @param {Number} resourceId - 资源ID
 * @param {Object} data - 更新数据
 * @returns {Promise} - 返回更新结果
 */
export function updateResource(resourceId, data) {
  // 将对象转换为URLSearchParams
  const params = new URLSearchParams();
  for (const key in data) {
    if (data[key] !== undefined && data[key] !== null) {
      params.append(key, data[key]);
    }
  }

  return request.put(`/resource/${resourceId}`, params, {
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    }
  });
}

/**
 * 删除资源
 * @param {Number} resourceId - 资源ID
 * @returns {Promise} - 返回删除结果
 */
export function deleteResource(resourceId) {
  // 添加调试信息
  console.log('Deleting resource with ID:', resourceId, {
    token: localStorage.getItem('token'),
    userId: localStorage.getItem('userId')
  });

  return request.delete(`/resource/${resourceId}`, {
    headers: {
      'token': localStorage.getItem('token'),
      'userId': localStorage.getItem('userId')
    }
  });
}
