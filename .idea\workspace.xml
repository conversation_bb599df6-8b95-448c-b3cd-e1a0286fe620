<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="3e973e9d-1fae-4af1-a180-1490a81a32ac" name="Changes" comment="私信功能">
      <change afterPath="$PROJECT_DIR$/.cursorrules" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/java/com/cqu/lab/config/ContentTypeConfig.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/java/com/cqu/lab/config/HttpMessageConverterConfig.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/java/com/cqu/lab/config/JacksonMessageConverterConfig.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/java/com/cqu/lab/config/WebMvcContentTypeConfig.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/.idea/compiler.xml" beforeDir="false" afterPath="$PROJECT_DIR$/.idea/compiler.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/.idea/dataSources.xml" beforeDir="false" afterPath="$PROJECT_DIR$/.idea/dataSources.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/.idea/misc.xml" beforeDir="false" afterPath="$PROJECT_DIR$/.idea/misc.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/cqu/lab/config/SecurityConfig.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/cqu/lab/config/SecurityConfig.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/cqu/lab/controller/PrivateMessageController.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/cqu/lab/controller/PrivateMessageController.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/cqu/lab/mapper/PrivateMessageMapper.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/cqu/lab/mapper/PrivateMessageMapper.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/cqu/lab/service/PrivateMessageService.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/cqu/lab/service/PrivateMessageService.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/cqu/lab/service/impl/PrivateMessageServiceImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/cqu/lab/service/impl/PrivateMessageServiceImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/cqu/lab/utils/RedisUtil.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/cqu/lab/utils/RedisUtil.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/resources/application.yml" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/resources/application.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/resources/mapper/PrivateMessageMapper.xml" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/resources/mapper/PrivateMessageMapper.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/resources/static/lab-ui/index.html" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/resources/static/lab-ui/index.html" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/resources/static/lab-ui/src/App.vue" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/resources/static/lab-ui/src/App.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/resources/static/lab-ui/src/api/message.js" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/resources/static/lab-ui/src/api/message.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/resources/static/lab-ui/src/assets/auth.css" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/resources/static/lab-ui/src/assets/auth.css" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/resources/static/lab-ui/src/components/Navigation.vue" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/resources/static/lab-ui/src/components/Navigation.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/resources/static/lab-ui/src/components/NewsSwiper.vue" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/resources/static/lab-ui/src/components/NewsSwiper.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/resources/static/lab-ui/src/style.css" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/resources/static/lab-ui/src/style.css" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/resources/static/lab-ui/src/utils/request.js" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/resources/static/lab-ui/src/utils/request.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/resources/static/lab-ui/src/views/AdminDashboard.vue" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/resources/static/lab-ui/src/views/AdminDashboard.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/resources/static/lab-ui/src/views/EnhancedHome.vue" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/resources/static/lab-ui/src/views/EnhancedHome.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/resources/static/lab-ui/src/views/Home.vue" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/resources/static/lab-ui/src/views/Home.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/resources/static/lab-ui/src/views/LabIntro.vue" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/resources/static/lab-ui/src/views/LabIntro.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/resources/static/lab-ui/src/views/Login.vue" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/resources/static/lab-ui/src/views/Login.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/resources/static/lab-ui/src/views/MessagePage.vue" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/resources/static/lab-ui/src/views/MessagePage.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/resources/static/lab-ui/src/views/News.vue" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/resources/static/lab-ui/src/views/News.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/resources/static/lab-ui/src/views/PostCreate.vue" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/resources/static/lab-ui/src/views/PostCreate.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/resources/static/lab-ui/src/views/PostDetail.vue" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/resources/static/lab-ui/src/views/PostDetail.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/resources/static/lab-ui/src/views/PostWall.vue" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/resources/static/lab-ui/src/views/PostWall.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/resources/static/lab-ui/src/views/PostsDetail.vue" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/resources/static/lab-ui/src/views/PostsDetail.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/resources/static/lab-ui/src/views/Register.vue" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/resources/static/lab-ui/src/views/Register.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/resources/static/lab-ui/src/views/Resources.vue" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/resources/static/lab-ui/src/views/Resources.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/resources/static/lab-ui/src/views/Science.vue" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/resources/static/lab-ui/src/views/Science.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/resources/static/lab-ui/src/views/Search.vue" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/resources/static/lab-ui/src/views/Search.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/resources/static/lab-ui/src/views/Teams.vue" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/resources/static/lab-ui/src/views/Teams.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/resources/static/lab-ui/src/views/UserProfile.vue" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/resources/static/lab-ui/src/views/UserProfile.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/resources/static/lab-ui/src/views/UserView.vue" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/resources/static/lab-ui/src/views/UserView.vue" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Class" />
      </list>
    </option>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="MarkdownSettingsMigration">
    <option name="stateVersion" value="1" />
  </component>
  <component name="MavenRunner">
    <option name="skipTests" value="true" />
  </component>
  <component name="ProblemsViewState">
    <option name="selectedTabId" value="CurrentFile" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 3
}</component>
  <component name="ProjectId" id="2vMXYtDQTqiLfdIMQ93REZUZmM6" />
  <component name="ProjectLevelVcsManager" settingsEditedManually="true">
    <ConfirmationsSetting value="2" id="Add" />
  </component>
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;RequestMappingsPanelOrder0&quot;: &quot;0&quot;,
    &quot;RequestMappingsPanelOrder1&quot;: &quot;1&quot;,
    &quot;RequestMappingsPanelWidth0&quot;: &quot;75&quot;,
    &quot;RequestMappingsPanelWidth1&quot;: &quot;75&quot;,
    &quot;RunOnceActivity.OpenProjectViewOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;SHARE_PROJECT_CONFIGURATION_FILES&quot;: &quot;true&quot;,
    &quot;WebServerToolWindowFactoryState&quot;: &quot;false&quot;,
    &quot;git-widget-placeholder&quot;: &quot;main&quot;,
    &quot;last_opened_file_path&quot;: &quot;D:/googleDownloads/2022fall-master/2022fall-master&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;project.structure.last.edited&quot;: &quot;Project&quot;,
    &quot;project.structure.proportion&quot;: &quot;0.15&quot;,
    &quot;project.structure.side.proportion&quot;: &quot;0.49885058&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;reference.projectsettings.compiler.javacompiler&quot;,
    &quot;ts.external.directory.path&quot;: &quot;D:\\apps\\IntelliJ IDEA 2023.2.5\\plugins\\javascript-impl\\jsLanguageServicesImpl\\external&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  },
  &quot;keyToStringList&quot;: {
    &quot;DatabaseDriversLRU&quot;: [
      &quot;mysql&quot;
    ]
  }
}</component>
  <component name="ReactorSettings">
    <option name="notificationShown" value="true" />
  </component>
  <component name="RecentsManager">
    <key name="MoveClassesOrPackagesDialog.RECENTS_KEY">
      <recent name="com.cqu.lab.mapper" />
    </key>
  </component>
  <component name="RunManager">
    <configuration default="true" type="JetRunConfigurationType">
      <module name="Lab" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration default="true" type="KotlinStandaloneScriptRunConfigurationType">
      <module name="Lab" />
      <option name="filePath" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="LabApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="lab" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.cqu.lab.LabApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="3e973e9d-1fae-4af1-a180-1490a81a32ac" name="Changes" comment="" />
      <created>1743955968198</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1743955968198</updated>
      <workItem from="1743955969206" duration="725000" />
      <workItem from="1744033970738" duration="1219000" />
      <workItem from="1744204494435" duration="706000" />
      <workItem from="1744422496930" duration="15451000" />
      <workItem from="1744471520090" duration="23853000" />
      <workItem from="1744538793956" duration="6771000" />
      <workItem from="1744635939563" duration="3908000" />
      <workItem from="1744728473571" duration="327000" />
      <workItem from="1744728836008" duration="3370000" />
      <workItem from="1744898217299" duration="4497000" />
      <workItem from="1745080062467" duration="619000" />
      <workItem from="1745115055005" duration="11109000" />
      <workItem from="1745149981718" duration="2607000" />
      <workItem from="1745219255163" duration="601000" />
      <workItem from="1745495024435" duration="1700000" />
      <workItem from="1745537221405" duration="642000" />
      <workItem from="1745648192281" duration="7577000" />
      <workItem from="1745718418349" duration="5770000" />
      <workItem from="1745727074361" duration="4761000" />
      <workItem from="1745735114640" duration="1382000" />
      <workItem from="1745736577029" duration="4555000" />
      <workItem from="1745742197858" duration="14972000" />
      <workItem from="1745804020366" duration="41251000" />
      <workItem from="1745916639554" duration="3363000" />
      <workItem from="1745931804581" duration="7529000" />
      <workItem from="1745989247838" duration="2211000" />
      <workItem from="1746099032117" duration="4848000" />
      <workItem from="1746192138068" duration="77005000" />
      <workItem from="1746495548014" duration="1570000" />
      <workItem from="1746497131685" duration="16869000" />
      <workItem from="1746702940490" duration="5507000" />
      <workItem from="1746751938339" duration="22371000" />
      <workItem from="1747017307070" duration="6846000" />
      <workItem from="1747027996975" duration="8379000" />
      <workItem from="1747205400168" duration="7676000" />
      <workItem from="1747754901708" duration="1540000" />
      <workItem from="1747795816954" duration="6756000" />
      <workItem from="1747811884864" duration="1351000" />
      <workItem from="1747813257230" duration="1773000" />
      <workItem from="1747815043401" duration="3718000" />
      <workItem from="1747821085949" duration="2409000" />
      <workItem from="1747829623891" duration="3057000" />
      <workItem from="1748183368563" duration="1504000" />
      <workItem from="1749039759184" duration="1479000" />
      <workItem from="1749044367214" duration="780000" />
      <workItem from="1749045159217" duration="461000" />
      <workItem from="1749045633213" duration="217000" />
      <workItem from="1749045864066" duration="1775000" />
      <workItem from="1749047657442" duration="155000" />
      <workItem from="1749047822887" duration="8000" />
      <workItem from="1749047941195" duration="974000" />
      <workItem from="1749049445120" duration="3150000" />
    </task>
    <task id="LOCAL-00001" summary="用户模块功能">
      <option name="closed" value="true" />
      <created>1744471784965</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1744471784965</updated>
    </task>
    <task id="LOCAL-00002" summary="帖子功能模块后端实现+用户模块前端">
      <option name="closed" value="true" />
      <created>1744724727593</created>
      <option name="number" value="00002" />
      <option name="presentableId" value="LOCAL-00002" />
      <option name="project" value="LOCAL" />
      <updated>1744724727593</updated>
    </task>
    <task id="LOCAL-00003" summary="前端基础框架实现">
      <option name="closed" value="true" />
      <created>1745511664784</created>
      <option name="number" value="00003" />
      <option name="presentableId" value="LOCAL-00003" />
      <option name="project" value="LOCAL" />
      <updated>1745511664784</updated>
    </task>
    <task id="LOCAL-00004" summary="后端框架完成">
      <option name="closed" value="true" />
      <created>1745765190803</created>
      <option name="number" value="00004" />
      <option name="presentableId" value="LOCAL-00004" />
      <option name="project" value="LOCAL" />
      <updated>1745765190803</updated>
    </task>
    <task id="LOCAL-00005" summary="登录功能调通">
      <option name="closed" value="true" />
      <created>1745830036731</created>
      <option name="number" value="00005" />
      <option name="presentableId" value="LOCAL-00005" />
      <option name="project" value="LOCAL" />
      <updated>1745830036731</updated>
    </task>
    <task id="LOCAL-00006" summary="删除target目录">
      <option name="closed" value="true" />
      <created>1745830090087</created>
      <option name="number" value="00006" />
      <option name="presentableId" value="LOCAL-00006" />
      <option name="project" value="LOCAL" />
      <updated>1745830090087</updated>
    </task>
    <task id="LOCAL-00007" summary="调通获取帖子详情，发布帖子等功能">
      <option name="closed" value="true" />
      <created>1745911792370</created>
      <option name="number" value="00007" />
      <option name="presentableId" value="LOCAL-00007" />
      <option name="project" value="LOCAL" />
      <updated>1745911792370</updated>
    </task>
    <task id="LOCAL-00008" summary="调通大部分功能，除了私信功能">
      <option name="closed" value="true" />
      <created>1746495736134</created>
      <option name="number" value="00008" />
      <option name="presentableId" value="LOCAL-00008" />
      <option name="project" value="LOCAL" />
      <updated>1746495736134</updated>
    </task>
    <task id="LOCAL-00009" summary="修改application.yml">
      <option name="closed" value="true" />
      <created>1746496149935</created>
      <option name="number" value="00009" />
      <option name="presentableId" value="LOCAL-00009" />
      <option name="project" value="LOCAL" />
      <updated>1746496149935</updated>
    </task>
    <task id="LOCAL-00010" summary="修改application.yml">
      <option name="closed" value="true" />
      <created>1746496264804</created>
      <option name="number" value="00010" />
      <option name="presentableId" value="LOCAL-00010" />
      <option name="project" value="LOCAL" />
      <updated>1746496264804</updated>
    </task>
    <task id="LOCAL-00011" summary="首页改造，红蓝相间">
      <option name="closed" value="true" />
      <created>1746629247493</created>
      <option name="number" value="00011" />
      <option name="presentableId" value="LOCAL-00011" />
      <option name="project" value="LOCAL" />
      <updated>1746629247494</updated>
    </task>
    <task id="LOCAL-00012" summary="界面优化。">
      <option name="closed" value="true" />
      <created>1746770305268</created>
      <option name="number" value="00012" />
      <option name="presentableId" value="LOCAL-00012" />
      <option name="project" value="LOCAL" />
      <updated>1746770305268</updated>
    </task>
    <task id="LOCAL-00013" summary="私信功能">
      <option name="closed" value="true" />
      <created>1747205784138</created>
      <option name="number" value="00013" />
      <option name="presentableId" value="LOCAL-00013" />
      <option name="project" value="LOCAL" />
      <updated>1747205784138</updated>
    </task>
    <option name="localTasksCounter" value="14" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State />
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <option name="ADD_EXTERNAL_FILES_SILENTLY" value="true" />
    <MESSAGE value="用户模块功能" />
    <MESSAGE value="帖子功能模块后端实现" />
    <MESSAGE value="帖子功能模块后端实现+用户模块前端" />
    <MESSAGE value="前端基础框架实现" />
    <MESSAGE value="后端框架完成" />
    <MESSAGE value="登录功能调通" />
    <MESSAGE value="删除target目录" />
    <MESSAGE value="调通获取帖子详情，发布帖子等功能" />
    <MESSAGE value="调通大部分功能，除了私信功能" />
    <MESSAGE value="修改application.yml" />
    <MESSAGE value="首页改造，红蓝相间" />
    <MESSAGE value="界面优化。" />
    <MESSAGE value="私信功能" />
    <option name="LAST_COMMIT_MESSAGE" value="私信功能" />
  </component>
</project>