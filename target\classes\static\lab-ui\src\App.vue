<template>
  <div id="app">
    <!-- Don't show navigation on login and register pages -->
    <Navigation v-if="!isAuthPage" />
    <div class="main-content">
      <!-- Add key to router-view to force component re-creation when route changes -->
      <router-view v-slot="{ Component }">
        <keep-alive>
          <component :is="Component" :key="route.fullPath" />
        </keep-alive>
      </router-view>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue';
import { useRoute } from 'vue-router';
import Navigation from './components/Navigation.vue';

const route = useRoute();

// Check if current page is login or register
const isAuthPage = computed(() => {
  return route.path === '/login' || route.path === '/register';
});
</script>

<style>
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  height: 100%;
  width: 100%;
  background-color: #f8f9fa;
  overflow-y: auto;
  color: #333333;
  font-size: 18px;
}

#app {
  min-height: 100vh;
  width: 100%;
  display: flex;
  flex-direction: column;
}

.main-content {
  flex: 1;
  width: 100%;
  padding-top: 70px; /* Height of the navigation bar */
  min-height: calc(100vh - 70px);
  position: relative;
  background-color: #f8f9fa; /* Updated to match light theme */
  margin: 0;
  overflow-x: hidden; /* 防止水平滚动条出现 */
}

/* For auth pages */
.auth-page {
  background: linear-gradient(#141e30, #243b55);
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 增大常用文本元素的字体大小 */
p {
  font-size: 18px;
}

span {
  font-size: 18px;
}

div {
  font-size: 18px;
}

li {
  font-size: 18px;
}

h1 {
  font-size: 36px;
}

h2 {
  font-size: 32px;
}

h3 {
  font-size: 28px;
}

h4 {
  font-size: 24px;
}

h5 {
  font-size: 20px;
}

h6 {
  font-size: 18px;
}

.text-sm {
  font-size: 16px;
}

.text-md {
  font-size: 18px;
}

.text-lg {
  font-size: 20px;
}

.text-xl {
  font-size: 24px;
}

.text-2xl {
  font-size: 28px;
}

.text-3xl {
  font-size: 32px;
}

.text-4xl {
  font-size: 36px;
}
</style>
