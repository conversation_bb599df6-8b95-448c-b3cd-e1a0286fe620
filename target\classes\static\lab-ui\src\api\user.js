import request from '../utils/request';

/**
 * 用户登录
 * @param {Object} data - 登录信息
 * @param {string} data.phone - 手机号
 * @param {string} data.password - 密码
 * @returns {Promise}
 */
export function login(data) {
  return request.post('/user/login', data);
}

/**
 * 用户注册
 * @param {Object} data - 注册信息
 * @param {string} data.phone - 手机号
 * @param {string} data.username - 用户名
 * @param {string} data.password - 密码
 * @returns {Promise}
 */
export function register(data) {
  // Remove the /api prefix since it's already in the baseURL
  return request.post('/user/register', data);
}

/**
 * 获取用户信息
 * @param {number} [userId] - 用户ID，不传则获取当前登录用户信息
 * @returns {Promise}
 */
export function getUserInfo(userId) {
  if (userId) {
    return request.get(`/user/info/${userId}`);
  }
  return request.get('/user/info');
}

/**
 * 获取用户基本信息
 * @param {number} userId - 用户ID
 * @returns {Promise}
 */
export function getUserBasicInfo(userId) {
  return request.get(`/user/basic/${userId}`);
}

/**
 * 更新用户信息
 * @param {Object} data - 更新信息
 * @param {string} [data.username] - 用户名
 * @param {string} [data.image] - 头像URL
 * @param {string} [data.signature] - 个性签名
 * @returns {Promise}
 */
export function updateUserInfo(data) {
  return request.put('/user/update', data);
}

/**
 * 关注用户
 * @param {number} followId - 被关注的用户ID
 * @returns {Promise}
 */
export function followUser(followId) {
  console.log('API: Following user with ID:', followId);
  return request.post('/follow/doFollow', null, {
    params: {
      followId
    }
  }).then(response => {
    console.log('API: Follow user response:', response);
    return response;
  }).catch(error => {
    console.error('API: Follow user error:', error);
    throw error;
  });
}

/**
 * 取消关注用户
 * @param {number} followId - 被关注的用户ID
 * @returns {Promise}
 */
export function cancelFollowUser(followId) {
  console.log('API: Canceling follow for user with ID:', followId);
  return request.post('/follow/cancelFollow', null, {
    params: {
      followId
    }
  }).then(response => {
    console.log('API: Cancel follow response:', response);
    return response;
  }).catch(error => {
    console.error('API: Cancel follow error:', error);
    throw error;
  });
}

/**
 * 检查是否关注了某用户
 * @param {number} followId - 被关注的用户ID
 * @returns {Promise}
 */
export function isFollowedUser(followId) {
  console.log('API: Checking if following user with ID:', followId);
  return request.get('/follow/isFollowed', {
    params: {
      followId
    }
  }).then(response => {
    console.log('API: Is followed response:', response);
    return response;
  }).catch(error => {
    console.error('API: Is followed error:', error);
    throw error;
  });
}