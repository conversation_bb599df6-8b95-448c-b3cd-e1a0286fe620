<template>
  <div class="search-page-container">
    <!-- 赛博朋克风格背景元素 -->
    <div class="cyberpunk-background">
      <div class="cyberpunk-overlay"></div>
      <div class="cyberpunk-grid"></div>
      <div class="cyberpunk-scanline"></div>
      <div class="cyberpunk-glitch-effect"></div>
    </div>

    <div class="search-header">
      <div class="close-button" @click="goBack">
        <i class="el-icon-close"></i>
      </div>
    </div>

    <div class="search-content">
      <div class="search-title-container">
        <h1 class="search-title">检索你关心的内容</h1>
        <p class="search-subtitle">你在寻找全站的相关信息</p>

        <!-- 添加装饰性数据可视化元素 -->
        <div class="data-viz-container">
          <div class="data-viz-bars">
            <div class="data-bar" v-for="(value, index) in [75, 45, 90, 60, 30]" :key="index"
                :style="{ height: value + '%', animationDelay: index * 0.2 + 's' }">
              <div class="data-bar-highlight"></div>
            </div>
          </div>
        </div>
      </div>

      <div class="search-input-container">
        <el-input
          v-model="searchQuery"
          placeholder="输入关键词搜索..."
          class="search-input cyberpunk-input"
          :prefix-icon="Search"
          @keyup.enter="performSearch"
          ref="searchInputRef"
          clearable
        >
          <template #append>
            <el-button type="primary" @click="performSearch">
              <span class="button-text">搜索</span>
              <span class="button-glow"></span>
            </el-button>
          </template>
        </el-input>

        <!-- 添加全息数据元素 -->
        <div class="holo-data-element">
          <div class="holo-circle"></div>
          <div class="holo-text">DATA SEARCH</div>
        </div>
      </div>

      <div class="search-results" v-if="hasSearched">
        <div v-if="loading" class="loading-container">
          <el-skeleton :rows="5" animated />
        </div>

        <div v-else-if="userResults.length === 0 && postResults.length === 0" class="no-results">
          <i class="el-icon-warning-outline"></i>
          <p>没有找到与 "{{ searchQuery }}" 相关的内容</p>
          <p class="suggestion">请尝试其他关键词或浏览我们的热门内容</p>
        </div>

        <div v-else class="results-list">
          <h2 class="results-count">找到 {{ totalResults }} 条结果</h2>

          <!-- 用户搜索结果 -->
          <div v-if="userResults.length > 0" class="result-section">
            <h3 class="section-title">用户 ({{ userResults.length }})</h3>

            <div v-for="(user, index) in userResults" :key="'user-'+index" class="user-result-item">
              <div class="user-avatar">
                <el-avatar :src="user.image || 'https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png'" :size="50"></el-avatar>
              </div>
              <div class="user-info">
                <div class="user-name" @click="navigateToUserProfile(user)">{{ user.username }}</div>
                <div class="user-signature">{{ user.signature || '这个用户很懒，还没有设置个性签名' }}</div>
                <div class="user-meta">
                  <span class="user-role">{{ user.role === 1 ? '管理员' : '普通用户' }}</span>
                  <el-button type="primary" size="small" @click="sendMessage(user)" v-if="isLoggedIn">发送私信</el-button>
                </div>
              </div>
            </div>
          </div>

          <!-- 帖子搜索结果 -->
          <div v-if="postResults.length > 0" class="result-section">
            <h3 class="section-title">帖子 ({{ postResults.length }})</h3>

            <div v-for="(post, index) in postResults" :key="'post-'+index" class="result-item">
              <div class="result-category">{{ getCategoryName(post.category) }}</div>
              <div class="result-title" @click="navigateToPost(post)">{{ post.title }}</div>
              <div class="result-content">{{ post.content }}</div>
              <div class="result-meta">
                <span class="result-date">{{ formatDate(post.createdAt) }}</span>
                <span v-if="post.userBasicVO" class="result-author">作者: {{ post.userBasicVO.username }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, nextTick } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { Search } from '@element-plus/icons-vue';
import { ElMessage } from 'element-plus';
import { search } from '@/api/search';

const router = useRouter();
const route = useRoute();
const searchQuery = ref('');
const userResults = ref([]);
const postResults = ref([]);
const loading = ref(false);
const hasSearched = ref(false);
const searchInputRef = ref(null);
const totalResults = ref(0);

// Check if user is logged in
const isLoggedIn = computed(() => {
  return !!localStorage.getItem('token');
});

// Get query from URL if present
onMounted(() => {
  if (route.query.q) {
    searchQuery.value = route.query.q;
    performSearch();
  }

  // Focus the search input
  nextTick(() => {
    if (searchInputRef.value) {
      searchInputRef.value.focus();
    }
  });
});

// Navigate back
const goBack = () => {
  router.back();
};

// Perform search
const performSearch = async () => {
  if (!searchQuery.value.trim()) return;

  loading.value = true;
  hasSearched.value = true;

  try {
    // Update URL with search query
    router.replace({
      path: '/search',
      query: { q: searchQuery.value }
    });

    // Log authentication state for debugging
    console.log('Authentication state:', {
      token: localStorage.getItem('token'),
      userId: localStorage.getItem('userId'),
      isLoggedIn: isLoggedIn.value
    });

    // Call the real search API with increased result limit (50 instead of default 10)
    const response = await search(searchQuery.value, 0, 50);
    console.log('Search API response:', response);

    if (response.code === 200 && response.data) {
      // Extract user and post results from the API response
      userResults.value = response.data.userResults?.userList || [];
      postResults.value = response.data.postResults?.postVOList || [];

      // Set total results count
      totalResults.value = response.data.totalResults || (userResults.value.length + postResults.value.length);

      console.log('Processed search results:', {
        users: userResults.value.length,
        posts: postResults.value.length,
        total: totalResults.value
      });
    } else {
      console.error('Search API returned error:', response);
      ElMessage.error(response.message || '搜索失败，请稍后重试');
      userResults.value = [];
      postResults.value = [];
      totalResults.value = 0;
    }
  } catch (error) {
    console.error('Search failed:', error);
    console.error('Error details:', {
      message: error.message,
      response: error.response?.data,
      status: error.response?.status
    });
    ElMessage.error('搜索失败，请稍后重试');
    userResults.value = [];
    postResults.value = [];
    totalResults.value = 0;
  } finally {
    loading.value = false;
  }
};



// Get category display name
const getCategoryName = (category) => {
  const categories = {
    0: '新闻动态',
    1: '通知公告',
    2: '学术动态',
    3: '在线交流'
  };

  return categories[category] || '其他';
};

// Format date
const formatDate = (date) => {
  if (!date) return '';
  return new Date(date).toLocaleDateString('zh-CN');
};

// Navigate to user profile
const navigateToUserProfile = (user) => {
  // Navigate to the user's profile page
  router.push(`/user/${user.id}`);
};

// Navigate to post
const navigateToPost = (post) => {
  // Always navigate to the post detail page when clicking on a post in search results
  router.push(`/post/detail/${post.id}`);

  // Log the navigation for debugging
  console.log(`Navigating to post detail page for post ID: ${post.id}, category: ${post.category}`);
};

// Send message to user
const sendMessage = (user) => {
  if (!isLoggedIn.value) {
    ElMessage.warning('请先登录后再发送私信');
    return;
  }

  // In a real app, this would navigate to the message page with the user pre-selected
  router.push({
    path: '/messages',
    query: { userId: user.id }
  });
};


</script>

<style scoped>
.search-page-container {
  min-height: 100vh;
  background-color: #f8f9fa;
  color: #333333;
  padding: 20px;
  position: relative;
  overflow: hidden;
}

/* 赛博朋克风格背景元素 */
.cyberpunk-background {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
  overflow: hidden;
}

.cyberpunk-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.8) 0%, rgba(240, 240, 240, 0.6) 100%);
  z-index: 1;
  animation: overlayPulse 8s infinite alternate;
}

.cyberpunk-grid {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image:
    linear-gradient(rgba(138, 43, 226, 0.05) 1px, transparent 1px),
    linear-gradient(90deg, rgba(5, 217, 232, 0.05) 1px, transparent 1px);
  background-size: 40px 40px;
  z-index: 2;
  animation: gridPulse 15s infinite;
}

.cyberpunk-scanline {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    to bottom,
    transparent 50%,
    rgba(0, 0, 0, 0.02) 50%
  );
  background-size: 100% 4px;
  z-index: 3;
  pointer-events: none;
  opacity: 0.1;
}

.cyberpunk-glitch-effect {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: transparent;
  z-index: 4;
  pointer-events: none;
  overflow: hidden;
}

.cyberpunk-glitch-effect::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 42, 109, 0.1), transparent);
  animation: glitchSweep 10s infinite;
}

@keyframes overlayPulse {
  0% {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.8) 0%, rgba(240, 240, 240, 0.6) 100%);
  }
  100% {
    background: linear-gradient(135deg, rgba(248, 249, 250, 0.7) 0%, rgba(233, 236, 239, 0.5) 100%);
  }
}

@keyframes gridPulse {
  0% {
    opacity: 0.2;
    background-size: 40px 40px;
  }
  50% {
    opacity: 0.3;
    background-size: 42px 42px;
  }
  100% {
    opacity: 0.2;
    background-size: 40px 40px;
  }
}

@keyframes glitchSweep {
  0%, 100% {
    left: -100%;
    opacity: 0;
  }
  10%, 90% {
    left: -100%;
    opacity: 0;
  }
  50% {
    left: 100%;
    opacity: 1;
  }
}

.search-header {
  display: flex;
  justify-content: flex-end;
  padding: 20px 0;
  position: relative;
  z-index: 5;
}

.close-button {
  cursor: pointer;
  font-size: 24px;
  color: #666666;
  transition: color 0.3s;
}

.close-button:hover {
  color: #05d9e8;
  text-shadow: 0 0 8px rgba(5, 217, 232, 0.4);
}

.search-content {
  max-width: 800px;
  margin: 0 auto;
  padding: 40px 20px;
  position: relative;
  z-index: 5;
}

.search-title-container {
  position: relative;
  margin-bottom: 40px;
}

.search-title {
  font-size: 2.5rem;
  margin-bottom: 10px;
  text-align: center;
  text-shadow: 1px 1px 3px rgba(138, 43, 226, 0.3);
  letter-spacing: 2px;
  animation: textPulse 3s infinite alternate;
  color: #333333;
  font-weight: 600;
}

@keyframes textPulse {
  0% {
    text-shadow: 1px 1px 3px rgba(138, 43, 226, 0.3);
  }
  100% {
    text-shadow: 2px 2px 5px rgba(138, 43, 226, 0.4), 1px 1px 3px rgba(5, 217, 232, 0.3);
  }
}

.search-subtitle {
  font-size: 1.1rem;
  color: #666666;
  margin-bottom: 20px;
  text-align: center;
  letter-spacing: 1px;
  font-weight: 500;
}

/* 数据可视化元素 */
.data-viz-container {
  position: absolute;
  right: 0;
  top: 10px;
  width: 100px;
  height: 60px;
  opacity: 0.6;
  pointer-events: none;
}

.data-viz-bars {
  display: flex;
  align-items: flex-end;
  justify-content: space-between;
  height: 100%;
}

.data-bar {
  width: 12px;
  background-color: rgba(5, 217, 232, 0.2);
  position: relative;
  animation: barPulse 2s infinite alternate;
}

.data-bar-highlight {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 3px;
  background-color: #05d9e8;
  box-shadow: 0 0 6px rgba(5, 217, 232, 0.5);
}

@keyframes barPulse {
  0% {
    height: calc(var(--height) - 10%);
  }
  100% {
    height: var(--height);
  }
}

.search-input-container {
  margin-bottom: 40px;
  position: relative;
}

.search-input {
  width: 100%;
}

/* 全息数据元素 */
.holo-data-element {
  position: absolute;
  right: 10px;
  bottom: -30px;
  display: flex;
  align-items: center;
  gap: 10px;
}

.holo-circle {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  border: 2px solid rgba(5, 217, 232, 0.5);
  position: relative;
  animation: holoPulse 3s infinite alternate;
}

.holo-circle::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background-color: rgba(5, 217, 232, 0.3);
  box-shadow: 0 0 8px rgba(5, 217, 232, 0.5);
}

.holo-text {
  font-size: 12px;
  color: #05d9e8;
  letter-spacing: 1px;
  text-shadow: 1px 1px 3px rgba(5, 217, 232, 0.3);
  font-weight: 500;
}

@keyframes holoPulse {
  0% {
    box-shadow: 0 0 5px rgba(5, 217, 232, 0.3);
  }
  100% {
    box-shadow: 0 0 10px rgba(5, 217, 232, 0.5);
  }
}

.loading-container {
  padding: 20px;
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 8px;
  backdrop-filter: blur(5px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
}

.no-results {
  text-align: center;
  padding: 40px 0;
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 8px;
  backdrop-filter: blur(5px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(138, 43, 226, 0.1);
}

.no-results i {
  font-size: 48px;
  color: #05d9e8;
  margin-bottom: 20px;
  text-shadow: 1px 1px 3px rgba(5, 217, 232, 0.3);
}

.no-results p {
  font-size: 1.2rem;
  margin-bottom: 10px;
  color: #333333;
  font-weight: 500;
}

.no-results .suggestion {
  color: #666666;
  font-size: 0.9rem;
}

.results-count {
  margin-bottom: 20px;
  font-size: 1.2rem;
  color: #05d9e8;
  text-shadow: 1px 1px 3px rgba(5, 217, 232, 0.2);
  letter-spacing: 1px;
  font-weight: 500;
}

.result-section {
  margin-bottom: 40px;
}

.section-title {
  font-size: 1.5rem;
  margin-bottom: 20px;
  color: #333333;
  border-bottom: 1px solid rgba(138, 43, 226, 0.2);
  padding-bottom: 10px;
  text-shadow: 1px 1px 3px rgba(138, 43, 226, 0.2);
  position: relative;
  font-weight: 600;
}

.section-title::after {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 0;
  width: 100px;
  height: 2px;
  background: linear-gradient(90deg, #8a2be2, #05d9e8);
  animation: borderPulse 3s infinite alternate;
}

@keyframes borderPulse {
  0% {
    width: 100px;
    box-shadow: 0 0 5px rgba(138, 43, 226, 0.3);
  }
  100% {
    width: 150px;
    box-shadow: 0 0 8px rgba(5, 217, 232, 0.4);
  }
}

/* User result styles */
.user-result-item {
  display: flex;
  background-color: rgba(255, 255, 255, 0.95);
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  transition: transform 0.3s, box-shadow 0.3s;
  border-left: 3px solid #8a2be2;
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(5px);
}

.user-result-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(138, 43, 226, 0.02) 0%, rgba(5, 217, 232, 0.02) 100%);
  z-index: 0;
}

.user-result-item:hover {
  transform: translateY(-3px);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.1), 0 0 15px rgba(138, 43, 226, 0.15);
  border-left-color: #05d9e8;
}

.user-avatar {
  margin-right: 20px;
  position: relative;
  z-index: 1;
}

.user-info {
  flex: 1;
  position: relative;
  z-index: 1;
}

.user-name {
  font-size: 1.2rem;
  font-weight: bold;
  margin-bottom: 5px;
  cursor: pointer;
  transition: all 0.3s;
  color: #333333;
}

.user-name:hover {
  color: #05d9e8;
  text-shadow: 1px 1px 3px rgba(5, 217, 232, 0.3);
}

.user-signature {
  color: #666666;
  margin-bottom: 15px;
  line-height: 1.5;
}

.user-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.user-role {
  display: inline-block;
  background: linear-gradient(90deg, #8a2be2, #ff2a6d);
  color: #ffffff;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.8rem;
  box-shadow: 0 2px 6px rgba(138, 43, 226, 0.3);
  font-weight: 500;
}

/* Post result styles */
.result-item {
  background-color: rgba(255, 255, 255, 0.95);
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  transition: all 0.3s;
  border-left: 3px solid #05d9e8;
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(5px);
}

.result-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(5, 217, 232, 0.02) 0%, rgba(138, 43, 226, 0.02) 100%);
  z-index: 0;
}

.result-item:hover {
  transform: translateY(-3px);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.1), 0 0 15px rgba(5, 217, 232, 0.15);
  border-left-color: #8a2be2;
}

.result-category {
  display: inline-block;
  background: linear-gradient(90deg, #05d9e8, #8a2be2);
  color: #ffffff;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.8rem;
  margin-bottom: 10px;
  position: relative;
  z-index: 1;
  box-shadow: 0 2px 6px rgba(5, 217, 232, 0.3);
  font-weight: 500;
}

.result-title {
  font-size: 1.2rem;
  font-weight: bold;
  margin-bottom: 10px;
  cursor: pointer;
  transition: all 0.3s;
  position: relative;
  z-index: 1;
  color: #333333;
}

.result-title:hover {
  color: #05d9e8;
  text-shadow: 1px 1px 3px rgba(5, 217, 232, 0.3);
}

.result-content {
  color: #666666;
  margin-bottom: 15px;
  line-height: 1.5;
  position: relative;
  z-index: 1;
}

.result-meta {
  display: flex;
  justify-content: space-between;
  color: #666666;
  font-size: 0.8rem;
  position: relative;
  z-index: 1;
}

/* 按钮发光效果 */
.button-text {
  position: relative;
  z-index: 2;
}

.button-glow {
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  animation: buttonGlow 3s infinite;
}

@keyframes buttonGlow {
  0% {
    left: -100%;
  }
  50% {
    left: 100%;
  }
  100% {
    left: 100%;
  }
}

/* Element Plus overrides */
:deep(.cyberpunk-input .el-input__inner) {
  background-color: #ffffff !important;
  border-color: #e0e0e0 !important;
  color: #333333 !important;
  transition: all 0.3s;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
  height: 50px;
  line-height: 50px;
}

:deep(.cyberpunk-input .el-input__inner:focus) {
  border-color: #8a2be2 !important;
  box-shadow: 0 0 8px rgba(138, 43, 226, 0.3) !important;
}

:deep(.el-input-group__append) {
  background-color: #8a2be2 !important;
  border-color: #8a2be2 !important;
  color: #fff !important;
  position: relative;
  overflow: hidden;
}

:deep(.el-button) {
  background-color: #8a2be2;
  border-color: #8a2be2;
  color: #fff;
  position: relative;
  overflow: hidden;
  box-shadow: 0 2px 6px rgba(138, 43, 226, 0.3);
}

:deep(.el-button:hover) {
  background-color: #9e6ff0;
  border-color: #9e6ff0;
  box-shadow: 0 4px 12px rgba(138, 43, 226, 0.4);
  transform: translateY(-2px);
}

:deep(.el-input__prefix) {
  color: #05d9e8;
}
</style>
