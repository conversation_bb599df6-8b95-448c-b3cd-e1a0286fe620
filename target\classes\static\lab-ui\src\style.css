:root {
  font-family: Inter, system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;
  font-size: 18px;

  color-scheme: light;
  color: #333333;
  background-color: #f8f9fa;

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  -webkit-text-size-adjust: 100%;
}

a {
  font-weight: 500;
  color: #646cff;
  text-decoration: inherit;
  font-size: 19px;
}
a:hover {
  color: #535bf2;
}

body {
  margin: 0;
  padding: 0;
  width: 100%;
  min-height: 100vh;
  overflow-x: hidden;
  background-color: #f8f9fa;
}

h1 {
  font-size: 3.2em;
  line-height: 1.1;
}

button {
  border-radius: 8px;
  border: 1px solid transparent;
  padding: 0.6em 1.2em;
  font-size: 18px;
  font-weight: 500;
  font-family: inherit;
  background-color: #e9ecef;
  color: #333333;
  cursor: pointer;
  transition: border-color 0.25s;
}
button:hover {
  border-color: #646cff;
}
button:focus,
button:focus-visible {
  outline: 4px auto -webkit-focus-ring-color;
}

.card {
  padding: 2em;
}

#app {
  width: 100%;
  min-height: 100vh;
  margin: 0;
  padding: 0;
  text-align: left;
}

/* Force light theme for all users */
:root {
  color: #333333;
  background-color: #f8f9fa;
}

a:hover {
  color: #747bff;
}

button {
  background-color: #e9ecef;
  color: #333333;
}

/*覆盖Element Plus的默认样式*/
.el-button--primary {
  --el-button-bg-color: #03e9f4;
  --el-button-border-color: #03e9f4;
  --el-button-text-color: #ffffff;
  --el-button-hover-bg-color: #05d9e8;
  --el-button-hover-border-color: #05d9e8;
  --el-button-active-bg-color: #05d9e8;
  --el-button-active-border-color: #05d9e8;
  --el-button-hover-text-color: #ffffff;
}

.el-button--success {
  --el-button-bg-color: #67c23a;
  --el-button-border-color: #67c23a;
  --el-button-text-color: #ffffff;
}

.el-button--warning {
  --el-button-bg-color: #e6a23c;
  --el-button-border-color: #e6a23c;
  --el-button-text-color: #ffffff;
}

.el-button--danger {
  --el-button-bg-color: #f56c6c;
  --el-button-border-color: #f56c6c;
  --el-button-text-color: #ffffff;
}

.el-input.is-focus .el-input__inner {
  border-color: #03e9f4 !important;
}

.el-input.el-input--prefix .el-input__inner {
  padding-left: 30px;
}

/* 覆盖 Element Plus 对话框样式 */
.el-overlay {
  background-color: rgba(0, 0, 0, 0.2) !important;
}

.el-dialog {
  background-color: #f8f9fa !important;
  color: #333333 !important;
  border: 1px solid rgba(5, 217, 232, 0.1) !important;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1) !important;
}

.el-dialog__header {
  border-bottom: 1px solid rgba(5, 217, 232, 0.1) !important;
  background-color: rgba(248, 249, 250, 0.9) !important;
}

.el-dialog__title {
  color: #05d9e8 !important;
}

.el-dialog__body {
  color: #333333 !important;
}

.el-dialog__footer {
  border-top: 1px solid rgba(5, 217, 232, 0.1) !important;
}

/* 增大Element Plus组件字体大小 */
.el-button {
  font-size: 18px !important;
}

.el-input__inner {
  font-size: 18px !important;
}

.el-textarea__inner {
  font-size: 18px !important;
}

.el-form-item__label {
  font-size: 18px !important;
}

.el-form-item__content {
  font-size: 18px !important;
}

.el-dropdown-menu__item {
  font-size: 18px !important;
}

.el-menu-item {
  font-size: 18px !important;
}

.el-submenu__title {
  font-size: 18px !important;
}

.el-dialog__title {
  font-size: 20px !important;
}

.el-dialog__body {
  font-size: 18px !important;
}

.el-message {
  font-size: 18px !important;
}

.el-message-box__message {
  font-size: 18px !important;
}

.el-message-box__title {
  font-size: 20px !important;
}

.el-table {
  font-size: 18px !important;
}

.el-table th {
  font-size: 18px !important;
}

.el-pagination {
  font-size: 18px !important;
}

.el-tag {
  font-size: 16px !important;
}

.el-select-dropdown__item {
  font-size: 18px !important;
}

.el-radio__label {
  font-size: 18px !important;
}

.el-checkbox__label {
  font-size: 18px !important;
}

.el-tabs__item {
  font-size: 18px !important;
}
