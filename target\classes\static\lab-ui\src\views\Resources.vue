<template>
  <div class="resources-container">
    <!-- Cyberpunk background elements -->
    <div class="cyberpunk-overlay"></div>
    <div class="cyberpunk-grid"></div>
    <div class="cyberpunk-scanline"></div>
    <div class="cyberpunk-glitch-effect"></div>

    <div class="resources-header">
      <h1 class="glitch-text" data-text="资源中心">{{ currentLang === 'zh' ? '资源中心' : 'Resources Center' }}</h1>
      <div class="header-line"></div>
      <p>{{ currentLang === 'zh' ? '获取实验室提供的书籍、实验指南和其他学习资源' : 'Access books, experiment guides, and other learning resources provided by the laboratory' }}</p>
    </div>

    <div class="resource-categories">
      <div
        v-for="category in categories"
        :key="category.id"
        class="category-item"
        :class="{ active: selectedCategory === category.id }"
        @click="selectedCategory = category.id"
      >
        {{ currentLang === 'zh' ? category.name : category.nameEn }}
      </div>
    </div>

    <div class="resources-grid">
      <div v-for="resource in filteredResources" :key="resource.id" class="resource-card">
        <div class="resource-image">
          <img :src="resource.coverUrl || getDefaultCover(resource.type)" :alt="resource.title" />
          <div class="resource-type-badge">
            <i :class="getResourceTypeIcon(resource.type)"></i>
          </div>
        </div>
        <div class="resource-info">
          <h3>{{ resource.title }}</h3>
          <p class="resource-description">{{ resource.description }}</p>
          <div class="resource-meta">
            <span class="resource-type">{{ getResourceTypeName(resource.type) }}</span>
            <span class="resource-date">{{ formatDate(resource.createdAt) }}</span>
          </div>
          <div class="resource-actions">
            <el-button type="primary" size="small" @click="viewResourceDetail(resource)">
              {{ currentLang === 'zh' ? '查看详情' : 'View Details' }}
            </el-button>
            <el-button
              v-if="resource.downloadUrl"
              type="success"
              size="small"
              @click="downloadResource(resource)"
            >
              <el-icon><Download /></el-icon>
              {{ currentLang === 'zh' ? '下载' : 'Download' }}
            </el-button>
          </div>
        </div>
      </div>

      <div v-if="loading" class="loading-indicator">
        <i class="el-icon-loading"></i> {{ currentLang === 'zh' ? '加载中...' : 'Loading...' }}
      </div>

      <div v-if="!loading && filteredResources.length === 0" class="no-resources">
        <i class="el-icon-document"></i>
        <p>{{ currentLang === 'zh' ? '暂无资源' : 'No resources available' }}</p>
      </div>
    </div>

    <!-- Resource Detail Dialog -->
    <el-dialog
      v-model="showResourceDetail"
      :title="selectedResource ? selectedResource.title : ''"
      width="70%"
      class="resource-detail-dialog"
      destroy-on-close
    >
      <div class="resource-detail-content" v-if="selectedResource">
        <div class="resource-detail-header">
          <div class="resource-detail-type">{{ getResourceTypeName(selectedResource.type) }}</div>
          <div class="resource-detail-date">{{ formatDate(selectedResource.createdAt) }}</div>
        </div>

        <div class="resource-detail-content-text">
          <div v-if="selectedResource.content" v-html="selectedResource.content" class="content-area"></div>
          <div v-else class="no-content-message">
            {{ currentLang === 'zh' ? '暂无内容详情' : 'No content details available' }}
          </div>
        </div>

        <div class="resource-download" v-if="selectedResource.downloadUrl">
          <div class="download-actions">
            <div class="download-stats" v-if="selectedResource.downloadCount !== undefined">
              <el-icon><Download /></el-icon>
              <span>{{ selectedResource.downloadCount || 0 }} {{ currentLang === 'zh' ? '次下载' : 'downloads' }}</span>
            </div>
            <el-button type="primary" @click="downloadResource(selectedResource)">
              <el-icon><Download /></el-icon>
              {{ currentLang === 'zh' ? '下载资源' : 'Download Resource' }}
            </el-button>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue';
import { ElMessage } from 'element-plus';
import { useRouter } from 'vue-router';
import { Document, VideoPlay, Reading, Download } from '@element-plus/icons-vue';
import { getResources, getResourceDetail } from '../api/resource';
import request from '../utils/request';

const router = useRouter();
const currentLang = ref(localStorage.getItem('language') || 'zh');

// Resource categories
const categories = [
  { id: 0, name: '全部', nameEn: 'All' },
  { id: 1, name: '书籍', nameEn: 'Books' },
  { id: 2, name: '实验指南', nameEn: 'Experiment Guides' },
  { id: 3, name: '视频教程', nameEn: 'Video Tutorials' },
  { id: 4, name: '软件工具', nameEn: 'Software Tools' }
];

// State variables
const resources = ref([]);
const selectedCategory = ref(0);
const loading = ref(false);
const showResourceDetail = ref(false);
const selectedResource = ref(null);

// Filter resources by selected category
const filteredResources = computed(() => {
  if (selectedCategory.value === 0) {
    return resources.value;
  }
  return resources.value.filter(resource => resource.categoryId === selectedCategory.value);
});

// Get default cover image based on resource type
const getDefaultCover = (type) => {
  switch (type) {
    case 1: // Book
      return 'https://via.placeholder.com/300x400?text=Book';
    case 2: // Guide
      return 'https://via.placeholder.com/300x400?text=Guide';
    case 3: // Video
      return 'https://via.placeholder.com/300x400?text=Video';
    case 4: // Software
      return 'https://via.placeholder.com/300x400?text=Software';
    default:
      return 'https://via.placeholder.com/300x400?text=Resource';
  }
};

// Get icon for resource type
const getResourceTypeIcon = (type) => {
  switch (type) {
    case 1: // Book
      return 'el-icon-reading';
    case 2: // Guide
      return 'el-icon-document';
    case 3: // Video
      return 'el-icon-video-play';
    case 4: // Software
      return 'el-icon-download';
    default:
      return 'el-icon-document';
  }
};

// Get name for resource type
const getResourceTypeName = (type) => {
  if (currentLang.value === 'zh') {
    switch (type) {
      case 1: return '书籍';
      case 2: return '实验指南';
      case 3: return '视频教程';
      case 4: return '软件工具';
      default: return '资源';
    }
  } else {
    switch (type) {
      case 1: return 'Book';
      case 2: return 'Experiment Guide';
      case 3: return 'Video Tutorial';
      case 4: return 'Software Tool';
      default: return 'Resource';
    }
  }
};

// Format date
const formatDate = (dateString) => {
  if (!dateString) return '';
  const date = new Date(dateString);
  return date.toLocaleDateString();
};

// View resource detail with mock data
const viewResourceDetail = async (resource) => {
  try {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 300));

    // Add additional details to the resource for the detail view
    const detailedResource = { ...resource };

    // Add more detailed content based on resource type
    if (resource.type === 1) { // Book
      detailedResource.content = `<h3>本书包含以下章节：</h3>
        <ol>
          <li>人工智能概述</li>
          <li>机器学习基础</li>
          <li>深度学习原理</li>
          <li>神经网络架构</li>
          <li>实战案例分析</li>
          <li>未来发展趋势</li>
        </ol>
        <h3>适合人群</h3>
        <p>本科生、研究生、AI研究人员和工程师</p>
        <h3>出版信息</h3>
        <p>重庆大学出版社，2023年版</p>`;
    } else if (resource.type === 2) { // Guide
      detailedResource.content = `<h3>本指南提供详细的实验步骤和注意事项，包括：</h3>
        <ul>
          <li>实验环境搭建</li>
          <li>数据准备与预处理</li>
          <li>模型训练与评估</li>
          <li>结果分析与可视化</li>
          <li>常见问题排查</li>
        </ul>
        <h3>适用对象</h3>
        <p>实验室研究生和科研人员</p>
        <h3>更新日期</h3>
        <p>${new Date(resource.createdAt).toLocaleDateString('zh-CN')}</p>`;
      detailedResource.downloadUrl = '#';
    } else if (resource.type === 3) { // Video
      if (!detailedResource.content) {
        detailedResource.content = `<h3>视频内容包括：</h3>
          <ul>
            <li>基础理论讲解</li>
            <li>代码实现演示</li>
            <li>实际应用案例</li>
            <li>常见问题解答</li>
          </ul>
          <h3>视频信息</h3>
          <p>视频时长：约4小时</p>
          <p>讲师：李教授（人工智能研究中心）</p>`;
      }
    } else if (resource.type === 4) { // Software
      detailedResource.content = `<h3>工具特点：</h3>
        <ul>
          <li>易于安装和使用</li>
          <li>支持多种数据格式</li>
          <li>提供丰富的API接口</li>
          <li>可扩展的插件系统</li>
        </ul>
        <h3>系统要求</h3>
        <p>Python 3.8+, CUDA 11.0+</p>
        <h3>许可证</h3>
        <p>MIT</p>`;
      detailedResource.downloadUrl = '#';
    }

    selectedResource.value = detailedResource;
    showResourceDetail.value = true;
  } catch (error) {
    console.error('获取资源详情失败:', error);
    // Fallback to the basic resource info
    selectedResource.value = resource;
    showResourceDetail.value = true;
  }
};

// Download resource and increment download count
const downloadResource = async (resource) => {
  if (resource.downloadUrl && resource.downloadUrl !== '#') {
    try {
      // Call API to increment download count
      const response = await request.post(`/resource/download/${resource.id}`);

      if (response.code === 200) {
        // Update the download count in the UI if needed
        if (resource.downloadCount !== undefined) {
          resource.downloadCount = (resource.downloadCount || 0) + 1;
        }

        // Open download URL in new tab
        window.open(resource.downloadUrl, '_blank');

        // Show success message
        ElMessage.success(currentLang.value === 'zh' ? '开始下载资源' : 'Starting download');
      } else {
        throw new Error(response.message || '下载失败');
      }
    } catch (error) {
      console.error('下载资源失败:', error);
      ElMessage.error(currentLang.value === 'zh' ? '下载失败，请稍后重试' : 'Download failed, please try again later');
    }
  } else if (resource.downloadUrl === '#') {
    // For mock data with placeholder URLs
    ElMessage({
      message: currentLang.value === 'zh' ? '这是模拟数据，实际下载功能尚未实现' : 'This is mock data, actual download functionality is not implemented',
      type: 'info',
      duration: 3000
    });
  } else {
    ElMessage.warning(currentLang.value === 'zh' ? '资源暂不可下载' : 'Resource is not available for download');
  }
};

// Fetch resources from API
const fetchResources = async () => {
  loading.value = true;
  try {
    // Call the API to get resources
    const response = await getResources(selectedCategory.value === 0 ? null : selectedCategory.value);

    if (response.code === 200 && response.data) {
      // Map the resources to match our UI structure
      const resourceList = response.data.resourceList || [];

      resources.value = resourceList.map(resource => ({
        id: resource.id,
        title: resource.title,
        description: resource.description,
        content: resource.content,
        type: resource.type,
        categoryId: resource.type, // Use type as categoryId for filtering
        coverUrl: resource.coverUrl || getDefaultCover(resource.type),
        downloadUrl: resource.downloadUrl,
        downloadCount: resource.downloadCount,
        createdAt: resource.createdAt
      }));
    } else {
      throw new Error(response.message || '获取资源失败');
    }

  } catch (error) {
    console.error('获取资源失败:', error);
    ElMessage.error(currentLang.value === 'zh' ? '获取资源失败' : 'Failed to fetch resources');
  } finally {
    loading.value = false;
  }
};

// Fetch resources on mount
onMounted(() => {
  fetchResources();
});

// Watch for category changes
watch(selectedCategory, () => {
  fetchResources();
});
</script>

<style scoped>
.resources-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 40px 20px;
  position: relative;
  min-height: 100vh;
  overflow: hidden;
}

/* Cyberpunk background elements - positioned fixed to cover the whole screen */
.cyberpunk-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: linear-gradient(135deg, rgba(245, 245, 250, 0.7) 0%, rgba(235, 235, 245, 0.5) 100%);
  z-index: -2;
  animation: overlayPulse 8s infinite alternate;
}

.cyberpunk-grid {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background-image:
    linear-gradient(rgba(5, 217, 232, 0.1) 1px, transparent 1px),
    linear-gradient(90deg, rgba(5, 217, 232, 0.1) 1px, transparent 1px),
    linear-gradient(rgba(138, 43, 226, 0.05) 10px, transparent 10px),
    linear-gradient(90deg, rgba(138, 43, 226, 0.05) 10px, transparent 10px);
  background-size: 40px 40px, 40px 40px, 200px 200px, 200px 200px;
  z-index: -1;
  animation: gridPulse 15s infinite;
}

.cyberpunk-scanline {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: repeating-linear-gradient(
    to bottom,
    transparent 0%,
    rgba(5, 217, 232, 0.05) 0.5%,
    transparent 1%
  );
  z-index: 0;
  pointer-events: none;
  animation: scanlineDrift 10s linear infinite;
}

.cyberpunk-glitch-effect {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: transparent;
  z-index: 0;
  pointer-events: none;
  overflow: hidden;
}

.cyberpunk-glitch-effect::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(138, 43, 226, 0.3), transparent);
  animation: glitchSweep 8s infinite;
}

.cyberpunk-glitch-effect::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(5, 217, 232, 0.2), transparent);
  animation: glitchSweep 12s 2s infinite;
}

.resources-header {
  text-align: center;
  margin-bottom: 60px;
  position: relative;
  z-index: 1;
  padding: 20px;
  background-color: rgba(245, 245, 250, 0.7);
  border-radius: 10px;
  border: 1px solid rgba(5, 217, 232, 0.2);
  box-shadow: 0 0 30px rgba(5, 217, 232, 0.2);
  backdrop-filter: blur(5px);
  animation: headerGlow 8s infinite alternate;
}

.resources-header h1 {
  font-size: 3.5rem;
  color: #05d9e8;
  margin-bottom: 15px;
  text-shadow: 0 0 15px rgba(5, 217, 232, 0.8), 0 0 30px rgba(5, 217, 232, 0.4);
  position: relative;
  display: inline-block;
  letter-spacing: 4px;
  font-weight: 800;
}

.glitch-text {
  position: relative;
}

.glitch-text::before,
.glitch-text::after {
  content: attr(data-text);
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0.8;
}

.glitch-text::before {
  color: #ff2a6d;
  z-index: -1;
  animation: glitch-animation 3s infinite linear alternate-reverse;
}

.glitch-text::after {
  color: #8a2be2;
  z-index: -2;
  animation: glitch-animation 2s infinite linear alternate;
}

.header-line {
  width: 150px;
  height: 3px;
  background: linear-gradient(90deg, transparent, #05d9e8, transparent);
  margin: 0 auto 20px;
  position: relative;
}

.header-line::before {
  content: '';
  position: absolute;
  top: -3px;
  left: 0;
  width: 100%;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(138, 43, 226, 0.5), transparent);
}

.resources-header p {
  color: rgba(50, 50, 50, 0.7);
  font-size: 1.2rem;
  max-width: 800px;
  margin: 0 auto;
  letter-spacing: 0.5px;
}

.resource-categories {
  display: flex;
  justify-content: center;
  gap: 15px;
  margin-bottom: 40px;
  flex-wrap: wrap;
  position: relative;
  z-index: 1;
}

.category-item {
  padding: 10px 20px;
  background-color: rgba(235, 235, 245, 0.8);
  color: rgba(50, 50, 50, 0.7);
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s;
  border: 1px solid rgba(5, 217, 232, 0.1);
  position: relative;
  overflow: hidden;
}

.category-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(5, 217, 232, 0.1), transparent);
  transform: translateX(-100%);
  transition: transform 0.5s;
}

.category-item:hover {
  background-color: rgba(225, 225, 235, 0.8);
  color: #333333;
  border-color: rgba(5, 217, 232, 0.3);
  box-shadow: 0 0 10px rgba(5, 217, 232, 0.2);
}

.category-item:hover::before {
  transform: translateX(100%);
}

.category-item.active {
  background-color: rgba(5, 217, 232, 0.2);
  color: #05d9e8;
  border-color: rgba(5, 217, 232, 0.5);
  box-shadow: 0 0 15px rgba(5, 217, 232, 0.3);
}

.resources-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 30px;
  position: relative;
  z-index: 1;
}

.resource-card {
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 10px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1), 0 0 15px rgba(5, 217, 232, 0.1);
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  border: 1px solid rgba(5, 217, 232, 0.15);
  position: relative;
  backdrop-filter: blur(5px);
}

.resource-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background:
    linear-gradient(90deg, rgba(5, 217, 232, 0.05) 1px, transparent 1px),
    linear-gradient(rgba(5, 217, 232, 0.05) 1px, transparent 1px),
    linear-gradient(45deg, rgba(138, 43, 226, 0.02) 25%, transparent 25%, transparent 50%, rgba(138, 43, 226, 0.02) 50%, rgba(138, 43, 226, 0.02) 75%, transparent 75%, transparent);
  background-size: 20px 20px, 20px 20px, 40px 40px;
  z-index: 0;
  opacity: 0;
  transition: opacity 0.3s, background-size 0.5s;
}

.resource-card::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 5px;
  background: linear-gradient(90deg, #05d9e8, #8a2be2);
  transform: scaleX(0);
  transform-origin: left;
  transition: transform 0.5s;
}

.resource-card:hover {
  transform: translateY(-10px) scale(1.03);
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.5), 0 0 25px rgba(5, 217, 232, 0.3), 0 0 10px rgba(138, 43, 226, 0.2);
  border-color: rgba(5, 217, 232, 0.4);
}

.resource-card:hover::before {
  opacity: 1;
  background-size: 22px 22px, 22px 22px, 44px 44px;
}

.resource-card:hover::after {
  transform: scaleX(1);
}

.resource-image {
  position: relative;
  height: 200px;
  overflow: hidden;
}

.resource-image::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg,
    rgba(5, 217, 232, 0.2) 0%,
    rgba(138, 43, 226, 0.2) 50%,
    rgba(255, 42, 109, 0.2) 100%);
  z-index: 1;
  pointer-events: none;
  opacity: 0.7;
  mix-blend-mode: color-dodge;
  transition: opacity 0.5s;
}

.resource-image::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: repeating-linear-gradient(
    -45deg,
    rgba(5, 217, 232, 0.05) 0px,
    rgba(5, 217, 232, 0.05) 1px,
    transparent 1px,
    transparent 10px
  );
  z-index: 2;
  pointer-events: none;
  opacity: 0;
  transition: opacity 0.5s;
}

.resource-card:hover .resource-image::before {
  opacity: 1;
}

.resource-card:hover .resource-image::after {
  opacity: 0.9;
}

.resource-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.8s cubic-bezier(0.19, 1, 0.22, 1);
  filter: saturate(1.2) contrast(1.1);
}

.resource-card:hover .resource-image img {
  transform: scale(1.08) rotate(1deg);
}

.resource-type-badge {
  position: absolute;
  top: 10px;
  right: 10px;
  background-color: rgba(5, 217, 232, 0.3);
  color: #05d9e8;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 0.8rem;
  z-index: 3;
  border: 1px solid rgba(5, 217, 232, 0.4);
  box-shadow: 0 0 15px rgba(5, 217, 232, 0.3);
  backdrop-filter: blur(5px);
  transition: all 0.3s;
}

.resource-card:hover .resource-type-badge {
  background-color: rgba(5, 217, 232, 0.4);
  box-shadow: 0 0 20px rgba(5, 217, 232, 0.5);
  transform: translateY(-2px);
}

.resource-info {
  padding: 25px;
  position: relative;
  z-index: 1;
  background: linear-gradient(180deg, rgba(245, 245, 250, 0.9) 0%, rgba(235, 235, 245, 0.95) 100%);
  border-top: 1px solid rgba(5, 217, 232, 0.1);
}

.resource-info::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle at top right, rgba(5, 217, 232, 0.05), transparent 70%);
  z-index: -1;
  opacity: 0;
  transition: opacity 0.5s;
}

.resource-card:hover .resource-info::before {
  opacity: 1;
}

.resource-info h3 {
  color: #333333;
  margin-bottom: 12px;
  font-size: 1.3rem;
  transition: all 0.3s;
  position: relative;
  display: inline-block;
}

.resource-info h3::after {
  content: '';
  position: absolute;
  bottom: -5px;
  left: 0;
  width: 0;
  height: 2px;
  background: linear-gradient(90deg, #05d9e8, transparent);
  transition: width 0.5s ease;
}

.resource-card:hover .resource-info h3 {
  color: #05d9e8;
  text-shadow: 0 0 8px rgba(5, 217, 232, 0.5);
  transform: translateX(5px);
}

.resource-card:hover .resource-info h3::after {
  width: 100%;
}

.resource-description {
  color: rgba(50, 50, 50, 0.8);
  margin-bottom: 18px;
  font-size: 0.95rem;
  line-height: 1.7;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  transition: color 0.3s;
}

.resource-card:hover .resource-description {
  color: rgba(0, 0, 0, 0.9);
}

.resource-meta {
  display: flex;
  justify-content: space-between;
  margin-bottom: 18px;
  font-size: 0.85rem;
  padding: 8px 0;
  border-top: 1px dashed rgba(5, 217, 232, 0.2);
  border-bottom: 1px dashed rgba(5, 217, 232, 0.2);
}

.resource-type {
  color: #ff2a6d;
  font-weight: 600;
  letter-spacing: 0.5px;
  transition: all 0.3s;
}

.resource-card:hover .resource-type {
  color: #ff5a8d;
  text-shadow: 0 0 5px rgba(255, 42, 109, 0.5);
}

.resource-date {
  color: #666666;
  transition: all 0.3s;
}

.resource-card:hover .resource-date {
  color: #333333;
}

.resource-actions {
  display: flex;
  gap: 12px;
  margin-top: 15px;
  position: relative;
  z-index: 2;
}

.loading-indicator, .no-resources {
  grid-column: 1 / -1;
  text-align: center;
  padding: 40px;
  color: #666666;
}

.no-resources i {
  font-size: 3rem;
  margin-bottom: 10px;
}

/* Resource Detail Dialog */
:deep(.resource-detail-dialog .el-dialog__header) {
  background-color: rgba(245, 245, 250, 0.95);
  padding: 20px;
  border-bottom: 1px solid rgba(5, 217, 232, 0.2);
  position: relative;
}

:deep(.resource-detail-dialog .el-dialog__header)::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(5, 217, 232, 0.5), transparent);
}

:deep(.resource-detail-dialog .el-dialog) {
  background-color: rgba(255, 255, 255, 0.95);
  border-radius: 10px;
  overflow: hidden;
  box-shadow: 0 0 30px rgba(5, 217, 232, 0.2);
  border: 1px solid rgba(5, 217, 232, 0.2);
}

:deep(.resource-detail-dialog .el-dialog__title) {
  color: #05d9e8;
  font-size: 1.6rem;
  text-shadow: 0 0 10px rgba(5, 217, 232, 0.5);
}

:deep(.resource-detail-dialog .el-dialog__body) {
  background-color: rgba(255, 255, 255, 0.95);
  color: #333333;
  padding: 25px;
  background:
    linear-gradient(90deg, rgba(5, 217, 232, 0.02) 1px, transparent 1px),
    linear-gradient(rgba(5, 217, 232, 0.02) 1px, transparent 1px);
  background-size: 20px 20px;
}

.resource-detail-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 25px;
  padding-bottom: 15px;
  border-bottom: 1px solid rgba(5, 217, 232, 0.1);
  color: rgba(50, 50, 50, 0.7);
}

.resource-detail-type {
  color: #05d9e8;
  font-weight: bold;
  background-color: rgba(5, 217, 232, 0.1);
  padding: 5px 10px;
  border-radius: 4px;
  border: 1px solid rgba(5, 217, 232, 0.2);
}

.resource-detail-date {
  color: rgba(50, 50, 50, 0.6);
}

.resource-detail-content-text {
  margin-bottom: 30px;
  background-color: rgba(245, 245, 250, 0.5);
  padding: 25px;
  border-radius: 10px;
  border: 1px solid rgba(5, 217, 232, 0.2);
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
  position: relative;
  overflow: hidden;
}

.resource-detail-content-text::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background:
    linear-gradient(90deg, rgba(5, 217, 232, 0.02) 1px, transparent 1px),
    linear-gradient(rgba(5, 217, 232, 0.02) 1px, transparent 1px);
  background-size: 20px 20px;
  z-index: 0;
  pointer-events: none;
}

.content-area {
  color: rgba(50, 50, 50, 0.8);
  line-height: 1.8;
  position: relative;
  z-index: 1;
}

.content-area h3, .content-area h4 {
  color: #05d9e8;
  margin: 20px 0 15px;
  position: relative;
  display: inline-block;
  text-shadow: 0 0 8px rgba(5, 217, 232, 0.4);
}

.content-area h3:first-child {
  margin-top: 0;
}

.content-area p {
  margin-bottom: 15px;
}

.content-area ul, .content-area ol {
  margin-bottom: 15px;
  padding-left: 20px;
}

.content-area li {
  margin-bottom: 8px;
}

.resource-download {
  margin-top: 30px;
  text-align: center;
}

.download-actions {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  gap: 20px;
}

.download-stats {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  color: rgba(50, 50, 50, 0.6);
  background-color: rgba(5, 217, 232, 0.1);
  padding: 8px 15px;
  border-radius: 20px;
  display: inline-flex;
}

.no-content-message {
  text-align: center;
  padding: 30px;
  color: rgba(50, 50, 50, 0.5);
  font-style: italic;
}

/* Element Plus button overrides */
:deep(.el-button--primary) {
  background-color: rgba(5, 217, 232, 0.15);
  border-color: rgba(5, 217, 232, 0.5);
  color: #05d9e8;
  transition: all 0.3s;
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(5px);
  letter-spacing: 0.5px;
  font-weight: 600;
}

:deep(.el-button--primary::before) {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(5, 217, 232, 0.3), transparent);
  transition: left 0.5s;
  z-index: -1;
}

:deep(.el-button--primary:hover) {
  background-color: rgba(5, 217, 232, 0.25);
  border-color: rgba(5, 217, 232, 0.7);
  box-shadow: 0 0 20px rgba(5, 217, 232, 0.4);
  transform: translateY(-2px);
  text-shadow: 0 0 5px rgba(5, 217, 232, 0.5);
}

:deep(.el-button--primary:hover::before) {
  left: 100%;
}

:deep(.el-button--success) {
  background-color: rgba(138, 43, 226, 0.15);
  border-color: rgba(138, 43, 226, 0.5);
  color: #8a2be2;
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(5px);
  letter-spacing: 0.5px;
  font-weight: 600;
}

:deep(.el-button--success::before) {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(138, 43, 226, 0.3), transparent);
  transition: left 0.5s;
  z-index: -1;
}

:deep(.el-button--success:hover) {
  background-color: rgba(138, 43, 226, 0.25);
  border-color: rgba(138, 43, 226, 0.7);
  box-shadow: 0 0 20px rgba(138, 43, 226, 0.4);
  transform: translateY(-2px);
  text-shadow: 0 0 5px rgba(138, 43, 226, 0.5);
}

:deep(.el-button--success:hover::before) {
  left: 100%;
}

/* Animations */
@keyframes glitch-animation {
  0% {
    transform: translate(0);
  }
  20% {
    transform: translate(-2px, 2px);
  }
  40% {
    transform: translate(-2px, -2px);
  }
  60% {
    transform: translate(2px, 2px);
  }
  80% {
    transform: translate(2px, -2px);
  }
  100% {
    transform: translate(0);
  }
}

@keyframes overlayPulse {
  0% {
    background: linear-gradient(135deg, rgba(245, 245, 250, 0.7) 0%, rgba(235, 235, 245, 0.5) 100%);
  }
  100% {
    background: linear-gradient(135deg, rgba(245, 245, 250, 0.6) 0%, rgba(235, 235, 245, 0.4) 100%);
  }
}

@keyframes gridPulse {
  0% {
    opacity: 0.3;
    background-size: 40px 40px, 40px 40px, 200px 200px, 200px 200px;
  }
  50% {
    opacity: 0.5;
    background-size: 42px 42px, 42px 42px, 210px 210px, 210px 210px;
  }
  100% {
    opacity: 0.3;
    background-size: 40px 40px, 40px 40px, 200px 200px, 200px 200px;
  }
}

@keyframes scanlineDrift {
  0% {
    background-position: 0 0;
  }
  100% {
    background-position: 0 100vh;
  }
}

@keyframes glitchSweep {
  0%, 100% {
    left: -100%;
    opacity: 0;
  }
  10%, 90% {
    left: -100%;
    opacity: 0;
  }
  50% {
    left: 100%;
    opacity: 1;
  }
}

@keyframes headerGlow {
  0% {
    box-shadow: 0 0 20px rgba(5, 217, 232, 0.2);
    border-color: rgba(5, 217, 232, 0.2);
  }
  50% {
    box-shadow: 0 0 30px rgba(5, 217, 232, 0.4), 0 0 50px rgba(138, 43, 226, 0.2);
    border-color: rgba(5, 217, 232, 0.4);
  }
  100% {
    box-shadow: 0 0 20px rgba(5, 217, 232, 0.2);
    border-color: rgba(5, 217, 232, 0.2);
  }
}

/* Responsive styles */
@media (max-width: 768px) {
  .resources-header h1 {
    font-size: 2rem;
  }

  .resource-categories {
    flex-direction: column;
    align-items: center;
  }

  .category-item {
    width: 100%;
    max-width: 300px;
    text-align: center;
  }
}
</style>
