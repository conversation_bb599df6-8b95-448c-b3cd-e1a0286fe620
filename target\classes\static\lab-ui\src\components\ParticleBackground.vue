<template>
  <div class="particle-container">
    <div class="particle-overlay">
      <slot></slot>
    </div>
    <canvas ref="particleCanvas" class="particle-canvas"></canvas>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue';

const particleCanvas = ref(null);
let animationFrame = null;
let particles = [];
let ctx;

// Define resizeCanvas in the component scope so it can be referenced in onUnmounted
const resizeCanvas = () => {
  if (!particleCanvas.value) return;
  const canvas = particleCanvas.value;
  canvas.width = canvas.offsetWidth;
  canvas.height = canvas.offsetHeight;
  initParticles();
};

onMounted(() => {
  const canvas = particleCanvas.value;
  if (!canvas) return;

  ctx = canvas.getContext('2d');

  // Set canvas size to match container
  window.addEventListener('resize', resizeCanvas);
  resizeCanvas();

  // Start animation
  animate();
});

onUnmounted(() => {
  window.removeEventListener('resize', resizeCanvas);
  if (animationFrame) {
    cancelAnimationFrame(animationFrame);
    animationFrame = null;
  }
  // Clear particles to free memory
  particles = [];
});

function initParticles() {
  particles = [];
  // Reduce particle count significantly for better performance
  const particleCount = Math.floor(particleCanvas.value.width * particleCanvas.value.height / 30000);

  // Create gate-like structure (rectangular frame)
  createGateStructure();
}

function createGateStructure() {
  const canvas = particleCanvas.value;
  const width = canvas.width;
  const height = canvas.height;

  // Gate dimensions
  const gateWidth = width * 0.6;
  const gateHeight = height * 0.8;
  const gateX = (width - gateWidth) / 2;
  const gateY = (height - gateHeight) / 2;

  // Number of particles for the gate
  const particleDensity = 0.05; // Adjust for more/less particles
  const particlesPerSide = Math.floor(Math.max(gateWidth, gateHeight) * particleDensity);

  // Create particles for the gate frame
  // Top side
  for (let i = 0; i < particlesPerSide; i++) {
    const x = gateX + (i / particlesPerSide) * gateWidth;
    const y = gateY;
    particles.push(createParticle(x, y));
  }

  // Right side
  for (let i = 0; i < particlesPerSide; i++) {
    const x = gateX + gateWidth;
    const y = gateY + (i / particlesPerSide) * gateHeight;
    particles.push(createParticle(x, y));
  }

  // Bottom side
  for (let i = 0; i < particlesPerSide; i++) {
    const x = gateX + gateWidth - (i / particlesPerSide) * gateWidth;
    const y = gateY + gateHeight;
    particles.push(createParticle(x, y));
  }

  // Left side
  for (let i = 0; i < particlesPerSide; i++) {
    const x = gateX;
    const y = gateY + gateHeight - (i / particlesPerSide) * gateHeight;
    particles.push(createParticle(x, y));
  }

  // Add some random particles inside the gate
  const innerParticleCount = particlesPerSide * 2;
  for (let i = 0; i < innerParticleCount; i++) {
    const x = gateX + Math.random() * gateWidth;
    const y = gateY + Math.random() * gateHeight;
    particles.push(createParticle(x, y));
  }
}

function createParticle(x, y) {
  // Create a more diverse color palette matching the cyberpunk theme
  const colorOptions = [
    `rgba(255, 255, 255, ${Math.random() * 0.5 + 0.2})`, // White
    `rgba(5, 217, 232, ${Math.random() * 0.5 + 0.2})`,   // Cyan
    `rgba(255, 42, 109, ${Math.random() * 0.5 + 0.2})`,  // Pink
    `rgba(138, 43, 226, ${Math.random() * 0.5 + 0.2})`   // Purple
  ];

  const colorIndex = Math.floor(Math.random() * colorOptions.length);

  return {
    x,
    y,
    size: Math.random() * 2.5 + 0.8,
    speedX: (Math.random() - 0.5) * 0.3,
    speedY: (Math.random() - 0.5) * 0.3,
    color: colorOptions[colorIndex],
    // Add a glowing effect to some particles
    glow: Math.random() > 0.7,
    // Add a pulsing effect to some particles
    pulse: Math.random() > 0.8,
    pulseSpeed: Math.random() * 0.02 + 0.01,
    pulseSize: 0,
    baseSize: Math.random() * 2.5 + 0.8
  };
}

// Track frame count for optimization
let frameCount = 0;

function animate() {
  if (!particleCanvas.value || !ctx) return;

  frameCount++;
  ctx.clearRect(0, 0, particleCanvas.value.width, particleCanvas.value.height);

  // Update and draw particles
  particles.forEach(particle => {
    // Update position with small movement
    particle.x += particle.speedX;
    particle.y += particle.speedY;

    // Bounce off edges
    if (particle.x < 0 || particle.x > particleCanvas.value.width) {
      particle.speedX *= -1;
    }
    if (particle.y < 0 || particle.y > particleCanvas.value.height) {
      particle.speedY *= -1;
    }

    // Update pulsing effect - only on certain frames to improve performance
    if (particle.pulse && frameCount % 3 === 0) {
      particle.pulseSize = Math.sin(Date.now() * particle.pulseSpeed) * 1.5;
      particle.size = particle.baseSize + particle.pulseSize;
    }

    // Simplified drawing - removed expensive gradient operations
    ctx.fillStyle = particle.color;
    ctx.beginPath();
    ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2);
    ctx.fill();

    // Only draw connections every other frame to improve performance
    if (frameCount % 2 === 0) {
      connectParticles(particle);
    }
  });

  animationFrame = requestAnimationFrame(animate);
}

function connectParticles(particle) {
  // Reduce max distance for fewer connections
  const maxDistance = 80;

  // Only check a subset of particles for connections to improve performance
  // Use particle index to determine which particles to check
  const particleIndex = particles.indexOf(particle);
  if (particleIndex % 3 !== 0) return; // Only process every third particle

  for (let i = 0; i < particles.length; i += 2) { // Skip every other particle
    const otherParticle = particles[i];
    if (particle === otherParticle) continue;

    const dx = particle.x - otherParticle.x;
    const dy = particle.y - otherParticle.y;

    // Use squared distance to avoid expensive sqrt operation
    const distanceSquared = dx * dx + dy * dy;
    const maxDistanceSquared = maxDistance * maxDistance;

    if (distanceSquared < maxDistanceSquared) {
      // Draw line with opacity based on distance
      const opacity = 1 - (Math.sqrt(distanceSquared) / maxDistance);

      // Simplified color selection - just use white with opacity
      const color = `rgba(255, 255, 255, ${opacity * 0.2})`;

      // Remove shadow effects for better performance
      ctx.strokeStyle = color;
      ctx.lineWidth = 0.5;
      ctx.beginPath();
      ctx.moveTo(particle.x, particle.y);
      ctx.lineTo(otherParticle.x, otherParticle.y);
      ctx.stroke();
    }
  }
}
</script>

<style scoped>
.particle-container {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.particle-canvas {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

.particle-overlay {
  position: relative;
  width: 100%;
  height: 100%;
  z-index: 2;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
