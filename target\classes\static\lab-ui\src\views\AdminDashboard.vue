<template>
  <div class="admin-dashboard">
    <!-- 左侧导航栏 -->
    <div class="admin-sidebar">
      <div class="admin-logo">
        <img src="../assets/lab-logo.svg" alt="实验室标志" class="lab-logo" />
        <span>管理后台</span>
      </div>

      <div class="menu-items">
        <div
          v-for="(item, index) in menuItems"
          :key="index"
          :class="['menu-item', { active: currentSection === item.key }]"
          @click="currentSection = item.key"
        >
          <el-icon><component :is="item.icon" /></el-icon>
          <span>{{ item.label }}</span>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="admin-content">
      <!-- 顶部信息栏 -->
      <div class="admin-header">
        <div class="header-title">{{ getCurrentSectionTitle }}</div>
        <div class="header-actions">
          <el-button type="primary" @click="handleAction">
            {{ getActionButtonText }}
          </el-button>
        </div>
      </div>

      <!-- 内容区域 -->
      <div class="content-area">
        <!-- 新闻管理 -->
        <div v-if="currentSection === 'news'" class="section-content">
          <div class="filter-row">
            <el-select v-model="currentCategory" placeholder="选择分类" @change="loadNewsData">
              <el-option label="全部" :value="null" />
              <el-option label="新闻动态" :value="0" />
              <el-option label="通知公告" :value="1" />
              <el-option label="学术动态" :value="2" />
              <el-option label="生活" :value="3" />
            </el-select>
            <el-button type="primary" @click="refreshNewsData">刷新</el-button>
          </div>

          <el-table :data="newsData" style="width: 100%" v-loading="loading">
            <el-table-column prop="date" label="发布日期" width="180">
              <template #header>
                <span class="column-header">发布日期</span>
              </template>
            </el-table-column>
            <el-table-column prop="title" label="标题">
              <template #header>
                <span class="column-header">标题</span>
              </template>
              <template #default="scope">
                <div class="title-cell">
                  <span class="clickable-title news-title" @click="viewPostDetail(scope.row)">{{ scope.row.title }}</span>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="categoryName" label="分类" width="120">
              <template #header>
                <span class="column-header">分类</span>
              </template>
            </el-table-column>
            <el-table-column label="可见性" width="120">
              <template #header>
                <span class="column-header">可见性</span>
              </template>
              <template #default="scope">
                <div class="title-cell">
                  <el-tag :type="scope.row.visibility === 0 ? 'success' : 'danger'">
                    {{ scope.row.visibility === 0 ? '可见' : '不可见' }}
                  </el-tag>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="220">
              <template #header>
                <span class="column-header">操作</span>
              </template>
              <template #default="scope">
                <div class="operation-buttons news-operations">
                  <el-button size="small" @click="viewPostDetail(scope.row)">查看</el-button>
                  <el-button
                    size="small"
                    :type="scope.row.visibility === 0 ? 'warning' : 'success'"
                    @click="togglePostVisibility(scope.row)"
                  >
                    {{ scope.row.visibility === 0 ? '隐藏' : '显示' }}
                  </el-button>
                  <el-button size="small" type="danger" @click="deleteNews(scope.row)">删除</el-button>
                </div>
              </template>
            </el-table-column>
          </el-table>

          <div class="pagination-container" v-if="newsData.length > 0">
            <el-button @click="loadMoreNews" :disabled="!hasMoreNews || loading" v-if="!showNoMoreMessage">
              加载更多
            </el-button>
            <div v-if="showNoMoreMessage" class="no-more-data">
              没有更多数据了
            </div>
          </div>
        </div>

        <!-- 用户管理 -->
        <div v-if="currentSection === 'users'" class="section-content">
          <div class="filter-row">
            <el-button type="primary" @click="refreshUserData">刷新</el-button>
          </div>

          <el-table :data="userData" style="width: 100%" v-loading="userLoading">
            <el-table-column prop="username" label="用户名">
              <template #header>
                <span class="column-header">用户名</span>
              </template>
            </el-table-column>
            <el-table-column prop="phone" label="手机号" width="120">
              <template #header>
                <span class="column-header">手机号</span>
              </template>
            </el-table-column>
            <el-table-column prop="role" label="角色" width="120">
              <template #header>
                <span class="column-header">角色</span>
              </template>
              <template #default="scope">
                <div class="title-cell">
                  <span class="table-cell-content">{{ scope.row.role === 1 ? '管理员' : '普通用户' }}</span>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="status" label="状态" width="120">
              <template #header>
                <span class="column-header">状态</span>
              </template>
              <template #default="scope">
                <div class="title-cell">
                  <el-tag :type="scope.row.status === 0 ? 'success' : 'danger'">
                    {{ scope.row.status === 0 ? '正常' : '禁用' }}
                  </el-tag>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="220">
              <template #header>
                <span class="column-header">操作</span>
              </template>
              <template #default="scope">
                <div class="operation-buttons user-operations">
                  <el-button
                    size="small"
                    :type="scope.row.role === 1 ? 'warning' : 'success'"
                    @click="toggleUserRole(scope.row)"
                  >
                    {{ scope.row.role === 1 ? '取消管理员' : '设为管理员' }}
                  </el-button>
                  <el-button
                    size="small"
                    :type="scope.row.status === 0 ? 'danger' : 'success'"
                    @click="toggleUserStatus(scope.row)"
                  >
                    {{ scope.row.status === 0 ? '禁用' : '启用' }}
                  </el-button>
                </div>
              </template>
            </el-table-column>
          </el-table>

          <div class="pagination-container" v-if="userData.length > 0">
            <el-button @click="loadMoreUsers" :disabled="!hasMoreUsers || userLoading" v-if="!showNoMoreUsersMessage">
              加载更多
            </el-button>
            <div v-if="showNoMoreUsersMessage" class="no-more-data">
              没有更多数据了
            </div>
          </div>
        </div>





        <!-- 评论管理 -->
        <div v-if="currentSection === 'comments'" class="section-content">
          <div class="filter-row">
            <el-button type="primary" @click="refreshCommentData">刷新</el-button>
          </div>

          <el-table :data="commentData" style="width: 100%" v-loading="commentLoading">
            <el-table-column prop="date" label="评论日期" width="120">
              <template #header>
                <span class="column-header">评论日期</span>
              </template>
            </el-table-column>
            <el-table-column prop="content" label="评论内容">
              <template #header>
                <span class="column-header">评论内容</span>
              </template>
              <template #default="scope">
                <div class="title-cell">
                  <span class="table-cell-content">{{ scope.row.content }}</span>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="username" label="评论者" width="120">
              <template #header>
                <span class="column-header">评论者</span>
              </template>
            </el-table-column>
            <el-table-column prop="postTitle" label="所属帖子" width="180">
              <template #header>
                <span class="column-header">所属帖子</span>
              </template>
              <template #default="scope">
                <div class="title-cell">
                  <span class="clickable-title">{{ scope.row.postTitle }}</span>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="180">
              <template #header>
                <span class="column-header">操作</span>
              </template>
              <template #default="scope">
                <div class="operation-buttons comment-operations">
                  <el-button size="small" @click="viewCommentPost(scope.row)">查看帖子</el-button>
                  <el-button size="small" type="danger" @click="deleteComment(scope.row)">删除</el-button>
                </div>
              </template>
            </el-table-column>
          </el-table>

          <div class="pagination-container" v-if="commentData.length > 0">
            <el-button @click="loadMoreComments" :disabled="!hasMoreComments || commentLoading" v-if="!showNoMoreCommentsMessage">
              加载更多
            </el-button>
            <div v-if="showNoMoreCommentsMessage" class="no-more-data">
              没有更多数据了
            </div>
          </div>
        </div>

        <!-- 资源管理 -->
        <div v-if="currentSection === 'resources'" class="section-content">
          <div class="filter-row">
            <el-select v-model="resourceTypeFilter" placeholder="选择资源类型" @change="loadResourceData">
              <el-option label="全部" :value="null" />
              <el-option label="书籍" :value="1" />
              <el-option label="实验指南" :value="2" />
              <el-option label="视频教程" :value="3" />
              <el-option label="软件工具" :value="4" />
            </el-select>
            <el-button type="primary" @click="refreshResourceData">刷新</el-button>
          </div>

          <el-table :data="resourceData" style="width: 100%" v-loading="resourceLoading">
            <el-table-column prop="date" label="上传日期" width="120">
              <template #header>
                <span class="column-header">上传日期</span>
              </template>
            </el-table-column>
            <el-table-column prop="title" label="标题">
              <template #header>
                <span class="column-header">标题</span>
              </template>
              <template #default="scope">
                <div class="title-cell">
                  <span class="clickable-title resource-title">{{ scope.row.title }}</span>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="typeName" label="类型" width="100">
              <template #header>
                <span class="column-header">类型</span>
              </template>
            </el-table-column>
            <el-table-column prop="downloadCount" label="下载次数" width="100">
              <template #header>
                <span class="column-header">下载次数</span>
              </template>
              <template #default="scope">
                <div class="title-cell">
                  <div class="download-count">
                    <el-icon><Download /></el-icon>
                    <span>{{ scope.row.downloadCount }}</span>
                  </div>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="160">
              <template #header>
                <span class="column-header">操作</span>
              </template>
              <template #default="scope">
                <div class="operation-buttons resource-operations">
                  <el-button size="small" @click="editResource(scope.row)">编辑</el-button>
                  <el-button size="small" type="danger" @click="removeResource(scope.row)">删除</el-button>
                </div>
              </template>
            </el-table-column>
          </el-table>

          <div class="pagination-container" v-if="resourceData.length > 0">
            <el-button @click="loadMoreResources" :disabled="!hasMoreResources || resourceLoading" v-if="!showNoMoreResourcesMessage">
              加载更多
            </el-button>
            <div v-if="showNoMoreResourcesMessage" class="no-more-data">
              没有更多数据了
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- 上传媒体对话框 -->
  <el-dialog
    v-model="uploadDialogVisible"
    title="上传媒体文件"
    width="600px"
    :close-on-click-modal="false"
    :before-close="handleCloseUploadDialog"
  >
    <div class="upload-dialog-content">
      <div class="upload-type-selector">
        <el-radio-group v-model="uploadType">
          <el-radio :label="0">图片</el-radio>
          <el-radio :label="1">视频</el-radio>
        </el-radio-group>
      </div>

      <div class="upload-area">
        <el-upload
          ref="uploadRef"
          :auto-upload="false"
          :limit="uploadType === 0 ? 9 : 1"
          :multiple="uploadType === 0"
          :file-list="fileList"
          :on-change="handleFileChange"
          :on-remove="handleFileRemove"
          list-type="picture-card"
          :class="{ 'hide-upload-button': fileList.length >= (uploadType === 0 ? 9 : 1) }"
        >
          <el-icon><Plus /></el-icon>
          <template #tip>
            <div class="el-upload__tip">
              {{ uploadType === 0 ? '最多上传9张图片' : '上传一个视频文件' }}
            </div>
          </template>
        </el-upload>
      </div>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleCloseUploadDialog">取消</el-button>
        <el-button type="primary" @click="handleUploadFiles" :loading="uploadLoading">
          上传并发布
        </el-button>
      </span>
    </template>
  </el-dialog>

  <!-- 资源上传对话框 -->
  <el-dialog
    v-model="resourceUploadDialogVisible"
    title="上传资源"
    width="600px"
    :close-on-click-modal="false"
  >
    <div class="resource-upload-form">
      <el-form :model="resourceForm" label-width="100px">
        <el-form-item label="资源类型">
          <el-select v-model="resourceForm.type" placeholder="选择资源类型">
            <el-option label="书籍" :value="1" />
            <el-option label="实验指南" :value="2" />
            <el-option label="视频教程" :value="3" />
            <el-option label="软件工具" :value="4" />
          </el-select>
        </el-form-item>

        <el-form-item label="资源标题" required>
          <el-input v-model="resourceForm.title" placeholder="请输入资源标题" />
        </el-form-item>

        <el-form-item label="资源描述" required>
          <el-input v-model="resourceForm.description" type="textarea" :rows="3" placeholder="请输入资源描述" />
        </el-form-item>

        <el-form-item label="详细内容">
          <el-input v-model="resourceForm.content" type="textarea" :rows="5" placeholder="请输入详细内容（可选）" />
        </el-form-item>

        <el-form-item label="封面图片">
          <el-upload
            class="resource-cover-uploader"
            action="#"
            :auto-upload="false"
            :limit="1"
            :on-change="handleResourceCoverChange"
            list-type="picture-card"
          >
            <el-icon><Plus /></el-icon>
            <template #tip>
              <div class="el-upload__tip">
                上传资源封面图片（可选）
              </div>
            </template>
          </el-upload>
        </el-form-item>

        <el-form-item label="资源文件" required>
          <el-upload
            class="resource-file-uploader"
            action="#"
            :auto-upload="false"
            :limit="1"
            :on-change="handleResourceFileChange"
          >
            <el-button type="primary">选择文件</el-button>
            <template #tip>
              <div class="el-upload__tip">
                上传资源文件（PDF、ZIP等格式）
              </div>
            </template>
          </el-upload>
        </el-form-item>
      </el-form>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="resourceUploadDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleResourceUpload" :loading="resourceUploadLoading">
          上传资源
        </el-button>
      </span>
    </template>
  </el-dialog>

  <!-- 资源编辑对话框 -->
  <el-dialog
    v-model="resourceEditDialogVisible"
    title="编辑资源"
    width="600px"
    :close-on-click-modal="false"
  >
    <div class="resource-edit-form" v-if="editingResource">
      <el-form :model="editingResource" label-width="100px">
        <el-form-item label="资源类型">
          <el-select v-model="editingResource.type" placeholder="选择资源类型">
            <el-option label="书籍" :value="1" />
            <el-option label="实验指南" :value="2" />
            <el-option label="视频教程" :value="3" />
            <el-option label="软件工具" :value="4" />
          </el-select>
        </el-form-item>

        <el-form-item label="资源标题" required>
          <el-input v-model="editingResource.title" placeholder="请输入资源标题" />
        </el-form-item>

        <el-form-item label="资源描述" required>
          <el-input v-model="editingResource.description" type="textarea" :rows="3" placeholder="请输入资源描述" />
        </el-form-item>

        <el-form-item label="详细内容">
          <el-input v-model="editingResource.content" type="textarea" :rows="5" placeholder="请输入详细内容（可选）" />
        </el-form-item>

        <el-form-item label="下载次数">
          <div class="download-count-display">
            <el-icon><Download /></el-icon>
            <span>{{ editingResource.downloadCount }}</span>
          </div>
        </el-form-item>
      </el-form>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="resourceEditDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="saveResourceEdit">
          保存更改
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import {
  Document,
  User,
  Setting,
  Collection,
  PieChart,
  Plus,
  Delete,
  Upload,
  Files,
  Download,
  ChatDotRound
} from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox, ElLoading, ElDialog } from 'element-plus'
import { getPosts, createPost } from '../api/post'
import { uploadImage, uploadVideo ,uploadResource} from '../api/file'
import { getResources, updateResource, deleteResource } from '../api/resource'
import request from '../utils/request'

// 菜单项配置
const menuItems = [
  { key: 'news', label: '新闻管理', icon: 'Document' },
  { key: 'users', label: '用户管理', icon: 'User' },
  { key: 'comments', label: '评论管理', icon: 'ChatDotRound' },
  { key: 'resources', label: '资源管理', icon: 'Files' },
  // { key: 'settings', label: '系统设置', icon: 'Setting' },
  // { key: 'statistics', label: '数据统计', icon: 'PieChart' }
]

const currentSection = ref('news')
const contentTab = ref('research')
const loading = ref(false)
const currentCategory = ref(null)
const lastPostId = ref(0)
const hasMoreNews = ref(true)
const showNoMoreMessage = ref(false) // 是否显示没有更多数据的提示

// 文件上传相关
const uploadDialogVisible = ref(false)
const uploadType = ref(0) // 0-图片 1-视频
const fileList = ref([])
const uploadLoading = ref(false)
const tempNewsData = ref({
  title: '',
  content: '',
  category: 0
})
const mediaUrls = ref([]) // 存储上传后的媒体URL

// 实际数据
const newsData = ref([])

// 用户管理相关
const userData = ref([])
const userLoading = ref(false)
const lastUserId = ref(0)
const hasMoreUsers = ref(true)
const showNoMoreUsersMessage = ref(false)

// 帖子管理相关
const postData = ref([])
const postLoading = ref(false)
const postCategoryFilter = ref(null)
const lastPostIdForPosts = ref(0)
const hasMorePosts = ref(true)
const showNoMorePostsMessage = ref(false)

// 评论管理相关
const commentData = ref([])
const commentLoading = ref(false)
const lastCommentId = ref(0)
const hasMoreComments = ref(true)
const showNoMoreCommentsMessage = ref(false)

// 资源管理相关
const resourceData = ref([])
const resourceLoading = ref(false)
const resourceTypeFilter = ref(null)
const lastResourceId = ref(0)
const hasMoreResources = ref(true)
const showNoMoreResourcesMessage = ref(false)

// 资源上传对话框
const resourceUploadDialogVisible = ref(false)
const resourceForm = ref({
  title: '',
  description: '',
  content: '',
  type: 1 // 默认为书籍
})
const resourceCoverFile = ref(null)
const resourceFile = ref(null)
const resourceUploadLoading = ref(false)

// 资源编辑对话框
const resourceEditDialogVisible = ref(false)
const editingResource = ref(null)

// 计算属性
const getCurrentSectionTitle = computed(() => {
  const item = menuItems.find(item => item.key === currentSection.value)
  return item ? item.label : ''
})

const getActionButtonText = computed(() => {
  switch (currentSection.value) {
    case 'news':
      return '发布新闻'
    case 'users':
      return '添加用户'
    case 'comments':
      return '刷新评论'
    case 'resources':
      return '上传资源'
    default:
      return '操作'
  }
})

// 格式化日期
const formatDate = (dateStr) => {
  if (!dateStr) return '';
  const date = new Date(dateStr);
  return date.toLocaleDateString('zh-CN').replace(/\//g, '-');
};

// 获取分类名称
const getCategoryName = (category) => {
  switch (category) {
    case 0:
      return '新闻动态';
    case 1:
      return '通知公告';
    case 2:
      return '学术动态';
    case 3:
      return '生活';
    default:
      return '未分类';
  }
};

// 获取资源类型名称
const getResourceTypeName = (type) => {
  switch (type) {
    case 1:
      return '书籍';
    case 2:
      return '实验指南';
    case 3:
      return '视频教程';
    case 4:
      return '软件工具';
    default:
      return '未分类';
  }
};

// 加载新闻数据
const loadNewsData = async (reset = true) => {
  try {
    loading.value = true;

    if (reset) {
      lastPostId.value = 0;
      newsData.value = [];
      hasMoreNews.value = true;
      showNoMoreMessage.value = false; // 重置没有更多数据的提示
    }

    // 使用管理员API获取所有帖子，包括不可见的
    const response = await request.get('/admin/posts', {
      params: {
        category: currentCategory.value,
        lastPostId: lastPostId.value
      }
    });

    if (response.code === 200 && response.data) {
      const posts = response.data.postVOList || [];

      if (posts.length === 0) {
        hasMoreNews.value = false;
      } else {
        // 转换帖子数据格式
        const formattedPosts = posts.map(post => ({
          id: post.id,
          date: formatDate(post.createdAt),
          title: post.title, // 使用内容的第一行作为标题
          content: post.content,
          category: post.category,
          categoryName: getCategoryName(post.category),
          coverUrl: post.coverUrl,
          visibility: post.visibility // 添加可见性字段
        }));

        newsData.value = [...newsData.value, ...formattedPosts];
        lastPostId.value = response.data.lastPostId;

        // 如果lastPostId为1，标记没有更多数据
        if (lastPostId.value === 1) {
          hasMoreNews.value = false;
          showNoMoreMessage.value = true;
        }
      }
    } else {
      ElMessage.error('获取新闻列表失败');
    }
  } catch (error) {
    console.error('Failed to load news data:', error);
    ElMessage.error('加载新闻数据失败');
  } finally {
    loading.value = false;
  }
};

// 刷新新闻数据
const refreshNewsData = () => {
  loadNewsData(true);
};

// 加载更多新闻
const loadMoreNews = () => {
  // 如果lastPostId已经是1，显示没有更多数据的提示
  if (lastPostId.value === 1) {
    showNoMoreMessage.value = true;
    return;
  }

  // 如果没有更多新闻或正在加载，直接返回
  if (!hasMoreNews.value || loading.value) return;

  // 加载更多数据
  loadNewsData(false);
};

// 方法
const handleAction = () => {
  switch (currentSection.value) {
    case 'news':
      // 打开新闻发布对话框
      showCreateNewsDialog();
      break;
    case 'users':
      // 实现用户添加逻辑
      break;
    case 'comments':
      // 刷新评论数据
      loadCommentData(true);
      break;
    case 'resources':
      // 打开资源上传对话框
      showResourceUploadDialog();
      break;
  }
};

// 显示创建新闻对话框
const showCreateNewsDialog = () => {
  ElMessageBox.prompt('请输入新闻标题', '发布新闻', {
    confirmButtonText: '下一步',
    cancelButtonText: '取消',
    inputPlaceholder: '请输入新闻标题'
  }).then(({ value }) => {
    if (value) {
      tempNewsData.value.title = value;
      showCreateNewsContentDialog();
    }
  }).catch(() => {
    // 用户取消操作
  });
};

// 显示创建新闻内容对话框
const showCreateNewsContentDialog = () => {
  ElMessageBox.prompt('请输入新闻内容', '发布新闻', {
    confirmButtonText: '下一步',
    cancelButtonText: '取消',
    inputType: 'textarea',
    inputPlaceholder: '请输入新闻内容'
  }).then(({ value }) => {
    if (value) {
      tempNewsData.value.content = value;
      showCreateNewsCategoryDialog();
    }
  }).catch(() => {
    // 用户取消操作
  });
};

// 显示创建新闻分类对话框
const showCreateNewsCategoryDialog = () => {
  ElMessageBox.confirm(
    '请选择新闻分类',
    '发布新闻',
    {
      confirmButtonText: '下一步',
      cancelButtonText: '取消',
      distinguishCancelAndClose: true,
      showCancelButton: true,
      showInput: true,
      inputType: 'select',
      inputPlaceholder: '请选择分类',
      inputValue: 0,
      inputOptions: [
        { label: '新闻动态', value: 0 },
        { label: '通知公告', value: 1 },
        { label: '学术动态', value: 2 },
        { label: '生活', value: 3 }
      ]
    }
  ).then(({ value }) => {
    tempNewsData.value.category = value;
    // 打开上传媒体对话框
    showUploadMediaDialog();
  }).catch(() => {
    // 用户取消操作
  });
};

// 显示上传媒体对话框
const showUploadMediaDialog = () => {
  // 重置上传相关数据
  fileList.value = [];
  mediaUrls.value = [];
  uploadType.value = 0;
  uploadDialogVisible.value = true;
};

// 处理关闭上传对话框
const handleCloseUploadDialog = () => {
  ElMessageBox.confirm('关闭将取消本次发布，是否确认关闭？', '提示', {
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    uploadDialogVisible.value = false;
    fileList.value = [];
    mediaUrls.value = [];
  }).catch(() => {
    // 用户取消关闭
  });
};

// 处理文件变化
const handleFileChange = (file, fileListNew) => {
  // 如果是视频类型，只保留最新上传的一个文件
  if (uploadType.value === 1 && fileListNew.length > 1) {
    fileList.value = [fileListNew[fileListNew.length - 1]];
  } else {
    fileList.value = fileListNew;
  }
};

// 处理文件移除
const handleFileRemove = (file, fileListNew) => {
  fileList.value = fileListNew;
};

// 处理文件上传
const handleUploadFiles = async () => {
  if (fileList.value.length === 0) {
    ElMessageBox.confirm('您没有选择任何媒体文件，是否直接发布？', '提示', {
      confirmButtonText: '直接发布',
      cancelButtonText: '取消',
      type: 'warning'
    }).then(() => {
      // 直接发布新闻，不包含媒体文件
      createNewsPost();
    }).catch(() => {
      // 用户取消发布
    });
    return;
  }

  try {
    uploadLoading.value = true;
    mediaUrls.value = [];

    // 根据上传类型处理文件
    if (uploadType.value === 0) {
      // 图片上传
      for (const fileItem of fileList.value) {
        const response = await uploadImage(fileItem.raw);
        if (response.code === 200 && response.data && response.data.url) {
          mediaUrls.value.push(response.data.url);
        } else {
          throw new Error(`上传图片失败: ${response.message || '未知错误'}`);
        }
      }
    } else {
      // 视频上传
      if (fileList.value.length > 0) {
        const videoFile = fileList.value[0].raw;
        const coverUrl = fileList.value[1]?.raw ?? null;
        const response = await uploadVideo(videoFile,coverUrl);
        if (response.code === 200 && response.data && response.data.videoUrl) {
          mediaUrls.value.push(response.data.videoUrl);
          if (response.data.coverUrl) {
            mediaUrls.value.push(response.data.coverUrl);
          }
        } else {
          throw new Error(`上传视频失败: ${response.message || '未知错误'}`);
        }
      }
    }

    // 上传成功后发布新闻
    await createNewsPost();
    uploadDialogVisible.value = false;
  } catch (error) {
    console.error('Failed to upload files:', error);
    ElMessage.error(error.message || '上传文件失败，请稍后重试');
  } finally {
    uploadLoading.value = false;
  }
};

// 创建新闻帖子
const createNewsPost = async () => {
  try {
    const loadingInstance = ElLoading.service({
      text: '正在发布...',
      background: 'rgba(0, 0, 0, 0.7)'
    });

    const postData = {
      type: uploadType.value, // 0-图片 1-视频
      title: tempNewsData.value.title,
      category: tempNewsData.value.category,
      visibility: 0, // 0-可见 1-不可见
      content: tempNewsData.value.content,
      mediaUrls: mediaUrls.value // 媒体文件URL数组
    };

    const response = await createPost(postData);

    loadingInstance.close();

    if (response.code === 200) {
      ElMessage.success('发布成功');
      refreshNewsData();
      // 关闭上传对话框
      uploadDialogVisible.value = false;
      // 重置临时数据
      tempNewsData.value = {
        title: '',
        content: '',
        category: 0
      };
      mediaUrls.value = [];
    } else {
      ElMessage.error(`发布失败: ${response.message || '未知错误'}`);
    }
  } catch (error) {
    console.error('Failed to create post:', error);
    ElMessage.error('发布失败，请稍后重试');
  }
};

// 查看帖子详情
const viewPostDetail = (row) => {
  // 重定向到帖子详情页
  window.open(`/post/detail/${row.id}`, '_blank');
  console.log(`Navigating to post detail page for post ID: ${row.id}, category: ${row.category}`);
};

const deleteNews = (row) => {
  ElMessageBox.confirm('确定要删除这条新闻吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    // TODO: 实现删除API调用
    ElMessage.info('删除功能即将上线');
  }).catch(() => {
    // 用户取消操作
  });
};

// 切换帖子可见性
const togglePostVisibility = (row) => {
  const newVisibility = row.visibility === 0 ? 1 : 0;
  const visibilityText = newVisibility === 0 ? '可见' : '不可见';

  ElMessageBox.confirm(`确定要将该帖子设为${visibilityText}吗？`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      const response = await request.put(`/admin/post/${row.id}/visibility`, {
        visibility: newVisibility
      });

      if (response.code === 200 && response.data) {
        ElMessage.success(`已将帖子设为${visibilityText}`);
        // 更新本地数据
        row.visibility = newVisibility;
      } else {
        ElMessage.error('操作失败，请稍后重试');
      }
    } catch (error) {
      console.error('Failed to update post visibility:', error);
      ElMessage.error('操作失败，请稍后重试');
    }
  }).catch(() => {
    // 用户取消操作
  });
};

// 加载用户数据
const loadUserData = async (reset = true) => {
  try {
    userLoading.value = true;

    if (reset) {
      lastUserId.value = 0;
      userData.value = [];
      hasMoreUsers.value = true;
      showNoMoreUsersMessage.value = false;
    }

    // 调用API获取用户数据
    const response = await request.get('/admin/users', {
      params: {
        lastUserId: lastUserId.value
      }
    });

    if (response.code === 200 && response.data) {
      const users = response.data.userList || [];

      if (users.length === 0) {
        hasMoreUsers.value = false;
      } else {
        // 转换用户数据格式
        const formattedUsers = users.map(user => ({
          id: user.id,
          username: user.username,
          phone: user.phone,
          role: user.role || 0, // 0-普通用户 1-管理员
          status: user.status || 0 // 0-正常 1-禁用
        }));

        userData.value = [...userData.value, ...formattedUsers];
        lastUserId.value = response.data.lastUserId;

        // 如果lastUserId为1，标记没有更多数据
        if (lastUserId.value === 1) {
          hasMoreUsers.value = false;
          showNoMoreUsersMessage.value = true;
        }
      }
    } else {
      ElMessage.error('获取用户列表失败');
    }
  } catch (error) {
    console.error('Failed to load user data:', error);
    ElMessage.error('加载用户数据失败');
  } finally {
    userLoading.value = false;
  }
};

// 刷新用户数据
const refreshUserData = () => {
  loadUserData(true);
};

// 加载更多用户
const loadMoreUsers = () => {
  // 如果lastUserId已经是1，显示没有更多数据的提示
  if (lastUserId.value === 1) {
    showNoMoreUsersMessage.value = true;
    return;
  }

  // 如果没有更多用户或正在加载，直接返回
  if (!hasMoreUsers.value || userLoading.value) return;

  // 加载更多数据
  loadUserData(false);
};

// 切换用户角色（管理员/普通用户）
const toggleUserRole = async (row) => {
  try {
    const newRole = row.role === 1 ? 0 : 1;
    const response = await request.put(`/admin/user/${row.id}/role`, {
      role: newRole
    });

    if (response.code === 200) {
      row.role = newRole;
      ElMessage.success(`用户角色已${newRole === 1 ? '设为管理员' : '取消管理员权限'}`);
    } else {
      ElMessage.error(`操作失败: ${response.message || '未知错误'}`);
    }
  } catch (error) {
    console.error('Failed to toggle user role:', error);
    ElMessage.error('操作失败，请稍后重试');
  }
};

// 切换用户状态（启用/禁用）
const toggleUserStatus = async (row) => {
  try {
    const newStatus = row.status === 0 ? 1 : 0;
    const response = await request.put(`/admin/user/${row.id}/status`, {
      status: newStatus
    });

    if (response.code === 200) {
      row.status = newStatus;
      ElMessage.success(`用户已${newStatus === 0 ? '启用' : '禁用'}`);
    } else {
      ElMessage.error(`操作失败: ${response.message || '未知错误'}`);
    }
  } catch (error) {
    console.error('Failed to toggle user status:', error);
    ElMessage.error('操作失败，请稍后重试');
  }
};

// 加载资源数据
const loadResourceData = async (reset = true) => {
  try {
    resourceLoading.value = true;

    if (reset) {
      lastResourceId.value = 0;
      resourceData.value = [];
      hasMoreResources.value = true;
      showNoMoreResourcesMessage.value = false;
    }

    const response = await getResources(resourceTypeFilter.value, lastResourceId.value);

    if (response.code === 200 && response.data) {
      const resources = response.data.resourceList || [];

      if (resources.length === 0) {
        hasMoreResources.value = false;
      } else {
        // 转换资源数据格式
        const formattedResources = resources.map(resource => ({
          id: resource.id,
          title: resource.title,
          description: resource.description,
          content: resource.content,
          type: resource.type,
          typeName: getResourceTypeName(resource.type),
          coverUrl: resource.coverUrl,
          downloadUrl: resource.downloadUrl,
          downloadCount: resource.downloadCount,
          date: formatDate(resource.createdAt),
          uploader: resource.userBasicVO ? resource.userBasicVO.username : '未知用户'
        }));

        resourceData.value = [...resourceData.value, ...formattedResources];
        lastResourceId.value = response.data.lastResourceId;

        // 如果lastResourceId为1，标记没有更多数据
        if (lastResourceId.value === 1) {
          hasMoreResources.value = false;
          showNoMoreResourcesMessage.value = true;
        }
      }
    } else {
      ElMessage.error('获取资源列表失败');
    }
  } catch (error) {
    console.error('Failed to load resource data:', error);
    ElMessage.error('加载资源数据失败');
  } finally {
    resourceLoading.value = false;
  }
};

// 刷新资源数据
const refreshResourceData = () => {
  loadResourceData(true);
};

// 加载更多资源
const loadMoreResources = () => {
  // 如果lastResourceId已经是1，显示没有更多数据的提示
  if (lastResourceId.value === 1) {
    showNoMoreResourcesMessage.value = true;
    return;
  }

  // 如果没有更多资源或正在加载，直接返回
  if (!hasMoreResources.value || resourceLoading.value) return;

  // 加载更多数据
  loadResourceData(false);
};

// 加载帖子数据
const loadPostData = async (reset = true) => {
  try {
    postLoading.value = true;

    if (reset) {
      lastPostIdForPosts.value = 0;
      postData.value = [];
      hasMorePosts.value = true;
      showNoMorePostsMessage.value = false;
    }

    // 调用API获取帖子数据 - 使用真实后端数据
    const response = await request.get('/admin/posts', {
      params: {
        category: postCategoryFilter.value,
        lastPostId: lastPostIdForPosts.value
      }
    });

    if (response.code === 200 && response.data) {
      const posts = response.data.postVOList || [];

      if (posts.length === 0) {
        hasMorePosts.value = false;
      } else {
        // 转换帖子数据格式
        const formattedPosts = posts.map(post => ({
          id: post.id,
          date: formatDate(post.createdAt),
          title: post.title || '无标题',
          content: post.content,
          username: post.userBasicVO ? post.userBasicVO.username : '未知用户',
          likeCount: post.likeCount || 0,
          commentCount: post.commentCount || 0,
          coverUrl: post.coverUrl
        }));

        postData.value = [...postData.value, ...formattedPosts];
        lastPostIdForPosts.value = response.data.lastPostId;

        // 如果lastPostId为1，标记没有更多数据
        if (lastPostIdForPosts.value === 1) {
          hasMorePosts.value = false;
          showNoMorePostsMessage.value = true;
        }
      }
    } else {
      ElMessage.error('获取帖子列表失败');
    }
  } catch (error) {
    console.error('Failed to load post data:', error);
    ElMessage.error('加载帖子数据失败');
  } finally {
    postLoading.value = false;
  }
};

// 刷新帖子数据
const refreshPostData = () => {
  loadPostData(true);
};

// 加载更多帖子
const loadMorePosts = () => {
  // 如果lastPostId已经是1，显示没有更多数据的提示
  if (lastPostIdForPosts.value === 1) {
    showNoMorePostsMessage.value = true;
    return;
  }

  // 如果没有更多帖子或正在加载，直接返回
  if (!hasMorePosts.value || postLoading.value) return;

  // 加载更多数据
  loadPostData(false);
};

// 查看帖子详情
const viewPost = (row) => {
  window.open(`/post/${row.id}`, '_blank');
};

// 删除帖子
const deletePost = (row) => {
  ElMessageBox.confirm('确定要删除这篇帖子吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      const response = await request.delete(`/admin/post/${row.id}`);

      if (response.code === 200) {
        ElMessage.success('帖子删除成功');
        refreshPostData();
      } else {
        ElMessage.error(`删除失败: ${response.message || '未知错误'}`);
      }
    } catch (error) {
      console.error('Failed to delete post:', error);
      ElMessage.error('删除帖子失败，请稍后重试');
    }
  }).catch(() => {
    // 用户取消操作
  });
};

// 加载评论数据 - 使用真实后端数据
const loadCommentData = async (reset = true) => {
  try {
    commentLoading.value = true;

    if (reset) {
      lastCommentId.value = 0;
      commentData.value = [];
      hasMoreComments.value = true;
      showNoMoreCommentsMessage.value = false;
    }

    // 调用API获取评论数据
    const response = await request.get('/admin/comments', {
      params: {
        lastCommentId: lastCommentId.value
      }
    });

    if (response.code === 200 && response.data) {
      const comments = response.data.commentList || [];

      if (comments.length === 0) {
        hasMoreComments.value = false;
      } else {
        // 转换评论数据格式
        const formattedComments = comments.map(comment => ({
          id: comment.id,
          content: comment.content,
          username: comment.userBasicVO ? comment.userBasicVO.username : '未知用户',
          date: formatDate(comment.createTime),
          postId: comment.postId,
          postTitle: comment.postTitle || '未知帖子'
        }));

        commentData.value = [...commentData.value, ...formattedComments];
        lastCommentId.value = response.data.lastCommentId;

        // 如果lastCommentId为1，标记没有更多数据
        if (lastCommentId.value === 1) {
          hasMoreComments.value = false;
          showNoMoreCommentsMessage.value = true;
        }
      }
    } else {
      ElMessage.error('获取评论列表失败');
    }
  } catch (error) {
    console.error('Failed to load comment data:', error);
    ElMessage.error('加载评论数据失败');
  } finally {
    commentLoading.value = false;
  }
};

// 刷新评论数据
const refreshCommentData = () => {
  loadCommentData(true);
};



// 加载更多评论
const loadMoreComments = () => {
  // 如果没有更多评论或正在加载，直接返回
  if (!hasMoreComments.value || commentLoading.value) return;

  // 加载更多数据
  loadCommentData(false);
};

// 查看评论所属帖子
const viewCommentPost = (row) => {
  window.open(`/post/detail/${row.postId}`, '_blank');
};

// 删除评论
const deleteComment = (row) => {
  ElMessageBox.confirm('确定要删除这条评论吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      const response = await request.delete(`/admin/comment/${row.id}`);

      if (response.code === 200) {
        ElMessage.success('评论删除成功');
        refreshCommentData();
      } else {
        ElMessage.error(`删除失败: ${response.message || '未知错误'}`);
      }
    } catch (error) {
      console.error('Failed to delete comment:', error);
      ElMessage.error('删除评论失败，请稍后重试');
    }
  }).catch(() => {
    // 用户取消操作
  });
};

// 显示资源上传对话框
const showResourceUploadDialog = () => {
  // 重置表单
  resourceForm.value = {
    title: '',
    description: '',
    content: '',
    type: 1
  };
  resourceCoverFile.value = null;
  resourceFile.value = null;
  resourceUploadDialogVisible.value = true;
};

// 处理资源封面文件变化
const handleResourceCoverChange = (file) => {
  resourceCoverFile.value = file.raw;
};

// 处理资源文件变化
const handleResourceFileChange = (file) => {
  resourceFile.value = file.raw;
};

// 上传资源
const handleResourceUpload = async () => {
  // 表单验证
  if (!resourceForm.value.title) {
    ElMessage.warning('请输入资源标题');
    return;
  }

  if (!resourceForm.value.description) {
    ElMessage.warning('请输入资源描述');
    return;
  }

  if (!resourceFile.value) {
    ElMessage.warning('请上传资源文件');
    return;
  }

  try {
    resourceUploadLoading.value = true;

    // 1. 首先上传文件到 AliyunOSS
    let coverUrl = null;
    let downloadUrl = null;

    // 上传封面图片（如果有）
    if (resourceCoverFile.value) {
      const coverResponse = await uploadImage(resourceCoverFile.value);
      if (coverResponse.code === 200 && coverResponse.data && coverResponse.data.url) {
        coverUrl = coverResponse.data.url;
      } else {
        throw new Error(`上传封面图片失败: ${coverResponse.message || '未知错误'}`);
      }
    }

    // 上传资源文件
    const fileResponse = await uploadResource(resourceFile.value);
    if (fileResponse.code === 200 && fileResponse.data && fileResponse.data.url) {
      downloadUrl = fileResponse.data.url;
    } else {
      throw new Error(`上传资源文件失败: ${fileResponse.message || '未知错误'}`);
    }

    // 2. 创建资源记录，使用获取到的 URL

    // 发送请求创建资源记录
    // 使用URLSearchParams来确保参数以表单形式发送，而不是JSON
    const params = new URLSearchParams();
    params.append('type', resourceForm.value.type);
    params.append('title', resourceForm.value.title);
    params.append('description', resourceForm.value.description);
    params.append('content', resourceForm.value.content || '');
    if (coverUrl) params.append('coverUrl', coverUrl);
    params.append('downloadUrl', downloadUrl);

    // 添加额外的调试信息
    console.log('Sending resource upload request with params:', {
      type: resourceForm.value.type,
      title: resourceForm.value.title,
      description: resourceForm.value.description,
      content: resourceForm.value.content || '',
      coverUrl: coverUrl,
      downloadUrl: downloadUrl,
      token: localStorage.getItem('token'),
      userId: localStorage.getItem('userId')
    });

    // 确保用户ID和令牌存在
    if (!localStorage.getItem('userId') || !localStorage.getItem('token')) {
      console.warn('Missing userId or token in localStorage');
      ElMessage.warning('您可能需要重新登录以获取授权');
    }

    try {
      const response = await request.post('/resource/upload/url', params, {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          'token': localStorage.getItem('token'),
          'userId': localStorage.getItem('userId')
        }
      });

      if (response.code === 200 && response.data) {
        ElMessage.success('资源上传成功');
        resourceUploadDialogVisible.value = false;
        refreshResourceData();
      } else {
        ElMessage.error(`上传失败: ${response.message || '未知错误'}`);
      }
    } catch (error) {
      console.error('Failed to upload resource:', error);
      ElMessage.error('上传资源失败，请稍后重试: ' + (error.message || '未知错误'));
    }
  } catch (error) {
    console.error('Failed to process resource upload:', error);
    ElMessage.error('处理资源上传失败: ' + (error.message || '未知错误'));
  } finally {
    resourceUploadLoading.value = false;
  }
};

// 编辑资源
const editResource = (row) => {
  editingResource.value = { ...row };
  resourceEditDialogVisible.value = true;
};

// 保存资源编辑
const saveResourceEdit = async () => {
  if (!editingResource.value) return;

  try {
    // 使用URLSearchParams来确保参数以表单形式发送，而不是JSON
    const params = new URLSearchParams();
    params.append('type', editingResource.value.type);
    params.append('title', editingResource.value.title);
    params.append('description', editingResource.value.description);
    params.append('content', editingResource.value.content || '');

    // 添加额外的调试信息
    console.log('Sending resource update request with params:', {
      id: editingResource.value.id,
      type: editingResource.value.type,
      title: editingResource.value.title,
      description: editingResource.value.description,
      content: editingResource.value.content || '',
      token: localStorage.getItem('token'),
      userId: localStorage.getItem('userId')
    });

    // 确保用户ID和令牌存在
    if (!localStorage.getItem('userId') || !localStorage.getItem('token')) {
      console.warn('Missing userId or token in localStorage');
      ElMessage.warning('您可能需要重新登录以获取授权');
    }

    const response = await request.put(`/resource/${editingResource.value.id}`, params, {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'token': localStorage.getItem('token'),
        'userId': localStorage.getItem('userId')
      }
    });

    if (response.code === 200 && response.data) {
      ElMessage.success('资源更新成功');
      resourceEditDialogVisible.value = false;
      refreshResourceData();
    } else {
      ElMessage.error(`更新失败: ${response.message || '未知错误'}`);
    }
  } catch (error) {
    console.error('Failed to update resource:', error);
    ElMessage.error('更新资源失败，请稍后重试: ' + (error.message || '未知错误'));
  }
};

// 删除资源
const removeResource = (row) => {
  ElMessageBox.confirm('确定要删除这个资源吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      const response = await deleteResource(row.id);

      if (response.code === 200 && response.data) {
        ElMessage.success('资源删除成功');
        refreshResourceData();
      } else {
        ElMessage.error(`删除失败: ${response.message || '未知错误'}`);
      }
    } catch (error) {
      console.error('Failed to delete resource:', error);
      ElMessage.error('删除资源失败，请稍后重试');
    }
  }).catch(() => {
    // 用户取消操作
  });
};

// 在组件挂载时加载数据
onMounted(() => {
  loadNewsData();

  // 监听当前选中的部分，根据需要加载相应数据
  watch(currentSection, (newValue) => {
    if (newValue === 'resources') {
      loadResourceData();
    } else if (newValue === 'comments') {
      loadCommentData();
    } else if (newValue === 'users') {
      loadUserData();
    }
  });
});
</script>

<style scoped>
.admin-dashboard {
  display: flex;
  min-height: 100vh;
  background-color: #f8f9fa;
  color: #212529;
}

/* 确保所有按钮都有可见的颜色 */
.admin-dashboard :deep(.el-button) {
  transition: all 0.3s;
}

.admin-sidebar {
  width: 250px;
  background-color: #ffffff;
  border-right: 1px solid #dee2e6;
  padding: 20px 0;
}

.admin-logo {
  display: flex;
  align-items: center;
  padding: 0 20px;
  margin-bottom: 30px;
}

.admin-logo span {
  font-size: 20px;
  font-weight: 500;
}

.lab-logo {
  height: 36px;
  margin-right: 10px;
}

.menu-items {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.menu-item {
  display: flex;
  align-items: center;
  padding: 14px 20px;
  cursor: pointer;
  transition: all 0.3s;
  color: #495057;
  font-size: 18px;
}

.menu-item:hover {
  background-color: #f1f3f5;
  color: #0d6efd;
}

.menu-item.active {
  background-color: #e9ecef;
  border-left: 3px solid #0d6efd;
  color: #0d6efd;
}

.menu-item .el-icon {
  margin-right: 10px;
  font-size: 22px;
}

.admin-content {
  flex: 1;
  padding: 20px;
}

.admin-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 20px;
  border-bottom: 1px solid #dee2e6;
}

.header-title {
  font-size: 32px;
  font-weight: bold;
  color: #212529;
}

.header-actions .el-button {
  font-size: 18px;
  padding: 10px 20px;
}

.content-area {
  background-color: #ffffff;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

.section-content {
  min-height: 500px;
}

:deep(.el-table) {
  background-color: #ffffff;
  color: #212529;
  font-size: 16px;
}

:deep(.el-table tr) {
  background-color: #ffffff;
}

:deep(.el-table th) {
  background-color: #f8f9fa;
  color: #495057;
  border-bottom: 1px solid #dee2e6;
  font-size: 18px;
  font-weight: 600;
  padding: 12px 0;
}

:deep(.el-table th .cell) {
  padding-left: 0;
  padding-right: 0;
}

:deep(.el-table td) {
  border-bottom: 1px solid #dee2e6;
  font-size: 16px;
  padding: 10px 0;
}

:deep(.el-table td .cell) {
  padding-left: 0;
  padding-right: 0;
}

.table-cell-content {
  display: block;
  text-align: left;
  padding-left: 0;
}

:deep(.el-tabs__item) {
  color: #6c757d;
}

:deep(.el-tabs__item.is-active) {
  color: #0d6efd;
}

:deep(.el-tabs__active-bar) {
  background-color: #0d6efd;
}

.filter-row {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
}

.filter-row .el-button {
  padding: 8px 15px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

.pagination-container .el-button {
  padding: 8px 20px;
}

.no-more-data {
  color: #6c757d;
  font-size: 15px;
  padding: 10px 20px;
  background-color: #f8f9fa;
  border-radius: 4px;
  border: 1px solid #dee2e6;
}

/* 上传对话框样式 */
.upload-dialog-content {
  padding: 10px 0;
}

.upload-type-selector {
  margin-bottom: 20px;
}

.upload-area {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

/* 隐藏上传按钮当达到限制时 */
.hide-upload-button :deep(.el-upload--picture-card) {
  display: none;
}

:deep(.el-upload-list__item) {
  transition: all 0.3s;
}

:deep(.el-dialog__body) {
  padding-top: 10px;
}

:deep(.el-upload--picture-card) {
  background-color: #f8f9fa;
  border-color: #dee2e6;
  color: #6c757d;
}

:deep(.el-upload--picture-card:hover) {
  border-color: #0d6efd;
  color: #0d6efd;
}

:deep(.el-upload-list--picture-card .el-upload-list__item) {
  background-color: #f8f9fa;
  border-color: #dee2e6;
}

:deep(.el-radio__input.is-checked .el-radio__inner) {
  background-color: #0d6efd;
  border-color: #0d6efd;
}

:deep(.el-radio__label) {
  color: #212529;
}

:deep(.el-dialog) {
  background-color: #ffffff;
  border: 1px solid #dee2e6;
  border-radius: 8px;
}

:deep(.el-dialog__title) {
  color: #212529;
}

:deep(.el-dialog__header) {
  border-bottom: 1px solid #dee2e6;
}

:deep(.el-dialog__footer) {
  border-top: 1px solid #dee2e6;
}

.dialog-footer .el-button {
  padding: 8px 20px;
}

/* 资源管理样式 */
.download-count {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  gap: 5px;
  padding-left: 0;
}

.download-count-display {
  display: flex;
  align-items: center;
  gap: 5px;
  color: #0d6efd;
}

.resource-upload-form,
.resource-edit-form {
  padding: 10px 0;
}

:deep(.el-form-item__label) {
  color: #212529;
}

:deep(.el-input__inner),
:deep(.el-textarea__inner) {
  background-color: #ffffff;
  border-color: #ced4da;
  color: #212529;
}

:deep(.el-input__inner:focus),
:deep(.el-textarea__inner:focus) {
  border-color: #0d6efd;
}

:deep(.el-select__tags) {
  background-color: #ffffff;
}

:deep(.el-select-dropdown__item) {
  color: #212529;
}

:deep(.el-select-dropdown__item.hover),
:deep(.el-select-dropdown__item:hover) {
  background-color: #f8f9fa;
}

:deep(.el-select-dropdown__item.selected) {
  color: #0d6efd;
}

:deep(.el-select-dropdown) {
  background-color: #ffffff;
  border: 1px solid #dee2e6;
}

.resource-cover-uploader,
.resource-file-uploader {
  margin-top: 10px;
}

/* 可点击标题样式 */
.clickable-title {
  color: #0d6efd;
  cursor: pointer;
  transition: all 0.3s;
  font-size: 16px;
  font-weight: 500;
  padding-left: 0;
  display: block;
}

.news-title,
.resource-title {
  font-size: 18px;
  font-weight: 600;
  padding-left: 0;
  display: block;
}

.title-cell {
  padding-left: 10px;
}

.clickable-title:hover {
  text-decoration: underline;
}

/* 增大Element Plus组件字体大小 */
:deep(.el-button) {
  font-size: 16px;
}

:deep(.el-tag) {
  font-size: 15px;
  padding: 0 8px;
  height: 28px;
  line-height: 28px;
}

:deep(.el-select) {
  font-size: 16px;
}

:deep(.el-option) {
  font-size: 16px;
}

:deep(.el-dialog__title) {
  font-size: 20px;
}

:deep(.el-form-item__label) {
  font-size: 18px;
}

:deep(.el-input__inner),
:deep(.el-textarea__inner) {
  font-size: 16px;
}

:deep(.el-radio__label) {
  font-size: 16px;
}

:deep(.el-upload__tip) {
  font-size: 15px;
}

/* 操作按钮样式 */
.operation-buttons {
  display: flex;
  gap: 5px;
  justify-content: center;
}

.news-operations,
.user-operations,
.comment-operations,
.resource-operations {
  justify-content: flex-start;
  padding-left: 10px;
}

.operation-buttons .el-button {
  margin: 0;
  padding: 6px 10px;
}

/* 确保按钮颜色始终可见 */
:deep(.el-button) {
  opacity: 1 !important;
}

:deep(.el-button--primary) {
  background-color: #03e9f4 !important;
  border-color: #03e9f4 !important;
  color: #ffffff !important;
}

:deep(.el-button--success) {
  background-color: #67c23a !important;
  border-color: #67c23a !important;
  color: #ffffff !important;
}

:deep(.el-button--warning) {
  background-color: #e6a23c !important;
  border-color: #e6a23c !important;
  color: #ffffff !important;
}

:deep(.el-button--danger) {
  background-color: #f56c6c !important;
  border-color: #f56c6c !important;
  color: #ffffff !important;
}

/* 表头样式 */
.column-header {
  font-size: 18px;
  font-weight: 600;
  display: flex;
  justify-content: flex-start;
  padding-left: 10px;
}
</style>