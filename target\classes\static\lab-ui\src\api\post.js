import request from '../utils/request';

/**
 * 获取帖子列表
 * @param {Number} category - 分类 0-新闻动态 1-通知公告 2-学术动态
 * @param {Number} lastPostId - 上一页最后一条帖子ID，用于分页
 * @returns {Promise} - 返回帖子列表
 */
export function getPosts(category = null, lastPostId = 0) {
  return request.get('/post/section', {
    params: {
      category,
      lastPostId
    }
  });
}

/**
 * 获取帖子详情
 * @param {Number} postId - 帖子ID
 * @param {Number} lastCommentId - 上一页最后一条评论ID，用于分页加载更多评论
 * @returns {Promise} - 返回帖子详情
 */
export function getPostDetail(postId, lastCommentId = 0) {
  return request.get('/post/postDetail', {
    params: {
      postId,
      lastCommentId
    }
  });
}

/**
 * 发布帖子
 * @param {Object} postData - 帖子数据
 * @param {Number} postData.type - 类型 0-图片 1-视频
 * @param {Number} postData.category - 分类 0-新闻动态 1-通知公告 2-学术动态
 * @param {Number} postData.visibility - 可见性 0-公开 1-私密
 * @param {String} postData.content - 文字内容
 * @param {String[]} postData.mediaUrls - 媒体文件URL列表（已上传的文件URL）
 * @returns {Promise} - 返回发布结果
 */
export function createPost(postData) {
  // 直接发送JSON数据，不需要FormData
  return request.post('/post/post', postData, {
    headers: {
      'Content-Type': 'application/json'
    }
  });
}

/**
 * 点赞帖子
 * @param {Number} postId - 帖子ID
 * @returns {Promise} - 返回点赞结果
 */
export function likePost(postId) {
  return request.post('/post/doLikePost', null, {
    params: {
      postId
    }
  });
}

/**
 * 收藏帖子
 * @param {Number} postId - 帖子ID
 * @returns {Promise} - 返回收藏结果
 */
export function collectPost(postId) {
  return request.post('/post/doCollectPost', null, {
    params: {
      postId
    }
  });
}

/**
 * 添加评论
 * @param {Object} commentData - 评论数据
 * @param {Number} commentData.postId - 帖子ID
 * @param {String} commentData.content - 评论内容
 * @param {Number} commentData.parentId - 父评论ID，如果是一级评论则为0
 * @returns {Promise} - 返回评论结果
 */
export function addComment(commentData) {
  return request.post('/post/comment', commentData, {
    headers: {
      'Content-Type': 'application/json'
    }
  });
}

/**
 * 获取用户发布的帖子
 * @param {Number} authorId - 作者ID，不传则获取当前用户的帖子
 * @param {Number} lastPostId - 上一页最后一条帖子ID，用于分页
 * @returns {Promise} - 返回帖子列表
 */
export function getUserPosts(authorId = null, lastPostId = 0) {
  return request.get('/post/personPosts', {
    params: {
      authorId,
      lastPostId
    }
  });
}

/**
 * 获取评论的子评论
 * @param {Number} commentId - 评论ID
 * @param {Number} lastChildCommentId - 上一页最后一条子评论ID，用于分页
 * @returns {Promise} - 返回子评论列表
 */
export function getChildComments(commentId, lastChildCommentId = 0) {
  return request.get('/post/comment/child', {
    params: {
      commentId,
      lastChildCommentId
    }
  });
}

/**
 * 获取用户收藏的帖子
 * @param {Number} userId - 用户ID，不传则获取当前用户的收藏
 * @param {Number} lastPostId - 上一页最后一条帖子ID，用于分页
 * @returns {Promise} - 返回收藏的帖子列表
 */
export function getUserCollectedPosts(userId = null, lastPostId = 0) {
  return request.get('/post/collectedPosts', {
    params: {
      userId,
      lastPostId
    }
  });
}
