import request from '../utils/request';

/**
 * 上传图片
 * @param {File} file - 图片文件
 * @returns {Promise} - 返回上传结果，包含图片URL
 */
export function uploadImage(file) {
  const formData = new FormData();
  formData.append('file', file);

  return request.post('/file/upload/image', formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  });
}

/**
 * 上传图片
 * @param {File} file - 资源文件
 * @returns {Promise} - 返回上传结果，包含资源URL
 */
export function uploadResource(file) {
  const formData = new FormData();
  formData.append('file', file);

  return request.post('/file/upload/resource', formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  });
}

/**
 * 上传视频
 * @param {File} file - 视频文件
 * @param {File} cover - 封面图片（可选）
 * @returns {Promise} - 返回上传结果，包含视频URL和封面URL
 */
export function uploadVideo(file, cover = null) {
  const formData = new FormData();
  formData.append('file', file);
  
  if (cover) {
    formData.append('cover', cover);
  }
  // todo 设置视频默认coverUrl

  return request.post('/file/upload/video', formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  });
}

/**
 * 删除文件
 * @param {String} fileUrl - 文件URL
 * @returns {Promise} - 返回删除结果
 */
export function deleteFile(fileUrl) {
  return request.delete('/file/delete', {
    params: {
      url: fileUrl
    }
  });
}
