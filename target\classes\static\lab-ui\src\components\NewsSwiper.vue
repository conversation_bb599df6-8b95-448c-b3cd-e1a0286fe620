<template>
  <div class="news-swiper">
    <div class="tabs">
      <div
        v-for="(tab, index) in tabs"
        :key="index"
        :class="['tab', { active: activeTab === index }]"
        @click="setActiveTab(index)"
      >
        {{ tab }}
      </div>
    </div>

    <div class="swiper-container">
      <swiper
        :slides-per-view="1"
        :space-between="30"
        :modules="modules"
        @swiper="onSwiper"
        @slideChange="onSlideChange"
      >
        <swiper-slide v-for="(items, tabIndex) in tabsContent" :key="tabIndex">
          <div class="news-list">
            <div
              v-for="(item, index) in items.slice(0, 3)"
              :key="index"
              class="news-item"
              @click="viewNewsDetail(item, tabIndex)"
            >
              <div class="news-date">{{ item.date }}</div>
              <div class="news-title">{{ item.title }}</div>
            </div>
          </div>
        </swiper-slide>
      </swiper>
    </div>

    <div class="more-btn" @click="viewMore">了解详情</div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { Swiper, SwiperSlide } from 'swiper/vue';
import { EffectFade } from 'swiper/modules';
import 'swiper/css';
import 'swiper/css/effect-fade';

const props = defineProps({
  tabs: {
    type: Array,
    default: () => ['新闻动态', '通知公告', '学术动态']
  },
  tabsContent: {
    type: Array,
    default: () => []
  }
});

const emit = defineEmits(['more', 'viewDetail']);

const activeTab = ref(0);
const swiperInstance = ref(null);
const modules = [EffectFade];

const onSwiper = (swiper) => {
  swiperInstance.value = swiper;
};

const onSlideChange = () => {
  if (swiperInstance.value) {
    activeTab.value = swiperInstance.value.activeIndex;
  }
};

const setActiveTab = (index) => {
  activeTab.value = index;
  if (swiperInstance.value) {
    swiperInstance.value.slideTo(index);
  }
};

const viewMore = () => {
  emit('more', activeTab.value);
};

// Function to handle clicking on a news item
const viewNewsDetail = (item, tabIndex) => {
  // Get the news type based on tab index
  const typeMap = {
    0: 'news',
    1: 'notice',
    2: 'academic'
  };
  const type = typeMap[tabIndex] || 'news';

  // Emit event to parent component with the news item and type
  emit('viewDetail', { item, type });
};
</script>

<style scoped>
.news-swiper {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  color: #333333;
}

.tabs {
  display: flex;
  gap: 20px;
  margin-bottom: 30px;
}

.tab {
  font-size: 20px;
  cursor: pointer;
  padding-bottom: 6px;
  position: relative;
  color: rgba(51, 51, 51, 0.6);
  transition: color 0.3s;
  font-weight: 500;
}

.tab.active {
  color: #05d9e8;
  font-weight: 600;
}

.tab.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 2px;
  background: linear-gradient(90deg, #8a2be2, #05d9e8);
  box-shadow: 0 0 10px rgba(138, 43, 226, 0.3);
  animation: tabGlow 2s infinite alternate;
}

@keyframes tabGlow {
  0% {
    box-shadow: 0 0 5px rgba(138, 43, 226, 0.5);
  }
  100% {
    box-shadow: 0 0 15px rgba(5, 217, 232, 0.8);
  }
}

.swiper-container {
  width: 100%;
  min-height: 220px;
  max-height: 280px;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

:deep(.swiper) {
  width: 100%;
  height: 100%;
}

:deep(.swiper-slide) {
  height: auto;
  pointer-events: auto;
}

.news-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
  position: relative;
  z-index: 5;
  pointer-events: auto;
}

.news-item {
  cursor: pointer;
  transition: all 0.3s;
  display: flex;
  flex-direction: column;
  padding: 15px 18px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  position: relative;
  overflow: hidden;
  z-index: 10;
  pointer-events: auto;
  background-color: rgba(255, 255, 255, 0.8);
  margin-bottom: 12px;
  border-radius: 8px;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.06);
}

.news-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 3px;
  height: 100%;
  background: linear-gradient(to bottom, #8a2be2, #05d9e8);
  opacity: 0;
  transition: opacity 0.3s;
}

.news-item:last-child {
  border-bottom: none;
  margin-bottom: 5px;
}

.news-item:hover {
  background-color: rgba(255, 255, 255, 0.95);
  transform: translateX(5px);
  box-shadow: 0 4px 12px rgba(5, 217, 232, 0.15);
}

.news-item:hover::before {
  opacity: 1;
}

.news-date {
  font-size: 14px;
  color: #05d9e8;
  margin-bottom: 8px;
  font-weight: 500;
}

.news-title {
  font-size: 17px;
  line-height: 1.5;
  transition: all 0.3s;
  position: relative;
  padding-left: 8px;
  color: #333333;
}

.news-title::before {
  content: '>';
  position: absolute;
  left: 0;
  top: 0;
  color: rgba(255, 42, 109, 0.8);
  opacity: 0;
  transition: opacity 0.3s;
}

.news-item:hover .news-title {
  color: #05d9e8;
}

.news-item:hover .news-title::before {
  opacity: 1;
}

.more-btn {
  margin-top: 20px;
  padding: 10px 28px;
  background-color: rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(5, 217, 232, 0.5);
  text-align: center;
  cursor: pointer;
  transition: all 0.3s;
  font-size: 16px;
  border-radius: 20px;
  align-self: center;
  position: relative;
  overflow: hidden;
  color: #05d9e8;
  box-shadow: 0 2px 8px rgba(5, 217, 232, 0.1);
  font-weight: 500;
}

.more-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(5, 217, 232, 0.2), transparent);
  transition: all 0.5s;
}

.more-btn:hover {
  background-color: rgba(5, 217, 232, 0.1);
  border-color: rgba(5, 217, 232, 0.8);
  color: #05d9e8;
  box-shadow: 0 4px 12px rgba(5, 217, 232, 0.2);
  transform: translateY(-2px);
}

.more-btn:hover::before {
  left: 100%;
}


</style>
