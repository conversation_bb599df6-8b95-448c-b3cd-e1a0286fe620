<template>
  <div class="user-profile-container">
    <!-- 赛博朋克风格背景元素 -->
    <div class="cyberpunk-container">
      <div class="cyberpunk-overlay"></div>
      <div class="cyberpunk-background"></div>
      <div class="cyberpunk-grid"></div>
      <div class="cyberpunk-scanline"></div>
      <div class="cyberpunk-glitch-effect"></div>
    </div>

    <div class="profile-header">
      <h1>个人资料</h1>
      <div class="profile-actions">
        <el-button type="primary" @click="handleSave" :loading="saving">
          <span class="button-text">保存更改</span>
          <span class="button-glow"></span>
        </el-button>
      </div>
    </div>

    <div class="profile-content">
      <div class="profile-sidebar">
        <!-- 添加全息数据元素 -->
        <div class="holo-data-element">
          <div class="holo-circle"></div>
          <div class="holo-text">USER DATA</div>
          <div class="holo-scan-line"></div>
          <div class="holo-glow"></div>
        </div>

        <div class="avatar-section">
          <div class="avatar-frame">
            <el-avatar
              :size="120"
              :src="userForm.avatar || userForm.image || 'https://randomuser.me/api/portraits/men/1.jpg'"
              @error="handleAvatarError"
            ></el-avatar>
            <div class="avatar-glow"></div>
            <div class="avatar-border"></div>
            <div class="avatar-corner top-left"></div>
            <div class="avatar-corner top-right"></div>
            <div class="avatar-corner bottom-left"></div>
            <div class="avatar-corner bottom-right"></div>
          </div>
          <el-upload
            class="avatar-uploader"
            action="#"
            :auto-upload="false"
            :show-file-list="false"
            :on-change="handleAvatarChange"
            accept="image/jpeg,image/png,image/gif,image/webp"
          >
            <el-button size="small" type="primary">
              <span class="button-text">更换头像</span>
              <span class="button-glow"></span>
            </el-button>
          </el-upload>
        </div>

        <div class="user-stats">
          <div class="stat-item">
            <div class="stat-label">发布帖子</div>
            <div class="stat-value">{{ userStats.postCount || 0 }}</div>
            <div class="stat-bar-container">
              <div class="stat-bar" :style="{ width: `${Math.min(userStats.postCount * 5, 100)}%` }"></div>
              <div class="stat-bar-glow" :style="{ width: `${Math.min(userStats.postCount * 5, 100)}%` }"></div>
            </div>
          </div>
          <div class="stat-item">
            <div class="stat-label">获得点赞</div>
            <div class="stat-value">{{ userStats.likeCount || 0 }}</div>
            <div class="stat-bar-container">
              <div class="stat-bar" :style="{ width: `${Math.min(userStats.likeCount, 100)}%` }"></div>
              <div class="stat-bar-glow" :style="{ width: `${Math.min(userStats.likeCount, 100)}%` }"></div>
            </div>
          </div>
          <div class="stat-item">
            <div class="stat-label">收藏帖子</div>
            <div class="stat-value">{{ userCollectedPosts.length || 0 }}</div>
            <div class="stat-bar-container">
              <div class="stat-bar" :style="{ width: `${Math.min(userCollectedPosts.length * 10, 100)}%` }"></div>
              <div class="stat-bar-glow" :style="{ width: `${Math.min(userCollectedPosts.length * 10, 100)}%` }"></div>
            </div>
          </div>
          <div class="stat-item">
            <div class="stat-label">注册时间</div>
            <div class="stat-value">{{ formatDate(userInfo.createTime) }}</div>
            <div class="stat-date-icon"></div>
          </div>
        </div>

        <!-- 添加全息装饰元素 -->
        <div class="holographic-container">
          <div class="holo-element">
            <div class="holo-lines"></div>
            <div class="holo-circle"></div>
            <div class="holo-data">USER DATA</div>
          </div>
        </div>
      </div>

      <div class="profile-details">
        <div class="details-header">
          <h2>个人信息</h2>
          <div class="details-header-line"></div>
          <div class="details-header-glow"></div>
          <div class="details-header-corner top-left"></div>
          <div class="details-header-corner top-right"></div>
        </div>

        <el-form :model="userForm" label-position="top">
          <el-form-item label="用户名">
            <el-input
              v-model="userForm.username"
              placeholder="请输入用户名"
              class="cyberpunk-input"
            ></el-input>
          </el-form-item>
          <el-form-item label="手机号">
            <div class="phone-display">
              <span class="phone-number">{{ userForm.phone || '未设置' }}</span>
              <div class="phone-icon"></div>
              <div class="phone-glow"></div>
            </div>
            <div class="form-hint">手机号不可修改</div>
          </el-form-item>
          <el-form-item label="个性签名">
            <el-input
              v-model="userForm.signature"
              type="textarea"
              :rows="4"
              placeholder="请输入个性签名"
              class="cyberpunk-input"
            ></el-input>
          </el-form-item>
        </el-form>

        <!-- 添加装饰性数据线条 -->
        <div class="data-lines">
          <div class="data-line"></div>
          <div class="data-line"></div>
          <div class="data-line"></div>
          <div class="data-circuit"></div>
          <div class="data-pulse"></div>
        </div>
      </div>
    </div>

    <div class="user-content-tabs">
      <el-tabs v-model="activeTab" @tab-click="handleTabClick">
        <el-tab-pane label="我的帖子" name="posts">
          <div v-if="userPosts.length > 0" class="posts-grid">
            <div v-for="post in userPosts" :key="post.id" class="post-card" @click="viewPostDetail(post)">
              <div class="post-image">
                <img :src="post.coverUrl || `https://picsum.photos/id/${(parseInt(post.id) % 10) + 1}/400/300`" :alt="post.content" />
                <div class="image-overlay"></div>
                <div class="image-glitch"></div>
                <div class="image-scan-line"></div>
                <div class="image-corner top-left"></div>
                <div class="image-corner top-right"></div>
                <div class="image-corner bottom-left"></div>
                <div class="image-corner bottom-right"></div>
              </div>
              <div class="post-content">
                <div class="post-category" v-if="post.category !== undefined">
                  {{ getCategoryName(post.category) }}
                </div>
                <p class="post-text">{{ post.title }}</p>
                <div class="post-info">
                  <div class="user-info" v-if="post.userBasicVO">
                    <img
                      :src="post.userBasicVO?.image || 'https://randomuser.me/api/portraits/men/1.jpg'"
                      :alt="post.userBasicVO?.username || '匿名用户'"
                      class="user-avatar"
                      @error="handleImageError"
                    />
                    <span class="username">{{ post.userBasicVO?.username || '匿名用户' }}</span>
                  </div>
                  <div class="post-stats">
                    <span class="like-count">
                      <div class="heart-icon"></div> {{ post.likeCount || 0 }}
                    </span>
                  </div>
                </div>
                <div class="card-border"></div>
              </div>
            </div>
          </div>
          <div v-else class="no-posts">
            <div class="no-data-icon"></div>
            <p>暂无帖子</p>
            <div class="no-data-line"></div>
          </div>
        </el-tab-pane>

        <el-tab-pane label="我的收藏" name="collections">
          <div v-if="userCollectedPosts.length > 0" class="posts-grid">
            <div v-for="post in userCollectedPosts" :key="post.id" class="post-card" @click="viewPostDetail(post)">
              <div class="post-image">
                <img :src="post.coverUrl || `https://picsum.photos/id/${(parseInt(post.id) % 10) + 1}/400/300`" :alt="post.content" />
                <div class="image-overlay"></div>
                <div class="image-glitch"></div>
                <div class="image-scan-line"></div>
                <div class="image-corner top-left"></div>
                <div class="image-corner top-right"></div>
                <div class="image-corner bottom-left"></div>
                <div class="image-corner bottom-right"></div>
              </div>
              <div class="post-content">
                <div class="post-category" v-if="post.category !== undefined">
                  {{ getCategoryName(post.category) }}
                </div>
                <p class="post-text">{{ post.title }}</p>
                <div class="post-info">
                  <div class="user-info" v-if="post.userBasicVO">
                    <img
                      :src="post.userBasicVO?.image || 'https://randomuser.me/api/portraits/men/1.jpg'"
                      :alt="post.userBasicVO?.username || '匿名用户'"
                      class="user-avatar"
                      @error="handleImageError"
                    />
                    <span class="username">{{ post.userBasicVO?.username || '匿名用户' }}</span>
                  </div>
                  <div class="post-stats">
                    <span class="like-count">
                      <div class="heart-icon"></div> {{ post.likeCount || 0 }}
                    </span>
                  </div>
                </div>
                <div class="card-border"></div>
              </div>
            </div>
          </div>
          <div v-else class="no-posts">
            <div class="no-data-icon"></div>
            <p>暂无收藏</p>
            <div class="no-data-line"></div>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue';
import { useRouter } from 'vue-router';
import { ElMessage, ElLoading } from 'element-plus';
import { getUserInfo, updateUserInfo } from '../api/user';
import { getUserPosts, getUserCollectedPosts } from '../api/post';
import { uploadImage } from '../api/file';

const router = useRouter();
const userInfo = ref({});
const userForm = reactive({
  username: '',
  phone: '',
  signature: '',
  avatar: ''
});
const userStats = reactive({
  postCount: 0,
  likeCount: 0,
  collectCount: 0
});
const userPosts = ref([]);
const userCollectedPosts = ref([]);
const saving = ref(false);
const avatarFile = ref(null);
const activeTab = ref('posts');

// 格式化日期
const formatDate = (dateStr) => {
  if (!dateStr) return '未知';
  const date = new Date(dateStr);
  return date.toLocaleDateString('zh-CN').replace(/\//g, '-');
};

// 获取用户信息
const fetchUserInfo = async () => {
  try {
    console.log('UserProfile: Fetching user info...');
    console.log('UserProfile: Token:', localStorage.getItem('token'));
    console.log('UserProfile: UserId:', localStorage.getItem('userId'));

    const response = await getUserInfo();
    console.log('UserProfile: User info response:', response);

    if (response.code === 200 && response.data) {
      userInfo.value = response.data;

      // Store userId in localStorage if not already there
      if (!localStorage.getItem('userId') && response.data.id) {
        localStorage.setItem('userId', response.data.id);
        console.log('UserProfile: User ID stored:', response.data.id);
      }

      // 填充表单
      userForm.username = response.data.username || '';
      userForm.phone = response.data.phone || '';
      userForm.signature = response.data.signature || '';
      userForm.image = response.data.image || '';
      // 清除临时头像数据，使用服务器返回的头像URL
      userForm.avatar = '';
      avatarFile.value = null;

      // 填充统计数据
      userStats.postCount = response.data.postCount || 0;
      userStats.likeCount = response.data.likeCount || 0;
    } else {
      console.error('UserProfile: Failed to get user info, response code:', response.code);
      ElMessage.error('获取用户信息失败');
      // 如果获取失败，可能是未登录，跳转到登录页
      router.push('/login');
    }
  } catch (error) {
    console.error('UserProfile: Failed to fetch user info:', error);
    console.error('UserProfile: Error details:', error.message);
    ElMessage.error('获取用户信息失败，请稍后重试');

    // Clear token and redirect to login
    localStorage.removeItem('token');
    localStorage.removeItem('userId');
    router.push('/login');
  }
};

// 获取用户帖子
const fetchUserPosts = async () => {
  try {
    // 使用一个较大的lastPostId值确保能获取到所有帖子，包括ID为5的帖子
    const response = await getUserPosts(null, 1000);
    if (response.code === 200 && response.data) {
      console.log('UserProfile: User posts response:', response.data);
      userPosts.value = response.data.postVOList.map(post => ({
        id: post.id,
        title: post.title,
        date: formatDate(post.createTime),
        coverUrl: post.coverUrl,
        likeCount: post.likeCount || 0,
        userBasicVO: post.userBasicVO,
        content: post.content
      }));
    }
  } catch (error) {
    console.error('Failed to fetch user posts:', error);
    ElMessage.error('获取用户帖子失败，请稍后重试');
  }
};

// 获取用户收藏的帖子
const fetchUserCollectedPosts = async () => {
  try {
    // 使用一个较大的lastPostId值确保能获取到所有收藏的帖子
    const response = await getUserCollectedPosts(null, 1000);
    if (response.code === 200 && response.data) {
      console.log('UserProfile: User collected posts response:', response.data);
      userCollectedPosts.value = response.data.postVOList.map(post => ({
        id: post.id,
        title: post.content.split('\n')[0] || '无标题',
        date: formatDate(post.createTime),
        coverUrl: post.coverUrl,
        likeCount: post.likeCount || 0,
        userBasicVO: post.userBasicVO,
        content: post.content
      }));
    }
  } catch (error) {
    console.error('Failed to fetch user collected posts:', error);
    ElMessage.error('获取收藏帖子失败，请稍后重试');
  }
};

// 处理标签页切换
const handleTabClick = (tab) => {
  if (tab.props.name === 'collections' && userCollectedPosts.value.length === 0) {
    fetchUserCollectedPosts();
  }
};

// 处理头像上传
const handleAvatarChange = (file) => {
  // 验证文件类型
  const isImage = file.raw.type.startsWith('image/');
  if (!isImage) {
    ElMessage.error('只能上传图片文件!');
    return;
  }

  // 验证文件大小 (限制为2MB)
  const isLt2M = file.raw.size / 1024 / 1024 < 2;
  if (!isLt2M) {
    ElMessage.error('图片大小不能超过2MB!');
    return;
  }

  // 保存文件引用
  avatarFile.value = file.raw;
  console.log('UserProfile: Avatar file selected:', file.raw.name);

  // 预览头像
  const reader = new FileReader();
  reader.onload = (e) => {
    userForm.avatar = e.target.result;
    console.log('UserProfile: Avatar preview loaded');
  };
  reader.readAsDataURL(file.raw);
};

// 保存用户信息
const handleSave = async () => {
  saving.value = true;

  // 显示全屏加载
  const loadingInstance = ElLoading.service({
    text: '正在保存...',
    background: 'rgba(0, 0, 0, 0.7)'
  });

  try {
    // 构建更新数据
    const updateData = {
      username: userForm.username,
      signature: userForm.signature
    };

    // 如果有新头像，需要上传
    if (avatarFile.value) {
      console.log('UserProfile: Uploading new avatar...');
      try {
        // 上传头像到服务器获取URL
        const uploadResponse = await uploadImage(avatarFile.value);

        if (uploadResponse.code === 200 && uploadResponse.data && uploadResponse.data.url) {
          // 获取上传后的URL
          const imageUrl = uploadResponse.data.url;
          console.log('UserProfile: Avatar uploaded successfully, URL:', imageUrl);

          // 将返回的URL赋值给updateData.image (注意后端字段是image而不是avatar)
          updateData.image = imageUrl;
        } else {
          throw new Error(`上传头像失败: ${uploadResponse.message || '未知错误'}`);
        }
      } catch (uploadError) {
        console.error('UserProfile: Failed to upload avatar:', uploadError);
        ElMessage.error('头像上传失败，请稍后重试');
        loadingInstance.close();
        saving.value = false;
        return;
      }
    }

    console.log('UserProfile: Updating user info with data:', updateData);
    const response = await updateUserInfo(updateData);

    if (response.code === 200) {
      ElMessage.success('保存成功');
      // 刷新用户信息
      await fetchUserInfo();
    } else {
      ElMessage.error(response.message || '保存失败');
    }
  } catch (error) {
    console.error('UserProfile: Failed to save user info:', error);
    ElMessage.error('保存失败，请稍后重试');
  } finally {
    loadingInstance.close();
    saving.value = false;
  }
};

// 查看帖子详情
const viewPostDetail = (post) => {
  // Always navigate to the post detail page when clicking on a post in search results
  router.push(`/post/detail/${post.id}`);

  // Log the navigation for debugging
  console.log(`Navigating to post detail page for post ID: ${post.id}, category: ${post.category}`);
};

// 处理图片加载错误
const handleImageError = (e) => {
  e.target.src = 'https://randomuser.me/api/portraits/men/1.jpg';
};

// 处理头像加载错误
const handleAvatarError = (e) => {
  console.error('UserProfile: Avatar image failed to load');
  e.target.src = 'https://randomuser.me/api/portraits/men/1.jpg';
};

// 获取分类名称
const getCategoryName = (category) => {
  const categories = {
    0: '新闻',
    1: '设备',
    2: '师生',
    3: '生活'
  };
  return categories[category] || '其他';
};

// 生命周期钩子
onMounted(async () => {
  console.log('UserProfile: Component mounted');

  // 检查是否登录
  const token = localStorage.getItem('token');
  console.log('UserProfile: Token from localStorage:', token);

  if (!token) {
    console.log('UserProfile: No token found, redirecting to login');
    ElMessage.warning('请先登录');
    router.push('/login');
    return;
  }

  // Log user ID if available
  const userId = localStorage.getItem('userId');
  console.log('UserProfile: UserId from localStorage:', userId);

  try {
    console.log('UserProfile: Fetching user data...');
    // 获取用户信息和帖子
    await fetchUserInfo();
    await fetchUserPosts();

    // 预加载收藏的帖子
    fetchUserCollectedPosts();

    console.log('UserProfile: Data fetched successfully');
  } catch (error) {
    console.error('UserProfile: Failed to initialize user profile:', error);
    console.error('UserProfile: Error details:', error.message);
    ElMessage.error('加载用户资料失败，请稍后重试');
  }
});
</script>

<style scoped>
.user-profile-container {
  max-width: 1200px;
  margin: 80px auto 40px;
  padding: 0 20px;
  color: #333333;
  position: relative;
  z-index: 1;
}

/* 赛博朋克背景样式 */
.cyberpunk-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
  overflow: hidden;
}

.cyberpunk-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.8) 0%, rgba(240, 240, 240, 0.7) 100%);
  z-index: 2;
  animation: overlayPulse 8s infinite alternate;
}

.cyberpunk-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: url('https://images.unsplash.com/photo-1550745165-9bc0b252726f?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80');
  background-size: cover;
  background-position: center;
  z-index: 1;
  filter: brightness(0.9) saturate(1.1) hue-rotate(0deg);
  animation: backgroundScale 20s infinite alternate;
}

.cyberpunk-grid {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image:
    linear-gradient(rgba(0, 162, 255, 0.05) 1px, transparent 1px),
    linear-gradient(90deg, rgba(0, 162, 255, 0.05) 1px, transparent 1px);
  background-size: 40px 40px;
  z-index: 3;
  animation: gridPulse 15s infinite alternate;
}

.cyberpunk-scanline {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: repeating-linear-gradient(
    to bottom,
    transparent 0%,
    rgba(0, 0, 0, 0.02) 0.5%,
    transparent 1%
  );
  background-size: 100% 4px;
  z-index: 4;
  pointer-events: none;
  opacity: 0.2;
}

.cyberpunk-glitch-effect {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: transparent;
  z-index: 5;
  pointer-events: none;
  overflow: hidden;
}

.cyberpunk-glitch-effect::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 42, 109, 0.1), transparent);
  animation: glitchSweep 10s infinite;
}

.profile-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 1px solid rgba(5, 217, 232, 0.2);
  position: relative;
}

.profile-header::after {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 0;
  width: 100%;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(5, 217, 232, 0.5), transparent);
  animation: borderGlow 4s infinite alternate;
}

.profile-header h1 {
  font-size: 32px;
  font-weight: bold;
  margin: 0;
  color: #333333;
  text-shadow: 1px 1px 3px rgba(138, 43, 226, 0.3);
  position: relative;
}

.profile-content {
  display: flex;
  gap: 40px;
  margin-bottom: 40px;
  position: relative;
  z-index: 2;
}

.profile-sidebar {
  width: 300px;
  background-color: rgba(255, 255, 255, 0.95);
  border-radius: 8px;
  padding: 30px;
  display: flex;
  flex-direction: column;
  align-items: center;
  border: 1px solid rgba(5, 217, 232, 0.1);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
  position: relative;
  overflow: hidden;
}

.profile-sidebar::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(45deg, transparent 65%, rgba(5, 217, 232, 0.05) 75%, transparent 85%);
  animation: scanEffect 3s linear infinite;
}

.avatar-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 15px;
  margin-bottom: 30px;
  position: relative;
}

.avatar-frame {
  position: relative;
  padding: 8px;
}

.avatar-glow {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  box-shadow: 0 0 15px rgba(138, 43, 226, 0.3);
  animation: avatarGlow 4s infinite alternate;
}

.avatar-border {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  border: 2px solid rgba(5, 217, 232, 0.4);
  box-sizing: border-box;
}

.avatar-corner {
  position: absolute;
  width: 10px;
  height: 10px;
  border: 2px solid rgba(255, 42, 109, 0.5);
}

.avatar-corner.top-left {
  top: 0;
  left: 0;
  border-right: none;
  border-bottom: none;
}

.avatar-corner.top-right {
  top: 0;
  right: 0;
  border-left: none;
  border-bottom: none;
}

.avatar-corner.bottom-left {
  bottom: 0;
  left: 0;
  border-right: none;
  border-top: none;
}

.avatar-corner.bottom-right {
  bottom: 0;
  right: 0;
  border-left: none;
  border-top: none;
}

.user-stats {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  padding: 10px 0;
  border-bottom: 1px solid rgba(5, 217, 232, 0.1);
  position: relative;
}

.stat-item::after {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 0;
  width: 100%;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(5, 217, 232, 0.2), transparent);
}

.stat-label {
  color: #666666;
  font-size: 14px;
  text-transform: uppercase;
  letter-spacing: 1px;
  font-weight: 500;
}

.stat-value {
  font-weight: bold;
  color: #05d9e8;
  text-shadow: 1px 1px 2px rgba(5, 217, 232, 0.2);
}

.stat-bar-container {
  position: relative;
  width: 100%;
  height: 4px;
  background-color: rgba(0, 0, 0, 0.05);
  border-radius: 2px;
  margin-top: 8px;
  overflow: hidden;
}

.stat-bar {
  height: 100%;
  background-color: #ff2a6d;
  border-radius: 2px;
  transition: width 0.5s ease;
}

.stat-bar-glow {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 42, 109, 0.5), transparent);
  border-radius: 2px;
  animation: barGlow 2s infinite alternate;
}

.profile-details {
  flex: 1;
  background-color: rgba(255, 255, 255, 0.95);
  border-radius: 8px;
  padding: 30px;
  border: 1px solid rgba(5, 217, 232, 0.1);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
  position: relative;
  overflow: hidden;
}

.details-header {
  position: relative;
  margin-bottom: 30px;
  padding-bottom: 15px;
}

.details-header h2 {
  font-size: 24px;
  color: #333333;
  margin: 0 0 10px;
  text-shadow: 1px 1px 3px rgba(138, 43, 226, 0.2);
  font-weight: 600;
}

.details-header-line {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 1px;
  background-color: rgba(5, 217, 232, 0.1);
}

.details-header-glow {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(5, 217, 232, 0.5), transparent);
  animation: borderGlow 4s infinite alternate;
}

.details-header-corner {
  position: absolute;
  width: 15px;
  height: 15px;
  border: 2px solid rgba(255, 42, 109, 0.5);
}

.details-header-corner.top-left {
  top: 0;
  left: 0;
  border-right: none;
  border-bottom: none;
}

.details-header-corner.top-right {
  top: 0;
  right: 0;
  border-left: none;
  border-bottom: none;
}

.form-hint {
  font-size: 12px;
  color: #999999;
  margin-top: 5px;
  font-style: italic;
}

.phone-display {
  background-color: rgba(248, 249, 250, 0.8);
  border-radius: 4px;
  padding: 12px 15px;
  color: #ff2a6d;
  font-size: 16px;
  border: 1px solid rgba(255, 42, 109, 0.2);
  position: relative;
  overflow: hidden;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
}

.phone-display::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 50%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 42, 109, 0.1), transparent);
  animation: phoneSweep 3s infinite;
}

.phone-number {
  font-family: 'Courier New', monospace;
  letter-spacing: 1px;
  font-weight: bold;
}

.phone-icon {
  position: absolute;
  right: 15px;
  top: 50%;
  transform: translateY(-50%);
  width: 16px;
  height: 16px;
  background-color: #ff2a6d;
  border-radius: 2px;
}

.phone-glow {
  position: absolute;
  right: 15px;
  top: 50%;
  transform: translateY(-50%);
  width: 16px;
  height: 16px;
  border-radius: 2px;
  box-shadow: 0 0 8px rgba(255, 42, 109, 0.5);
  animation: phoneGlow 2s infinite alternate;
}

/* Holographic container */
.holographic-container {
  position: relative;
  width: 100%;
  height: 120px;
  margin-top: 20px;
}

.holo-element {
  position: relative;
  width: 100%;
  height: 100%;
  animation: holoFloat 4s ease-in-out infinite;
}

.holo-lines {
  position: absolute;
  top: 10%;
  left: 10%;
  width: 80%;
  height: 80%;
  border: 1px solid rgba(5, 217, 232, 0.4);
  border-radius: 50%;
}

.holo-lines::before, .holo-lines::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 0;
  width: 100%;
  height: 1px;
  background: rgba(5, 217, 232, 0.4);
}

.holo-lines::after {
  transform: rotate(90deg);
}

.holo-circle {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 60%;
  height: 60%;
  border-radius: 50%;
  border: 1px solid rgba(5, 217, 232, 0.6);
  box-shadow: 0 0 10px rgba(5, 217, 232, 0.3);
  animation: holoPulse 2s ease-in-out infinite;
}

.holo-data {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: rgba(5, 217, 232, 0.8);
  font-size: 14px;
  letter-spacing: 2px;
  text-shadow: 1px 1px 3px rgba(5, 217, 232, 0.3);
  white-space: nowrap;
  font-weight: 500;
}

.user-content-tabs {
  margin-top: 40px;
  position: relative;
  z-index: 2;
}

.posts-grid {
  display: grid;
  grid-template-columns: repeat(4, minmax(250px, 280px));
  gap: 20px;
  margin-bottom: 40px;
  justify-content: center;
}

.post-card {
  background-color: rgba(255, 255, 255, 0.95);
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  cursor: pointer;
  position: relative;
  height: 100%;
  display: flex;
  flex-direction: column;
  border: 1px solid rgba(5, 217, 232, 0.1);
}

.post-card:hover {
  transform: translateY(-5px) scale(1.02);
  box-shadow: 0 8px 20px rgba(5, 217, 232, 0.15);
  border-color: rgba(5, 217, 232, 0.3);
}

.post-card:hover .image-glitch {
  opacity: 0.4;
}

.post-card:hover .image-scan-line {
  opacity: 0.3;
}

.post-image {
  position: relative;
  width: 100%;
  height: 0;
  padding-bottom: 75%; /* 4:3 aspect ratio */
  overflow: hidden;
}

.post-image img {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: all 0.5s ease;
  filter: brightness(0.95) contrast(1.05);
}

.post-card:hover .post-image img {
  filter: brightness(1.05) contrast(1.1) saturate(1.1);
}

.image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(138, 43, 226, 0.1) 0%, rgba(5, 217, 232, 0.1) 100%);
  z-index: 1;
}

.image-glitch {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 42, 109, 0.1), transparent);
  z-index: 2;
  opacity: 0;
  animation: imageGlitch 5s infinite;
}

.image-scan-line {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: repeating-linear-gradient(
    to bottom,
    transparent 0%,
    rgba(5, 217, 232, 0.05) 0.5%,
    transparent 1%
  );
  background-size: 100% 4px;
  z-index: 3;
  opacity: 0.2;
}

.image-corner {
  position: absolute;
  width: 15px;
  height: 15px;
  border: 2px solid rgba(5, 217, 232, 0.8);
  z-index: 4;
}

.image-corner.top-left {
  top: 10px;
  left: 10px;
  border-right: none;
  border-bottom: none;
}

.image-corner.top-right {
  top: 10px;
  right: 10px;
  border-left: none;
  border-bottom: none;
}

.image-corner.bottom-left {
  bottom: 10px;
  left: 10px;
  border-right: none;
  border-top: none;
}

.image-corner.bottom-right {
  bottom: 10px;
  right: 10px;
  border-left: none;
  border-top: none;
}

.post-content {
  padding: 15px;
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  position: relative;
  background-color: rgba(255, 255, 255, 0.95);
  border-top: 1px solid rgba(5, 217, 232, 0.1);
}

.post-category {
  display: inline-block;
  padding: 3px 8px;
  background-color: rgba(138, 43, 226, 0.1);
  border: 1px solid rgba(138, 43, 226, 0.2);
  border-radius: 4px;
  color: #8a2be2;
  font-size: 12px;
  text-transform: uppercase;
  letter-spacing: 1px;
  margin-bottom: 8px;
  font-weight: 500;
}

.post-text {
  margin: 0 0 15px;
  font-size: 18px;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  color: #333333;
  flex-grow: 1;
  font-weight: 500;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.05);
}

.post-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: auto;
  padding-top: 10px;
  border-top: 1px solid rgba(5, 217, 232, 0.05);
  position: relative;
}

.post-info::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(5, 217, 232, 0.1), transparent);
}

.user-info {
  display: flex;
  align-items: center;
}

.user-avatar {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  margin-right: 8px;
  object-fit: cover;
  border: 1px solid rgba(5, 217, 232, 0.2);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.username {
  font-size: 14px;
  color: #666666;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100px;
  font-weight: 500;
}

.post-stats {
  display: flex;
  align-items: center;
  color: #666666;
  font-size: 12px;
}

.like-count {
  display: flex;
  align-items: center;
  gap: 4px;
  color: #666666;
  font-size: 12px;
  position: relative;
  font-weight: 500;
}

.card-border {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 2px;
  background: linear-gradient(90deg, #8a2be2, #05d9e8);
  transform: scaleX(0);
  transform-origin: left;
  transition: transform 0.3s ease;
}

.post-card:hover .card-border {
  transform: scaleX(1);
}

.heart-icon {
  display: inline-block;
  width: 14px;
  height: 12px;
  margin-right: 4px;
  position: relative;
}

.heart-icon:before,
.heart-icon:after {
  content: "";
  position: absolute;
  top: 0;
  width: 7px;
  height: 12px;
  border-radius: 7px 7px 0 0;
  background: #ff2a6d;
  box-shadow: 0 0 5px rgba(255, 42, 109, 0.7);
}

.heart-icon:before {
  left: 0;
  transform: rotate(-45deg);
  transform-origin: 100% 100%;
}

.heart-icon:after {
  left: 7px;
  transform: rotate(45deg);
  transform-origin: 0 100%;
}

.post-card:hover .heart-icon:before,
.post-card:hover .heart-icon:after {
  animation: heartbeat 1.5s infinite;
}

.no-posts {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 0;
  color: #666666;
  text-align: center;
}

.no-data-icon {
  width: 60px;
  height: 60px;
  border: 2px solid rgba(5, 217, 232, 0.3);
  border-radius: 50%;
  margin-bottom: 20px;
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: rgba(255, 255, 255, 0.8);
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);
}

.no-data-icon::before {
  content: '!';
  font-size: 30px;
  color: #05d9e8;
  text-shadow: 1px 1px 3px rgba(5, 217, 232, 0.3);
  font-weight: bold;
}

.no-data-icon::after {
  content: '';
  position: absolute;
  top: -5px;
  left: -5px;
  right: -5px;
  bottom: -5px;
  border: 1px solid rgba(5, 217, 232, 0.1);
  border-radius: 50%;
  animation: pulseRing 2s infinite;
}

.no-data-line {
  width: 100px;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(5, 217, 232, 0.3), transparent);
  margin-top: 20px;
}

/* Animations */
@keyframes overlayPulse {
  0% {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.8) 0%, rgba(240, 240, 240, 0.7) 100%);
  }
  100% {
    background: linear-gradient(135deg, rgba(248, 249, 250, 0.7) 0%, rgba(233, 236, 239, 0.6) 100%);
  }
}

@keyframes backgroundScale {
  0% {
    transform: scale(1);
    filter: brightness(0.9) saturate(1.1) hue-rotate(0deg);
  }
  100% {
    transform: scale(1.05);
    filter: brightness(0.95) saturate(1.2) hue-rotate(0deg);
  }
}

@keyframes gridPulse {
  0% {
    opacity: 0.2;
    background-size: 40px 40px;
  }
  100% {
    opacity: 0.3;
    background-size: 42px 42px;
  }
}

@keyframes glitchSweep {
  0%, 100% {
    left: -100%;
    opacity: 0;
  }
  10%, 90% {
    left: -100%;
    opacity: 0;
  }
  50% {
    left: 100%;
    opacity: 1;
  }
}

@keyframes borderGlow {
  0% {
    opacity: 0.3;
  }
  100% {
    opacity: 0.8;
  }
}

@keyframes avatarGlow {
  0% {
    box-shadow: 0 0 10px rgba(138, 43, 226, 0.4);
  }
  100% {
    box-shadow: 0 0 20px rgba(138, 43, 226, 0.7);
  }
}

@keyframes scanEffect {
  0% {
    transform: translateY(-100%);
  }
  100% {
    transform: translateY(100%);
  }
}

@keyframes barGlow {
  0% {
    opacity: 0.3;
  }
  100% {
    opacity: 0.7;
  }
}

@keyframes phoneSweep {
  0%, 100% {
    left: -100%;
    opacity: 0;
  }
  50% {
    left: 100%;
    opacity: 1;
  }
}

@keyframes phoneGlow {
  0% {
    box-shadow: 0 0 5px rgba(255, 42, 109, 0.5);
  }
  100% {
    box-shadow: 0 0 15px rgba(255, 42, 109, 0.9);
  }
}

@keyframes holoFloat {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-10px); }
}

@keyframes holoPulse {
  0%, 100% {
    opacity: 0.5;
    box-shadow: 0 0 5px rgba(5, 217, 232, 0.3);
  }
  50% {
    opacity: 0.8;
    box-shadow: 0 0 15px rgba(5, 217, 232, 0.7);
  }
}

@keyframes imageGlitch {
  0%, 100% {
    opacity: 0;
    transform: translateX(0);
  }
  10%, 90% {
    opacity: 0;
  }
  50%, 51% {
    opacity: 0.5;
    transform: translateX(2px);
  }
  52%, 53% {
    transform: translateX(-2px);
  }
}

@keyframes heartbeat {
  0%, 100% {
    transform-origin: center;
    transform: scale(1);
  }
  50% {
    transform: scale(1.2);
  }
}

@keyframes pulseRing {
  0% {
    transform: scale(0.8);
    opacity: 0.3;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.6;
  }
  100% {
    transform: scale(0.8);
    opacity: 0.3;
  }
}

/* Element Plus overrides */
:deep(.el-input__inner), :deep(.el-textarea__inner) {
  background-color: rgba(255, 255, 255, 0.9);
  border-color: rgba(5, 217, 232, 0.3);
  color: #333333;
  transition: all 0.3s ease;
}

:deep(.el-input__inner:focus), :deep(.el-textarea__inner:focus) {
  border-color: rgba(5, 217, 232, 0.7);
  box-shadow: 0 0 10px rgba(5, 217, 232, 0.3);
}

:deep(.el-input__inner:hover), :deep(.el-textarea__inner:hover) {
  border-color: rgba(5, 217, 232, 0.5);
}

:deep(.el-form-item__label) {
  color: #333333;
  font-size: 14px;
  text-transform: uppercase;
  letter-spacing: 1px;
}

:deep(.el-button--primary) {
  background: linear-gradient(90deg, #8a2be2, #05d9e8);
  border: none;
  position: relative;
  overflow: hidden;
}

:deep(.el-button--primary:hover) {
  background: linear-gradient(90deg, #9b4dff, #05f7ff);
  box-shadow: 0 0 15px rgba(5, 217, 232, 0.5);
}

:deep(.el-button--primary::before) {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 50%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  animation: buttonSweep 3s infinite;
}

:deep(.el-tabs__item) {
  color: rgba(51, 51, 51, 0.7);
  font-size: 16px;
  transition: all 0.3s ease;
  padding: 0 20px;
  position: relative;
}

:deep(.el-tabs__item:hover) {
  color: rgba(51, 51, 51, 0.9);
}

:deep(.el-tabs__item.is-active) {
  color: #05d9e8;
  text-shadow: 0 0 10px rgba(5, 217, 232, 0.5);
}

:deep(.el-tabs__active-bar) {
  background: linear-gradient(90deg, #8a2be2, #05d9e8);
  height: 3px;
}

:deep(.el-tabs__nav-wrap::after) {
  /* Remove the white line under tabs */
  display: none;
}

:deep(.el-tabs__content) {
  /* Add shadow to tab content */
  box-shadow: 0 0 20px rgba(5, 217, 232, 0.1);
  border-radius: 0 0 8px 8px;
  padding: 20px;
  background-color: rgba(255, 255, 255, 0.95);
  border: 1px solid rgba(5, 217, 232, 0.2);
  border-top: none;
}

/* Add shadow to distinguish between sections */
.user-content-tabs .el-tab-pane {
  box-shadow: 0 0 20px rgba(5, 217, 232, 0.1);
}

@keyframes buttonSweep {
  0%, 100% {
    left: -100%;
    opacity: 0;
  }
  50% {
    left: 100%;
    opacity: 1;
  }
}

/* Responsive styles */
@media (max-width: 992px) {
  .profile-content {
    flex-direction: column;
  }

  .profile-sidebar {
    width: 100%;
  }
}

@media (max-width: 768px) {
  .profile-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
  }

  .posts-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 576px) {
  .posts-grid {
    grid-template-columns: 1fr;
  }
}
</style>
