<template>
  <div class="enhanced-home-container">
    <!-- 页面指示器 -->
    <div class="page-indicator">
      <div
        v-for="i in 3"
        :key="i"
        :class="['indicator-dot', { active: currentSection === i }]"
        @click="scrollToSection(i)"
      ></div>
    </div>

    <!-- 第一部分：全屏赛博朋克风格背景 -->
    <section id="section1" class="home-section" ref="section1">
      <div class="cyberpunk-container">
        <div class="cyberpunk-overlay"></div>
        <div class="cyberpunk-background"></div>
        <div class="cyberpunk-grid"></div>
        <div class="cyberpunk-scanline"></div>
        <div class="cyberpunk-glitch-effect"></div>


        <div class="cyberpunk-content">
          <div class="slogan-container">
            <div class="chinese-slogan" :data-text="cyberpunkData.slogan.chinese">{{ cyberpunkData.slogan.chinese }}</div>
            <div class="english-slogan">{{ cyberpunkData.slogan.english }}</div>
          </div>
          <div class="cyberpunk-elements">
            <div class="data-circle">
              <div class="circle-inner">
                <div class="circle-number">{{ cyberpunkData.stats.circleValue }}</div>
                <svg class="circle-progress" width="120" height="120">
                  <circle cx="60" cy="60" r="54" stroke="#8a2be2" stroke-width="4" fill="none" stroke-dasharray="339.3" stroke-dashoffset="100" />
                  <!-- Removed additional circles for better performance -->
                </svg>
              </div>
            </div>
            <div class="data-stats">
              <div class="stat-item">
                <div class="stat-label">AI POWER</div>
                <div class="stat-value">{{ cyberpunkData.stats.aiPower }}<span class="percent">%</span></div>
              </div>
              <div class="stat-item">
                <div class="stat-label">EFFICIENCY</div>
                <div class="stat-value">{{ cyberpunkData.stats.efficiency }}<span class="unit">ms</span></div>
              </div>
            </div>
          </div>
        </div>
        <div class="scroll-down" @click="scrollToSection(2)">
          <i class="el-icon-arrow-down"></i>
        </div>
      </div>
    </section>

    <!-- 第二部分：粒子背景和新闻轮播 -->
    <section id="section2" class="home-section" ref="section2">
      <div class="main-content">
        <!-- 左侧视觉区域 -->
        <div class="visual-area">
          <ParticleBackground>
            <div class="center-content">
              <div class="animated-text">
                <div class="chinese-text" data-text="实验室门户网站">实验室门户网站</div>
                <div class="english-text">Laboratory Portal Website</div>
              </div>
              <div class="animated-elements">
                <div class="element" v-for="(item, index) in animatedElements" :key="index">
                  <div class="element-label">{{ item.label }}</div>
                  <div class="element-circle">
                    <div class="circle-content">
                      <div class="circle-value">{{ item.value }}</div>
                      <div class="circle-unit">{{ item.unit }}</div>
                    </div>
                    <svg class="circle-svg" width="80" height="80">
                      <circle class="circle-bg" cx="40" cy="40" r="36" />
                      <circle class="circle-progress" cx="40" cy="40" r="36"
                        :style="{ strokeDashoffset: 226 - (226 * item.progress / 100) }" />
                    </svg>
                  </div>
                </div>
              </div>

              <!-- Add a floating holographic element -->
              <div class="holographic-container">
                <div class="holo-element">
                  <div class="holo-lines"></div>
                  <div class="holo-circle"></div>
                  <div class="holo-data">DATA</div>
                </div>
              </div>

              <!-- Add a data visualization element -->
              <div class="data-viz-container">
                <div class="data-viz-title">SYSTEM STATUS</div>
                <div class="data-viz-bars">
                  <div class="data-bar" v-for="(value, index) in [75, 45, 90, 60, 30]" :key="index"
                       :style="{ height: value + '%', animationDelay: index * 0.2 + 's' }">
                    <div class="data-bar-highlight"></div>
                  </div>
                </div>
                <div class="data-viz-line"></div>
              </div>
            </div>
          </ParticleBackground>
        </div>

        <!-- 右侧信息区域 -->
        <div class="info-area">
          <NewsSwiper
            :tabs="tabs"
            :tabs-content="tabsContent"
            @more="handleViewMore"
            @viewDetail="handleViewNewsDetail"
          />
        </div>
      </div>
    </section>

    <!-- 第三部分：研究领域展示 -->
    <section id="section3" class="home-section" ref="section3">
      <div class="research-content">
        <div class="section-header">
          <h2>研究领域</h2>
          <div class="section-subtitle">RESEARCH AREAS</div>
        </div>

        <div class="research-areas">
          <div
            class="research-area"
            v-for="(area, index) in researchAreas"
            :key="index"
            @click="navigateToResearchArea(area)"
          >
            <div class="research-icon">
              <i :class="area.icon"></i>
            </div>
            <div class="research-title">{{ area.title }}</div>
            <div class="research-description">{{ area.description }}</div>
            <div class="research-more">
              <span>了解详情</span>
              <i class="el-icon-arrow-right"></i>
            </div>
          </div>
        </div>

        <div class="section-button" @click="navigateTo('/science')">
          查看所有研究方向
        </div>
      </div>
    </section>
  </div>
</template>

<script setup>
import { ref, onMounted, computed, onUnmounted } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { Search, Menu, ArrowDown } from '@element-plus/icons-vue';
import { Swiper, SwiperSlide } from 'swiper/vue';
import { Pagination, EffectFade, Autoplay } from 'swiper/modules';
import 'swiper/css';
import 'swiper/css/pagination';
import 'swiper/css/effect-fade';
import ParticleBackground from '../components/ParticleBackground.vue';
import NewsSwiper from '../components/NewsSwiper.vue';
import { getPosts } from '../api/post';
import { ElMessage } from 'element-plus';

const router = useRouter();
const route = useRoute();
const currentRoute = computed(() => route.path);
const tabs = ['新闻动态', '通知公告', '学术动态'];

// 页面滚动相关
const section1 = ref(null);
const section2 = ref(null);
const section3 = ref(null);
const currentSection = ref(1);
const isScrolling = ref(false);

// 赛博朋克风格背景数据
const cyberpunkData = {
  slogan: {
    chinese: '实验室门户网站',
    english: 'Laboratory Portal Website'
  },
  stats: {
    circleValue: 14,
    aiPower: 31.3,
    efficiency: 55
  }
};

// 研究领域数据
const researchAreas = [
  {
    icon: 'el-icon-cpu',
    title: '机器学习与深度学习',
    description: '研究先进的机器学习算法和深度学习模型，解决复杂场景下的智能决策问题。'
  },
  {
    icon: 'el-icon-data-analysis',
    title: '大数据分析与挖掘',
    description: '开发高效的大数据处理技术，从海量数据中提取有价值的信息和知识。'
  },
  {
    icon: 'el-icon-monitor',
    title: '计算机视觉',
    description: '研究图像识别、目标检测、场景理解等视觉智能技术，赋能智能监控、自动驾驶等领域。'
  },
  {
    icon: 'el-icon-chat-dot-round',
    title: '自然语言处理',
    description: '探索语言理解与生成技术，推动智能对话、机器翻译、文本分析等应用发展。'
  },
  {
    icon: 'el-icon-connection',
    title: '知识图谱与推理',
    description: '构建结构化知识体系，实现智能推理与决策，支持智能问答和知识服务。'
  },
  {
    icon: 'el-icon-mobile',
    title: '智能交互技术',
    description: '研究人机交互新模式，打造更自然、高效、智能的交互体验。'
  }
];

// Navigation functions
const navigateTo = (path) => {
  // Only navigate if we're not already on this path
  if (route.path !== path) {
    router.push(path).catch(err => {
      if (err.name !== 'NavigationDuplicated') {
        console.error('Navigation error:', err);
      }
    });
  }
};

// Navigate to specific research area
const navigateToResearchArea = (area) => {
  // Encode the research area title to use as a query parameter
  const areaId = encodeURIComponent(area.title);

  // Navigate to the science page with the area title as a parameter
  router.push({
    path: '/science',
    query: { area: areaId }
  });
};

// 滚动到指定部分
const scrollToSection = (sectionNumber) => {
  if (isScrolling.value) return;

  isScrolling.value = true;
  currentSection.value = sectionNumber;

  let targetSection;
  switch(sectionNumber) {
    case 1:
      targetSection = section1.value;
      break;
    case 2:
      targetSection = section2.value;
      break;
    case 3:
      targetSection = section3.value;
      break;
  }

  if (targetSection) {
    // Use requestAnimationFrame for smoother scrolling
    const targetTop = targetSection.offsetTop;
    const startPosition = window.scrollY;
    const distance = targetTop - startPosition;
    const duration = 800; // ms
    let startTime = null;

    const animateScroll = (currentTime) => {
      if (!startTime) startTime = currentTime;
      const timeElapsed = currentTime - startTime;
      const progress = Math.min(timeElapsed / duration, 1);
      const easeInOutCubic = progress < 0.5
        ? 4 * progress * progress * progress
        : 1 - Math.pow(-2 * progress + 2, 3) / 2;

      window.scrollTo(0, startPosition + distance * easeInOutCubic);

      if (timeElapsed < duration) {
        requestAnimationFrame(animateScroll);
      } else {
        // Scroll complete
        isScrolling.value = false;
      }
    };

    requestAnimationFrame(animateScroll);
  }
};

// 使用节流函数优化滚动事件处理
const throttle = (fn, delay) => {
  let lastCall = 0;
  return function(...args) {
    const now = new Date().getTime();
    if (now - lastCall < delay) {
      return;
    }
    lastCall = now;
    return fn(...args);
  };
};

// 监听滚动事件，更新当前部分 - 增加节流时间以提高性能
const handleScroll = throttle(() => {
  if (isScrolling.value) return;

  const scrollPosition = window.scrollY;
  const windowHeight = window.innerHeight;

  // 计算每个部分的位置 - 缓存这些值以提高性能
  const section1Top = section1.value?.offsetTop || 0;
  const section2Top = section2.value?.offsetTop || 0;
  const section3Top = section3.value?.offsetTop || 0;

  // 确定当前部分
  if (scrollPosition < section2Top - windowHeight / 3) {
    currentSection.value = 1;
  } else if (scrollPosition < section3Top - windowHeight / 3) {
    currentSection.value = 2;
  } else {
    currentSection.value = 3;
  }
}, 200); // 增加到200ms以减少处理频率



// 实际数据
const newsItems = ref([]);
const noticeItems = ref([]);
const academicItems = ref([]);
const tabsContent = computed(() => [newsItems.value,noticeItems.value,academicItems.value]);

// 格式化日期
const formatDate = (dateStr) => {
  if (!dateStr) return '';
  const date = new Date(dateStr);
  return date.toLocaleDateString('zh-CN').replace(/\//g, '/');
};

// 默认数据 - 用于快速显示
const defaultNewsItems = [
  { date: '2021/09/30', title: '人工智能教育部重点实验室建设项目验收会召开' },
  { date: '2021/07/14', title: 'AI时代数据开放共享创新论坛顺利举行' }
];

const defaultNoticeItems = [
  { date: '2024/06/15', title: '关于2024年暑期研究院开放日活动安排的通知' },
  { date: '2024/05/20', title: '2024年度研究院研究生招生面试通知' }
];

const defaultAcademicItems = [
  { date: '2024/06/10', title: '研究院最新研究成果在国际顶级期刊Nature发表' },
  { date: '2024/05/15', title: '研究院主任受邀在ICML 2024作特邀报告' }
];

// 初始化默认数据以提高初始加载速度
newsItems.value = [...defaultNewsItems];
noticeItems.value = [...defaultNoticeItems];
academicItems.value = [...defaultAcademicItems];

// 加载新闻数据 - 优化为顺序加载而非并行加载，减少同时请求数
const loadNewsData = async () => {
  try {
    // 先加载新闻动态 (category=0)
    const newsResponse = await getPosts(0);
    if (newsResponse.code === 200 && newsResponse.data) {
      newsItems.value = newsResponse.data.postVOList.map(post => ({
        id: post.id,
        date: formatDate(post.createdAt),
        title: post.title,
        // 不加载完整内容以提高性能
        coverUrl: post.coverUrl
      }));
    }

    // 然后加载通知公告 (category=1)
    const noticeResponse = await getPosts(1);
    if (noticeResponse.code === 200 && noticeResponse.data) {
      noticeItems.value = noticeResponse.data.postVOList.map(post => ({
        id: post.id,
        date: formatDate(post.createdAt),
        title: post.title,
        // 不加载完整内容以提高性能
        coverUrl: post.coverUrl
      }));
    }

    // 最后加载学术动态 (category=2)
    const academicResponse = await getPosts(2);
    if (academicResponse.code === 200 && academicResponse.data) {
      academicItems.value = academicResponse.data.postVOList.map(post => ({
        id: post.id,
        date: formatDate(post.createdAt),
        title: post.title,
        // 不加载完整内容以提高性能
        coverUrl: post.coverUrl
      }));
    }
  } catch (error) {
    console.error('Failed to load news data:', error);
    ElMessage.error('加载新闻数据失败，请稍后重试');
    // 已经有默认数据，不需要再次设置
  }
};

// 动画元素 - 添加数据可视化
const animatedElements = [
  {
    label: 'AI Power',
    value: '87',
    unit: '%',
    progress: 87
  },
  {
    label: 'Data Flow',
    value: '64',
    unit: 'TB',
    progress: 64
  },
  {
    label: 'Efficiency',
    value: '92',
    unit: '%',
    progress: 92
  }
];

// 处理"了解更多"按钮点击
const handleViewMore = (tabIndex) => {
  console.log('View more for tab:', tabs[tabIndex]);
  // 根据不同的标签页跳转到不同的页面
  switch(tabIndex) {
    case 0: // 新闻动态
      router.push('/news?type=news');
      break;
    case 1: // 通知公告
      router.push('/news?type=notice');
      break;
    case 2: // 学术动态
      router.push('/news?type=academic');
      break;
    default:
      router.push('/news');
  }
};

// 处理点击新闻项
const handleViewNewsDetail = ({ item, type }) => {
  console.log('View news detail:', item, 'type:', type);

  // 使用真实的帖子ID
  const postId = item.id;
  let newsType = type;

  // 根据类型设置查询参数
  router.push({
    path: '/news',
    query: {
      id: postId,
      type: newsType
    }
  });
};

// 生命周期钩子
onMounted(() => {
  window.addEventListener('scroll', handleScroll);
  // 初始滚动到顶部
  window.scrollTo(0, 0);

  // 使用setTimeout延迟加载数据，让UI先渲染完成
  setTimeout(() => {
    loadNewsData();
  }, 500);
});

onUnmounted(() => {
  window.removeEventListener('scroll', handleScroll);
});
</script>

<style scoped>
.enhanced-home-container {
  width: 100%;
  background-color: #f8f9fa;
  color: #333333;
  position: relative;
  overflow-x: hidden;
  scroll-behavior: smooth;
  font-family: 'Helvetica Neue', Arial, sans-serif;
}

/* Hide scrollbar for Chrome, Safari and Opera */
.enhanced-home-container::-webkit-scrollbar {
  display: none;
}

/* Hide scrollbar for IE, Edge and Firefox */
.enhanced-home-container {
  -ms-overflow-style: none;  /* IE and Edge */
  scrollbar-width: none;  /* Firefox */
}

/* 通用部分样式 */
.home-section {
  width: 100%;
  height: 100vh;
  position: relative;
  overflow: hidden;
}

/* 页面指示器 */
.page-indicator {
  position: fixed;
  right: 40px;
  top: 50%;
  transform: translateY(-50%);
  display: flex;
  flex-direction: column;
  gap: 15px;
  z-index: 100;
}

.indicator-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: #cccccc;
  transition: all 0.3s;
  cursor: pointer;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.indicator-dot.active {
  background-color: #333333;
  height: 20px;
  border-radius: 10px;
}

/* 第一部分：赛博朋克风格 */
.cyberpunk-container {
  width: 100%;
  height: 100%;
  position: relative;
  overflow: hidden;
}



.cyberpunk-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.8) 0%, rgba(245, 245, 250, 0.7) 100%);
  z-index: 2;
  /* Removed animation for better performance */
}

.cyberpunk-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: url('https://images.unsplash.com/photo-1483478550801-ceba5fe50e8e?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80');
  background-size: cover;
  background-position: center;
  filter: brightness(1.3) saturate(0.8) hue-rotate(5deg);
  z-index: 1;
}

.cyberpunk-grid {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  /* Simplified grid with fewer layers */
  background-image:
    linear-gradient(rgba(0, 162, 255, 0.15) 1px, transparent 1px),
    linear-gradient(90deg, rgba(0, 162, 255, 0.15) 1px, transparent 1px);
  background-size: 40px 40px;
  z-index: 3;
  /* Removed animation for better performance */
}

.cyberpunk-scanline {
  /* Removed for better performance */
  display: none;
}

@keyframes gridPulse {
  0% {
    opacity: 0.3;
    background-size: 40px 40px, 40px 40px, 200px 200px, 200px 200px;
  }
  50% {
    opacity: 0.6;
    background-size: 42px 42px, 42px 42px, 210px 210px, 210px 210px;
  }
  100% {
    opacity: 0.3;
    background-size: 40px 40px, 40px 40px, 200px 200px, 200px 200px;
  }
}

@keyframes overlayPulse {
  0% {
    background: linear-gradient(135deg, rgba(13, 17, 23, 0.6) 0%, rgba(13, 17, 23, 0.4) 100%);
  }
  100% {
    background: linear-gradient(135deg, rgba(13, 17, 23, 0.5) 0%, rgba(13, 17, 23, 0.3) 100%);
  }
}

@keyframes backgroundScale {
  0% {
    transform: scale(1.05);
    filter: brightness(0.8) saturate(1.8) hue-rotate(-10deg);
  }
  100% {
    transform: scale(1.1);
    filter: brightness(0.9) saturate(2) hue-rotate(0deg);
  }
}

.cyberpunk-glitch-effect {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: transparent;
  z-index: 5;
  pointer-events: none;
  overflow: hidden;
}

.cyberpunk-glitch-effect::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 42, 109, 0.1), transparent);
  animation: glitchSweep 10s infinite;
}

@keyframes glitchSweep {
  0%, 100% {
    left: -100%;
    opacity: 0;
  }
  10%, 90% {
    left: -100%;
    opacity: 0;
  }
  50% {
    left: 100%;
    opacity: 1;
  }
}

.cyberpunk-glitch-effect {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: transparent;
  z-index: 5;
  pointer-events: none;
  overflow: hidden;
}

.cyberpunk-glitch-effect::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 42, 109, 0.2), transparent);
  animation: glitchSweep 10s infinite;
}

@keyframes glitchSweep {
  0%, 100% {
    left: -100%;
    opacity: 0;
  }
  10%, 90% {
    left: -100%;
    opacity: 0;
  }
  50% {
    left: 100%;
    opacity: 1;
  }
}

.cyberpunk-content {
  position: relative;
  z-index: 4;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  width: 100%;
  padding: 0 20px;
}

.slogan-container {
  text-align: center;
  margin-bottom: 60px;
}

.chinese-slogan {
  font-size: 4.5rem;
  font-weight: bold;
  margin-bottom: 15px;
  color: #333333;
  text-shadow: 2px 2px 4px rgba(138, 43, 226, 0.2);
  letter-spacing: 6px;
  /* Removed animation for better performance */
  position: relative;
}

/* Removed ::after pseudo-element for better performance */

.english-slogan {
  font-size: 2.2rem;
  text-transform: uppercase;
  letter-spacing: 3px;
  color: #555555;
  text-shadow: 1px 1px 3px rgba(0, 162, 255, 0.2);
  /* Removed animation for better performance */
  position: relative;
  margin-top: 5px;
}

@keyframes textGlow {
  0% {
    text-shadow: 0 0 15px rgba(138, 43, 226, 0.8), 0 0 10px rgba(0, 162, 255, 0.6);
  }
  100% {
    text-shadow: 0 0 20px rgba(138, 43, 226, 1), 0 0 30px rgba(0, 162, 255, 0.8), 0 0 40px rgba(255, 42, 109, 0.4);
  }
}

@keyframes textGlitchEffect {
  0%, 100% {
    opacity: 0;
    transform: translateX(0);
  }
  10%, 90% {
    opacity: 0;
  }
  50%, 51% {
    opacity: 0.7;
    transform: translateX(2px);
  }
  52%, 53% {
    transform: translateX(-2px);
  }
}

.cyberpunk-elements {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 80px;
  margin-top: 40px;
}

.data-circle {
  position: relative;
  width: 120px;
  height: 120px;
}

.circle-inner {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: rgba(255, 255, 255, 0.95);
  border-radius: 50%;
  box-shadow: 0 6px 20px rgba(138, 43, 226, 0.15);
  border: 1px solid rgba(138, 43, 226, 0.15);
}

.circle-number {
  font-size: 3rem;
  font-weight: bold;
  color: #ff2a6d;
  text-shadow: 1px 1px 2px rgba(255, 42, 109, 0.3);
}

.circle-progress {
  position: absolute;
  top: 0;
  left: 0;
  transform: rotate(-90deg);
  /* Removed filter for better performance */
  /* Removed animation for better performance */
}

/* Removed circle animation for better performance */

/* Removed keyframes for better performance */

.data-stats {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.stat-item {
  background-color: rgba(255, 255, 255, 0.95);
  padding: 12px 20px;
  border-left: 3px solid #05d9e8;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.06);
  position: relative;
  overflow: hidden;
  border-radius: 6px;
  margin-bottom: 12px;
}

/* Removed :after pseudo-element and animation for better performance */

.stat-label {
  font-size: 1.2rem;
  color: #05d9e8;
  margin-bottom: 6px;
  font-weight: 600;
  letter-spacing: 1px;
}

.stat-value {
  font-size: 2.2rem;
  font-weight: bold;
  color: #333333;
}

.percent, .unit {
  font-size: 1.2rem;
  margin-left: 2px;
  color: #ff2a6d;
}

.scroll-down {
  position: absolute;
  bottom: 30px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 10;
  width: 45px;
  height: 45px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.95);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(5, 217, 232, 0.3);
  transition: all 0.3s ease;
  /* Removed animation for better performance */
}

.scroll-down i {
  font-size: 22px;
  color: #333333;
}

.scroll-down:hover {
  transform: translateX(-50%) translateY(-3px);
  box-shadow: 0 6px 20px rgba(5, 217, 232, 0.15);
}

/* Removed keyframes for better performance */

/* 第二部分：粒子背景和新闻轮播 */
.main-content {
  display: flex;
  height: 100vh;
  width: 100%;
}

/* 左侧视觉区域 */
.visual-area {
  flex: 2;
  position: relative;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  background: radial-gradient(circle at center, rgba(255, 255, 255, 0.95) 0%, rgba(245, 245, 250, 0.9) 70%);
  box-shadow: inset 0 0 20px rgba(0, 0, 0, 0.03);
}

.visual-area::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background:
    linear-gradient(90deg, rgba(5, 217, 232, 0.08) 1px, transparent 1px),
    linear-gradient(rgba(5, 217, 232, 0.08) 1px, transparent 1px);
  background-size: 20px 20px;
  z-index: 0;
}

.visual-area::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(138, 43, 226, 0.05) 0%, rgba(5, 217, 232, 0.05) 100%);
  z-index: 0;
}

.center-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  width: 100%;
  padding: 0 20px;
  position: relative;
  z-index: 1;
}

.animated-text {
  text-align: center;
  margin-bottom: 80px;
}

.chinese-text {
  font-size: 46px;
  font-weight: bold;
  margin-bottom: 12px;
  color: #333333;
  text-shadow: 1px 1px 2px rgba(138, 43, 226, 0.2);
  position: relative;
  animation: textPulse 3s infinite alternate;
  letter-spacing: 2px;
}

@keyframes textPulse {
  0% {
    text-shadow: 1px 1px 2px rgba(138, 43, 226, 0.2);
  }
  100% {
    text-shadow: 1px 1px 3px rgba(138, 43, 226, 0.3), 1px 1px 4px rgba(5, 217, 232, 0.2);
  }
}

.chinese-text::after {
  content: attr(data-text);
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
  color: transparent;
  text-shadow: 1px 1px 2px rgba(255, 42, 109, 0.2);
  opacity: 0;
  animation: textGlitch 8s infinite;
}

@keyframes textGlitch {
  0%, 90%, 100% {
    opacity: 0;
    transform: translateX(0);
  }
  92%, 94%, 96% {
    opacity: 0.5;
    transform: translateX(2px);
  }
  93%, 95%, 97% {
    opacity: 0.5;
    transform: translateX(-2px);
  }
}

.english-text {
  font-size: 18px;
  color: #555555;
  opacity: 0.9;
  text-shadow: 1px 1px 2px rgba(0, 162, 255, 0.2);
  letter-spacing: 3px;
  position: relative;
  overflow: hidden;
  font-weight: 500;
  margin-top: 5px;
}

.english-text::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: -100%;
  width: 100%;
  height: 1px;
  background: linear-gradient(90deg, transparent, #05d9e8, transparent);
  animation: lineScan 4s infinite;
}

@keyframes lineScan {
  0%, 100% {
    left: -100%;
  }
  50% {
    left: 100%;
  }
}

.animated-elements {
  display: flex;
  gap: 40px;
  flex-wrap: wrap;
  justify-content: center;
  margin-top: 40px;
  margin-bottom: 20px;
}

.element {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 15px;
  position: relative;
  transition: transform 0.3s ease;
}

.element:hover {
  transform: translateY(-5px);
}

.element-label {
  font-size: 18px;
  color: #05d9e8;
  background-color: rgba(5, 217, 232, 0.1);
  padding: 6px 12px;
  border-radius: 20px;
  position: relative;
  overflow: hidden;
  font-weight: 500;
  box-shadow: 0 2px 8px rgba(5, 217, 232, 0.1);
}

.element-label::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 50%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(5, 217, 232, 0.3), transparent);
  animation: labelScan 3s infinite;
  animation-delay: calc(var(--index, 0) * 1s);
}

@keyframes labelScan {
  0%, 100% { left: -50%; }
  50% { left: 100%; }
}

.element-circle {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.circle-content {
  position: absolute;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 2;
}

.circle-value {
  font-size: 18px;
  font-weight: bold;
  color: #333333;
}

.circle-unit {
  font-size: 12px;
  color: #05d9e8;
  font-weight: 500;
}

.circle-svg {
  position: absolute;
  top: 0;
  left: 0;
  transform: rotate(-90deg);
}

.circle-bg {
  fill: none;
  stroke: rgba(200, 200, 200, 0.2);
  stroke-width: 3;
}

.circle-progress {
  fill: none;
  stroke: #05d9e8;
  stroke-width: 3;
  stroke-linecap: round;
  stroke-dasharray: 226;
  transition: stroke-dashoffset 1s ease;
  filter: drop-shadow(0 0 2px rgba(5, 217, 232, 0.5));
}

/* Holographic element */
.holographic-container {
  position: absolute;
  bottom: 40px;
  right: 40px;
  width: 120px;
  height: 120px;
}

.holo-element {
  position: relative;
  width: 100%;
  height: 100%;
  animation: holoFloat 4s ease-in-out infinite;
}

@keyframes holoFloat {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-10px); }
}

.holo-lines {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border: 1px solid rgba(5, 217, 232, 0.5);
  border-radius: 50%;
}

.holo-lines::before, .holo-lines::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 0;
  width: 100%;
  height: 1px;
  background: rgba(5, 217, 232, 0.5);
}

.holo-lines::after {
  transform: rotate(90deg);
}

.holo-circle {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 60%;
  height: 60%;
  border-radius: 50%;
  border: 1px solid rgba(5, 217, 232, 0.8);
  box-shadow: 0 0 10px rgba(5, 217, 232, 0.5);
  animation: holoPulse 2s ease-in-out infinite;
}

@keyframes holoPulse {
  0%, 100% { opacity: 0.5; transform: translate(-50%, -50%) scale(1); }
  50% { opacity: 0.8; transform: translate(-50%, -50%) scale(1.1); }
}

.holo-data {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 14px;
  font-weight: bold;
  color: #05d9e8;
  letter-spacing: 2px;
}

/* Data visualization element */
.data-viz-container {
  position: absolute;
  bottom: 40px;
  left: 40px;
  width: 180px;
  height: 120px;
  border: 1px solid rgba(5, 217, 232, 0.3);
  padding: 10px;
  background-color: rgba(255, 255, 255, 0.9);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
  border-radius: 8px;
}

.data-viz-title {
  font-size: 13px;
  color: #05d9e8;
  margin-bottom: 10px;
  letter-spacing: 1px;
  font-weight: 600;
}

.data-viz-bars {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  height: 80px;
  position: relative;
}

.data-bar {
  width: 8px;
  background-color: rgba(5, 217, 232, 0.3);
  position: relative;
  animation: barPulse 2s ease-in-out infinite;
  border-radius: 4px;
}

@keyframes barPulse {
  0%, 100% {
    opacity: 0.7;
  }
  50% {
    opacity: 1;
  }
}

.data-bar-highlight {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 3px;
  background-color: #05d9e8;
  box-shadow: 0 0 5px #05d9e8;
}

.data-viz-line {
  height: 1px;
  width: 100%;
  background: linear-gradient(90deg, transparent, #05d9e8, transparent);
  margin-top: 5px;
  position: relative;
  animation: linePulse 2s infinite;
}

@keyframes linePulse {
  0%, 100% {
    opacity: 0.5;
  }
  50% {
    opacity: 1;
  }
}

/* 右侧信息区域 */
.info-area {
  width: 450px;
  padding: 40px 40px 20px 40px;
  display: flex;
  flex-direction: column;
  background: linear-gradient(to right, rgba(245, 245, 250, 0.9), rgba(255, 255, 255, 0.85));
  background-image:
    linear-gradient(90deg, rgba(5, 217, 232, 0.08) 1px, transparent 1px),
    linear-gradient(rgba(5, 217, 232, 0.08) 1px, transparent 1px);
  background-size: 20px 20px;
  border-left: 1px solid rgba(5, 217, 232, 0.1);
  overflow-y: auto;
  position: relative;
  box-shadow: -5px 0 20px rgba(0, 0, 0, 0.05);
  z-index: 5;
}

/* Removed ::before pseudo-element for better performance */

/* Removed ::after pseudo-element for better performance */

/* 第三部分：研究领域 */
.research-content {
  width: 100%;
  height: 100%;
  padding: 80px 40px;
  display: flex;
  flex-direction: column;
  align-items: center;
  background-color: #f8f9fa;
  position: relative;
  overflow: hidden;
  border-top: 1px solid rgba(0, 0, 0, 0.05);
  box-shadow: inset 0 5px 15px rgba(0, 0, 0, 0.03);
}

/* Removed ::before pseudo-element with grid pattern for better performance */

.section-header {
  text-align: center;
  margin-bottom: 60px;
  position: relative;
  z-index: 2;
}

.section-header h2 {
  font-size: 46px;
  font-weight: bold;
  margin-bottom: 12px;
  color: #333333;
  text-shadow: 1px 1px 3px rgba(138, 43, 226, 0.15);
  position: relative;
  display: inline-block;
  letter-spacing: 2px;
}

/* Removed h2::after pseudo-element for better performance */

.section-subtitle {
  font-size: 20px;
  color: #666666;
  letter-spacing: 3px;
  margin-top: 5px;
  font-weight: 500;
  /* Removed text-shadow for better performance */
}

.research-areas {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 40px;
  width: 100%;
  max-width: 1200px;
  position: relative;
  z-index: 2;
}

.research-area {
  background-color: rgba(255, 255, 255, 0.98);
  border-radius: 10px;
  padding: 35px;
  transition: all 0.3s;
  border: 1px solid rgba(5, 217, 232, 0.1);
  cursor: pointer;
  position: relative;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.06);
}

/* Removed ::before pseudo-element for better performance */

/* Removed ::after pseudo-element for better performance */

.research-area:hover {
  /* Simplified hover effect for better performance */
  border-color: rgba(5, 217, 232, 0.3);
  box-shadow: 0 8px 25px rgba(5, 217, 232, 0.1);
  transform: translateY(-5px);
}

.research-icon {
  font-size: 42px;
  color: #05d9e8;
  margin-bottom: 20px;
  /* Removed transition for better performance */
  /* Removed text-shadow for better performance */
}

.research-area:hover .research-icon {
  /* Removed transform for better performance */
  color: #ff2a6d;
  /* Removed text-shadow for better performance */
}

.research-title {
  font-size: 26px;
  font-weight: bold;
  margin-bottom: 15px;
  color: #333333;
  /* Removed transition for better performance */
  position: relative;
  display: inline-block;
  letter-spacing: 1px;
}

/* Removed ::after pseudo-element for better performance */

.research-area:hover .research-title {
  color: #05d9e8;
  /* Removed text-shadow for better performance */
}

/* Removed hover effect for title::after for better performance */

.research-description {
  font-size: 18px;
  line-height: 1.7;
  color: #666666;
  margin-bottom: 20px;
  flex: 1;
  /* Removed transition for better performance */
}

.research-area:hover .research-description {
  color: #444444;
}

.research-more {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 5px;
  font-size: 16px;
  color: #999999;
  /* Removed transition for better performance */
  margin-top: 10px;
  /* Always visible for better UX */
  opacity: 0.7;
  /* Removed transform for better performance */
}

.research-area:hover .research-more {
  opacity: 1;
  /* Removed transform for better performance */
  color: #ff2a6d;
  /* Removed text-shadow for better performance */
}

.section-button {
  margin-top: 60px;
  padding: 14px 35px;
  background-color: rgba(5, 217, 232, 0.1);
  color: #05d9e8;
  border: 1px solid rgba(5, 217, 232, 0.3);
  border-radius: 30px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s;
  position: relative;
  z-index: 2;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
  letter-spacing: 1px;
  /* Removed text-shadow for better performance */
}

/* Removed ::before pseudo-element for better performance */

.section-button:hover {
  background-color: rgba(5, 217, 232, 0.15);
  color: #05d9e8;
  transform: translateY(-3px);
  box-shadow: 0 6px 20px rgba(5, 217, 232, 0.2);
  border-color: rgba(5, 217, 232, 0.5);
  /* Removed text-shadow for better performance */
}

/* Removed hover effect for ::before for better performance */

/* 响应式设计 */
@media (max-width: 1200px) {
  .research-areas {
    grid-template-columns: repeat(2, 1fr);
  }

  .main-content {
    flex-direction: column;
    height: auto;
  }

  .visual-area {
    height: 50vh;
  }

  .info-area {
    width: 100%;
  }
}

@media (max-width: 768px) {
  .research-areas {
    grid-template-columns: 1fr;
  }

  .chinese-slogan {
    font-size: 3rem;
  }

  .english-slogan {
    font-size: 1.5rem;
  }

  .cyberpunk-elements {
    flex-direction: column;
    gap: 40px;
  }

  .page-indicator {
    display: none;
  }

  .home-section {
    height: auto;
    min-height: 100vh;
  }
}
</style>
