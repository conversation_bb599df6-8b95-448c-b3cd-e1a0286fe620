<template>
  <div class="lab-intro-container">
    <div class="hero-section">
      <div class="cyberpunk-grid"></div>
      <div class="cyberpunk-glitch"></div>
      <div class="hero-content" data-aos="fade-up">
        <h1 class="welcome-text">{{ currentLang === 'zh' ? '大家好，这里是' : 'Welcome to' }}</h1>
        <h1 class="institute-name" data-text="实验室门户网站简介！">{{ currentLang === 'zh' ? '实验室门户网站简介！' : 'Laboratory Portal Introduction!' }}</h1>
        <div class="hero-decoration">
          <div class="decoration-line"></div>
          <div class="decoration-circle"></div>
          <div class="decoration-line"></div>
        </div>
      </div>
    </div>

    <div class="content-section">
      <div class="sidebar">
        <div class="sidebar-item" :class="{ active: activeSection === 'overview' }" @click="scrollToSection('overview')">
          {{ currentLang === 'zh' ? '研究院概况' : 'Overview' }}
        </div>
        <div class="sidebar-item" :class="{ active: activeSection === 'foundation' }" @click="scrollToSection('foundation')">
          {{ currentLang === 'zh' ? '发展基础' : 'Foundation' }}
        </div>
        <div class="sidebar-item" :class="{ active: activeSection === 'content' }" @click="scrollToSection('content')">
          {{ currentLang === 'zh' ? '建设内容' : 'Content' }}
        </div>
        <div class="sidebar-item" :class="{ active: activeSection === 'goals' }" @click="scrollToSection('goals')">
          {{ currentLang === 'zh' ? '建设目标' : 'Goals' }}
        </div>
      </div>

      <div class="main-content">
        <div id="overview" class="content-block">
          <h2>{{ currentLang === 'zh' ? '门户网站概况' : 'Portal Overview' }}</h2>
          <p>{{ currentLang === 'zh' ? '人工智能正在深刻改变社会发展模式，本实验室门户网站（以下简称"门户"）旨在为科研人员和学生提供一个交流与学习的平台。' : 'Artificial Intelligence is profoundly changing the mode of social development, and this Laboratory Portal (hereinafter referred to as the "Portal") aims to provide a platform for researchers and students to communicate and learn.' }}</p>
          <p>{{ currentLang === 'zh' ? '门户网站作为实验室的线上窗口，整合了丰富的学术资源，提供最新的研究动态、学术成果展示以及交流互动的功能，致力于促进科研创新和知识共享。' : 'As the online window of the laboratory, the portal integrates rich academic resources, provides the latest research dynamics, academic achievement display and communication interaction functions, and is committed to promoting scientific research innovation and knowledge sharing.' }}</p>
        </div>

        <div id="foundation" class="content-block">
          <h2>{{ currentLang === 'zh' ? '发展基础' : 'Development Foundation' }}</h2>
          <p>{{ currentLang === 'zh' ? '本实验室在人工智能领域拥有深厚的研究基础和丰富的人才资源。实验室拥有先进的研究设备和技术平台，涵盖机器学习、计算机视觉、自然语言处理等多个人工智能核心领域。' : 'Our laboratory has a solid research foundation and rich talent resources in the field of artificial intelligence. The laboratory has advanced research equipment and technical platforms, covering multiple core areas of artificial intelligence such as machine learning, computer vision, natural language processing, etc.' }}</p>
          <p>{{ currentLang === 'zh' ? '门户网站依托实验室强大的学科优势和科研实力，整合各方资源，打造具有广泛影响力的人工智能研究与交流平台。' : 'The portal relies on the laboratory\'s strong disciplinary advantages and scientific research strength, integrates various resources, and builds an artificial intelligence research and communication platform with wide influence.' }}</p>
        </div>

        <div id="content" class="content-block">
          <h2>{{ currentLang === 'zh' ? '建设内容' : 'Construction Content' }}</h2>
          <p>{{ currentLang === 'zh' ? '门户网站重点提供学术资源共享、研究成果展示、在线交流互动等功能，涵盖智能感知、机器学习、知识工程、人机交互等多个研究方向的内容。' : 'The portal focuses on providing academic resource sharing, research result display, online communication and interaction functions, covering content in multiple research directions such as intelligent perception, machine learning, knowledge engineering, human-computer interaction, etc.' }}</p>
          <p>{{ currentLang === 'zh' ? '同时，门户网站积极促进学术交流，为研究人员和学生提供一个开放的交流平台，共同推动人工智能技术创新和知识传播。' : 'At the same time, the portal actively promotes academic exchanges, provides an open communication platform for researchers and students, and jointly promotes technological innovation and knowledge dissemination of artificial intelligence.' }}</p>
        </div>

        <div id="goals" class="content-block">
          <h2>{{ currentLang === 'zh' ? '建设目标' : 'Construction Goals' }}</h2>
          <p>{{ currentLang === 'zh' ? '门户网站致力于打造一流的学术交流平台，促进科研合作与知识共享，展示最新研究成果，推动人工智能技术在各领域的创新应用。' : 'The portal is committed to building a first-class academic exchange platform, promoting scientific research cooperation and knowledge sharing, displaying the latest research results, and promoting innovative applications of artificial intelligence technology in various fields.' }}</p>
          <p>{{ currentLang === 'zh' ? '未来，门户网站将不断完善功能，提升用户体验，扩大影响力，成为人工智能领域重要的学术交流与资源共享平台，为科技创新和人才培养提供有力支持。' : 'In the future, the portal will continuously improve its functions, enhance user experience, expand its influence, and become an important academic exchange and resource sharing platform in the field of artificial intelligence, providing strong support for technological innovation and talent cultivation.' }}</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, computed } from 'vue';

// Language state
const currentLang = ref(localStorage.getItem('language') || 'zh');

// Active section tracking
const activeSection = ref('overview');
const sections = ['overview', 'foundation', 'content', 'goals'];
const sectionElements = ref([]);

// Scroll to section
const scrollToSection = (sectionId) => {
  const element = document.getElementById(sectionId);
  if (element) {
    element.scrollIntoView({ behavior: 'smooth' });
    activeSection.value = sectionId;
  }
};

// Handle scroll to update active section
const handleScroll = () => {
  const scrollPosition = window.scrollY + 100; // Add offset for better UX

  for (let i = sectionElements.value.length - 1; i >= 0; i--) {
    const section = sectionElements.value[i];
    if (section && scrollPosition >= section.offsetTop) {
      activeSection.value = sections[i];
      break;
    }
  }
};

// Setup scroll listener
onMounted(() => {
  // Get section elements
  sectionElements.value = sections.map(id => document.getElementById(id));

  // Add scroll event listener
  window.addEventListener('scroll', handleScroll);

  // Check for language changes
  window.addEventListener('storage', (event) => {
    if (event.key === 'language') {
      currentLang.value = event.newValue;
    }
  });

  // Initial check for active section
  handleScroll();
});

// Clean up
onUnmounted(() => {
  window.removeEventListener('scroll', handleScroll);
});
</script>

<style scoped>
.lab-intro-container {
  min-height: 100vh;
  background-color: #f8f9fa;
  color: #333333;
  padding-top: 60px; /* Space for fixed navigation */
}

.hero-section {
  height: 60vh;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding: 0 20px;
  overflow: hidden;
}

.hero-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: url('https://images.unsplash.com/photo-1635830625698-3b9bd74671ca?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1920&q=80');
  background-size: cover;
  background-position: center;
  filter: brightness(0.7) saturate(1.5) hue-rotate(-10deg);
  z-index: 1;
  animation: backgroundScale 20s infinite alternate ease-in-out;
}

.hero-section::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.7) 0%, rgba(255, 255, 255, 0.5) 100%);
  z-index: 2;
}

.cyberpunk-grid {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image:
    linear-gradient(rgba(0, 123, 255, 0.15) 1px, transparent 1px),
    linear-gradient(90deg, rgba(0, 123, 255, 0.15) 1px, transparent 1px);
  background-size: 40px 40px;
  z-index: 3;
  animation: gridPulse 10s infinite alternate ease-in-out;
}

.cyberpunk-glitch {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: transparent;
  z-index: 4;
  pointer-events: none;
  overflow: hidden;
}

.cyberpunk-glitch::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(13, 110, 253, 0.2), transparent);
  animation: glitchSweep 10s infinite;
}

@keyframes glitchSweep {
  0%, 100% {
    left: -100%;
    opacity: 0;
  }
  10%, 90% {
    left: -100%;
    opacity: 0;
  }
  50% {
    left: 100%;
    opacity: 1;
  }
}

@keyframes gridPulse {
  0% {
    opacity: 0.3;
    background-size: 40px 40px;
  }
  50% {
    opacity: 0.6;
    background-size: 42px 42px;
  }
  100% {
    opacity: 0.3;
    background-size: 40px 40px;
  }
}

@keyframes backgroundScale {
  0% {
    transform: scale(1.05);
    filter: brightness(0.7) saturate(1.5) hue-rotate(-10deg);
  }
  100% {
    transform: scale(1.1);
    filter: brightness(0.8) saturate(1.7) hue-rotate(0deg);
  }
}

.hero-content {
  max-width: 800px;
  position: relative;
  z-index: 5;
}

.welcome-text {
  font-size: 2rem;
  margin-bottom: 10px;
  color: #0d6efd;
  line-height: 1.2;
  text-shadow: 0 0 10px rgba(13, 110, 253, 0.7);
  letter-spacing: 2px;
}

.institute-name {
  font-size: 2.8rem;
  font-weight: bold;
  margin-bottom: 20px;
  color: #212529;
  line-height: 1.2;
  text-shadow: 0 0 15px rgba(13, 110, 253, 0.5);
  position: relative;
  letter-spacing: 2px;
  animation: textGlow 3s infinite alternate;
}

.institute-name::after {
  content: attr(data-text);
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
  color: transparent;
  text-shadow: 0 0 10px rgba(13, 110, 253, 0.6);
  opacity: 0;
  animation: textGlitch 8s infinite;
}

@keyframes textGlow {
  0% {
    text-shadow: 0 0 15px rgba(13, 110, 253, 0.5), 0 0 10px rgba(13, 110, 253, 0.4);
  }
  100% {
    text-shadow: 0 0 20px rgba(13, 110, 253, 0.7), 0 0 30px rgba(13, 110, 253, 0.6), 0 0 40px rgba(13, 110, 253, 0.3);
  }
}

@keyframes textGlitch {
  0%, 90%, 100% {
    opacity: 0;
    transform: translateX(0);
  }
  92%, 94%, 96% {
    opacity: 0.8;
    transform: translateX(2px);
  }
  93%, 95%, 97% {
    opacity: 0.8;
    transform: translateX(-2px);
  }
}

.hero-decoration {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 30px;
}

.decoration-line {
  width: 80px;
  height: 1px;
  background: linear-gradient(90deg, transparent, #0d6efd, transparent);
}

.decoration-circle {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background-color: #0d6efd;
  margin: 0 10px;
  box-shadow: 0 0 10px rgba(13, 110, 253, 0.8);
  animation: pulsate 2s infinite alternate;
}

@keyframes pulsate {
  0% {
    transform: scale(1);
    opacity: 0.7;
  }
  100% {
    transform: scale(1.2);
    opacity: 1;
  }
}

.content-section {
  display: flex;
  max-width: 1200px;
  margin: 0 auto;
  padding: 40px 20px;
  position: relative;
}

.content-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image:
    linear-gradient(rgba(13, 110, 253, 0.05) 1px, transparent 1px),
    linear-gradient(90deg, rgba(13, 110, 253, 0.05) 1px, transparent 1px);
  background-size: 20px 20px;
  z-index: 0;
  opacity: 0.5;
}

.sidebar {
  width: 250px;
  position: sticky;
  top: 100px;
  height: fit-content;
  margin-right: 40px;
  z-index: 1;
}

.sidebar-item {
  padding: 15px 20px;
  margin-bottom: 15px;
  background-color: rgba(255, 255, 255, 0.9);
  border-left: 3px solid transparent;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.sidebar-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 3px;
  height: 100%;
  background: linear-gradient(to bottom, #0d6efd, #6610f2);
  opacity: 0;
  transition: opacity 0.3s;
}

.sidebar-item:hover {
  background-color: rgba(248, 249, 250, 0.9);
  transform: translateX(5px);
}

.sidebar-item:hover::before {
  opacity: 1;
}

.sidebar-item.active {
  background-color: rgba(248, 249, 250, 0.9);
  color: #212529;
  border-left-color: #0d6efd;
  box-shadow: 0 0 15px rgba(13, 110, 253, 0.3);
}

.sidebar-item.active::after {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 5px;
  height: 100%;
  background: linear-gradient(to bottom, transparent, #0d6efd, transparent);
}

.main-content {
  flex: 1;
  position: relative;
  z-index: 1;
}

.content-block {
  margin-bottom: 60px;
  padding: 25px;
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 8px;
  border: 1px solid rgba(13, 110, 253, 0.2);
  transition: all 0.3s;
  position: relative;
  overflow: hidden;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
  scroll-margin-top: 100px; /* For smooth scrolling */
}

.content-block:hover {
  border-color: rgba(13, 110, 253, 0.5);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15), 0 0 15px rgba(13, 110, 253, 0.2);
}

.content-block h2 {
  font-size: 1.8rem;
  margin-bottom: 20px;
  color: #0d6efd;
  position: relative;
  display: inline-block;
  text-shadow: 0 0 10px rgba(13, 110, 253, 0.3);
  border-bottom: 2px solid rgba(13, 110, 253, 0.3);
  padding-bottom: 10px;
}

.content-block p {
  font-size: 1.1rem;
  line-height: 1.6;
  margin-bottom: 20px;
  color: #495057;
  position: relative;
  padding-left: 15px;
}

.content-block p::before {
  content: '';
  position: absolute;
  left: 0;
  top: 8px;
  width: 5px;
  height: 5px;
  background-color: #0d6efd;
  border-radius: 50%;
  box-shadow: 0 0 5px rgba(13, 110, 253, 0.7);
}

/* Responsive styles */
@media (max-width: 768px) {
  .content-section {
    flex-direction: column;
  }

  .sidebar {
    width: 100%;
    position: static;
    margin-right: 0;
    margin-bottom: 30px;
  }

  .welcome-text {
    font-size: 1.5rem;
  }

  .institute-name {
    font-size: 2rem;
  }

  .hero-decoration {
    margin-top: 20px;
  }

  .decoration-line {
    width: 50px;
  }

  .content-block {
    padding: 15px;
  }

  .content-block h2 {
    font-size: 1.5rem;
  }

  .content-block p {
    font-size: 1rem;
  }
}

/* Animation for content blocks on scroll */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.content-block {
  animation: fadeInUp 0.6s ease-out forwards;
  opacity: 0;
}

.content-block:nth-child(1) {
  animation-delay: 0.2s;
}

.content-block:nth-child(2) {
  animation-delay: 0.4s;
}

.content-block:nth-child(3) {
  animation-delay: 0.6s;
}

.content-block:nth-child(4) {
  animation-delay: 0.8s;
}
</style>
